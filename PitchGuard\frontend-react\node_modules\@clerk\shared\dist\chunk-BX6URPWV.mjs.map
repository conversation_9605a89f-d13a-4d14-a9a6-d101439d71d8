{"version": 3, "sources": ["../src/utils/instance.ts", "../src/url.ts"], "sourcesContent": ["/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n", "import { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, pkgVersion?: string) => {\n  if (!pkgVersion && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!pkgVersion) {\n    return 'latest';\n  }\n\n  return pkgVersion.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (\n  frontendApi: string,\n  { pkgVersion = CLERK_JS_PACKAGE_VERSION, clerkJSVersion }: { pkgVersion?: string; clerkJSVersion?: string },\n) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, pkgVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n"], "mappings": ";AAGO,SAAS,UAAU,aAA8B;AACtD,SACE,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,iBAAiB,KACtC,YAAY,SAAS,oBAAoB;AAE7C;;;ACRO,SAAS,kBAAkB,cAAc,IAAqB;AACnE,MAAI,YAAY,WAAW,GAAG,GAAG;AAC/B,kBAAc,YAAY,MAAM,CAAC;AAAA,EACnC;AACA,SAAO,IAAI,gBAAgB,WAAW;AACxC;AAEO,SAAS,YAAY,MAAM,IAAY;AAC5C,UAAQ,OAAO,IAAI,QAAQ,YAAY,EAAE;AAC3C;AAEO,SAAS,eAAe,KAAyB;AACtD,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,IAAI,MAAM,iBAAiB,GAAG;AAChC,YAAQ;AAAA,EACV,WAAW,IAAI,MAAM,kBAAkB,GAAG;AACxC,WAAO;AAAA,EACT,OAAO;AACL,YAAQ;AAAA,EACV;AAEA,QAAM,WAAW,IAAI,QAAQ,OAAO,EAAE;AACtC,SAAO,SAAS,QAAQ;AAC1B;AAQO,IAAM,8BAA8B,CAAC,aAAqB,eAAwB;AACvF,MAAI,CAAC,cAAc,UAAU,WAAW,GAAG;AACzC,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AAEA,SAAO,WAAW,MAAM,GAAG,EAAE,CAAC,KAAK;AACrC;AAOO,IAAM,eAAe,CAC1B,aACA,EAAE,aAAa,UAA0B,eAAe,MACrD;AACH,QAAM,sBAAsB,YAAY,QAAQ,iBAAiB,EAAE;AACnE,QAAM,QAAQ,4BAA4B,aAAa,UAAU;AACjE,SAAO,WAAW,mBAAmB,wBAAwB,kBAAkB,KAAK;AACtF;", "names": []}