{"version": 3, "sources": ["../src/devBrowser.ts"], "sourcesContent": ["export const DEV_BROWSER_SSO_JWT_PARAMETER = '__dev_session';\nexport const DEV_BROWSER_JWT_MARKER = '__clerk_db_jwt';\nconst DEV_BROWSER_JWT_MARKER_REGEXP = /__clerk_db_jwt\\[(.*)\\]/;\n\n// Sets the dev_browser JWT in the hash or the search\nexport function setDevBrowserJWTInURL(url: URL, jwt: string, opts = { hash: true }): URL {\n  const resultURL = new URL(url);\n\n  const jwtFromHash = extractDevBrowserJWTFromURLHash(resultURL);\n  const jwtFromSearch = extractDevBrowserJWTFromURLSearchParams(resultURL);\n  // Existing jwt takes precedence\n  const jwtToSet = jwtFromHash || jwtFromSearch || jwt;\n\n  if (jwtToSet) {\n    resultURL.searchParams.append(DEV_BROWSER_SSO_JWT_PARAMETER, jwtToSet);\n    resultURL.searchParams.append(DEV_BROWSER_JWT_MARKER, jwtToSet);\n    if (opts.hash) {\n      resultURL.hash = resultURL.hash + `${DEV_BROWSER_JWT_MARKER}[${jwtToSet}]`;\n    }\n  }\n\n  return resultURL;\n}\n\nfunction extractDevBrowserJWTFromHash(hash: string): string {\n  const matches = hash.match(DEV_BROWSER_JWT_MARKER_REGEXP);\n  return matches ? matches[1] : '';\n}\n\n/**\n * Extract & strip existing jwt from hash\n * Side effect: Removes dev browser from the url hash\n **/\nexport function extractDevBrowserJWTFromURLHash(url: URL) {\n  const jwt = extractDevBrowserJWTFromHash(url.hash);\n  url.hash = url.hash.replace(DEV_BROWSER_JWT_MARKER_REGEXP, '');\n  if (url.href.endsWith('#')) {\n    url.hash = '';\n  }\n\n  return jwt;\n}\n\n/**\n * Extract & strip existing jwt from search params\n * Side effect: Removes dev browser from the search params\n **/\nexport function extractDevBrowserJWTFromURLSearchParams(url: URL) {\n  const jwtFromDevSession = url.searchParams.get(DEV_BROWSER_SSO_JWT_PARAMETER);\n  url.searchParams.delete(DEV_BROWSER_SSO_JWT_PARAMETER);\n\n  const jwtFromClerkDbJwt = url.searchParams.get(DEV_BROWSER_JWT_MARKER);\n  url.searchParams.delete(DEV_BROWSER_JWT_MARKER);\n\n  return jwtFromDevSession || jwtFromClerkDbJwt || '';\n}\n"], "mappings": ";AAAO,IAAM,gCAAgC;AACtC,IAAM,yBAAyB;AACtC,IAAM,gCAAgC;AAG/B,SAAS,sBAAsB,KAAU,KAAa,OAAO,EAAE,MAAM,KAAK,GAAQ;AACvF,QAAM,YAAY,IAAI,IAAI,GAAG;AAE7B,QAAM,cAAc,gCAAgC,SAAS;AAC7D,QAAM,gBAAgB,wCAAwC,SAAS;AAEvE,QAAM,WAAW,eAAe,iBAAiB;AAEjD,MAAI,UAAU;AACZ,cAAU,aAAa,OAAO,+BAA+B,QAAQ;AACrE,cAAU,aAAa,OAAO,wBAAwB,QAAQ;AAC9D,QAAI,KAAK,MAAM;AACb,gBAAU,OAAO,UAAU,OAAO,GAAG,sBAAsB,IAAI,QAAQ;AAAA,IACzE;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,6BAA6B,MAAsB;AAC1D,QAAM,UAAU,KAAK,MAAM,6BAA6B;AACxD,SAAO,UAAU,QAAQ,CAAC,IAAI;AAChC;AAMO,SAAS,gCAAgC,KAAU;AACxD,QAAM,MAAM,6BAA6B,IAAI,IAAI;AACjD,MAAI,OAAO,IAAI,KAAK,QAAQ,+BAA+B,EAAE;AAC7D,MAAI,IAAI,KAAK,SAAS,GAAG,GAAG;AAC1B,QAAI,OAAO;AAAA,EACb;AAEA,SAAO;AACT;AAMO,SAAS,wCAAwC,KAAU;AAChE,QAAM,oBAAoB,IAAI,aAAa,IAAI,6BAA6B;AAC5E,MAAI,aAAa,OAAO,6BAA6B;AAErD,QAAM,oBAAoB,IAAI,aAAa,IAAI,sBAAsB;AACrE,MAAI,aAAa,OAAO,sBAAsB;AAE9C,SAAO,qBAAqB,qBAAqB;AACnD;", "names": []}