{"version": 3, "sources": ["../../tween-functions/index.js", "../../react-confetti/src/utils.ts", "../../react-confetti/src/Particle.ts", "../../react-confetti/src/ParticleGenerator.ts", "../../react-confetti/src/Confetti.ts", "../../react-confetti/src/ReactConfetti.tsx"], "sourcesContent": ["'use strict';\n\n// t: current time, b: beginning value, _c: final value, d: total duration\nvar tweenFunctions = {\n  linear: function(t, b, _c, d) {\n    var c = _c - b;\n    return c * t / d + b;\n  },\n  easeInQuad: function(t, b, _c, d) {\n    var c = _c - b;\n    return c * (t /= d) * t + b;\n  },\n  easeOutQuad: function(t, b, _c, d) {\n    var c = _c - b;\n    return -c * (t /= d) * (t - 2) + b;\n  },\n  easeInOutQuad: function(t, b, _c, d) {\n    var c = _c - b;\n    if ((t /= d / 2) < 1) {\n      return c / 2 * t * t + b;\n    } else {\n      return -c / 2 * ((--t) * (t - 2) - 1) + b;\n    }\n  },\n  easeInCubic: function(t, b, _c, d) {\n    var c = _c - b;\n    return c * (t /= d) * t * t + b;\n  },\n  easeOutCubic: function(t, b, _c, d) {\n    var c = _c - b;\n    return c * ((t = t / d - 1) * t * t + 1) + b;\n  },\n  easeInOutCubic: function(t, b, _c, d) {\n    var c = _c - b;\n    if ((t /= d / 2) < 1) {\n      return c / 2 * t * t * t + b;\n    } else {\n      return c / 2 * ((t -= 2) * t * t + 2) + b;\n    }\n  },\n  easeInQuart: function(t, b, _c, d) {\n    var c = _c - b;\n    return c * (t /= d) * t * t * t + b;\n  },\n  easeOutQuart: function(t, b, _c, d) {\n    var c = _c - b;\n    return -c * ((t = t / d - 1) * t * t * t - 1) + b;\n  },\n  easeInOutQuart: function(t, b, _c, d) {\n    var c = _c - b;\n    if ((t /= d / 2) < 1) {\n      return c / 2 * t * t * t * t + b;\n    } else {\n      return -c / 2 * ((t -= 2) * t * t * t - 2) + b;\n    }\n  },\n  easeInQuint: function(t, b, _c, d) {\n    var c = _c - b;\n    return c * (t /= d) * t * t * t * t + b;\n  },\n  easeOutQuint: function(t, b, _c, d) {\n    var c = _c - b;\n    return c * ((t = t / d - 1) * t * t * t * t + 1) + b;\n  },\n  easeInOutQuint: function(t, b, _c, d) {\n    var c = _c - b;\n    if ((t /= d / 2) < 1) {\n      return c / 2 * t * t * t * t * t + b;\n    } else {\n      return c / 2 * ((t -= 2) * t * t * t * t + 2) + b;\n    }\n  },\n  easeInSine: function(t, b, _c, d) {\n    var c = _c - b;\n    return -c * Math.cos(t / d * (Math.PI / 2)) + c + b;\n  },\n  easeOutSine: function(t, b, _c, d) {\n    var c = _c - b;\n    return c * Math.sin(t / d * (Math.PI / 2)) + b;\n  },\n  easeInOutSine: function(t, b, _c, d) {\n    var c = _c - b;\n    return -c / 2 * (Math.cos(Math.PI * t / d) - 1) + b;\n  },\n  easeInExpo: function(t, b, _c, d) {\n    var c = _c - b;\n    return (t==0) ? b : c * Math.pow(2, 10 * (t/d - 1)) + b;\n  },\n  easeOutExpo: function(t, b, _c, d) {\n    var c = _c - b;\n    return (t==d) ? b+c : c * (-Math.pow(2, -10 * t/d) + 1) + b;\n  },\n  easeInOutExpo: function(t, b, _c, d) {\n    var c = _c - b;\n    if (t === 0) {\n      return b;\n    }\n    if (t === d) {\n      return b + c;\n    }\n    if ((t /= d / 2) < 1) {\n      return c / 2 * Math.pow(2, 10 * (t - 1)) + b;\n    } else {\n      return c / 2 * (-Math.pow(2, -10 * --t) + 2) + b;\n    }\n  },\n  easeInCirc: function(t, b, _c, d) {\n    var c = _c - b;\n    return -c * (Math.sqrt(1 - (t /= d) * t) - 1) + b;\n  },\n  easeOutCirc: function(t, b, _c, d) {\n    var c = _c - b;\n    return c * Math.sqrt(1 - (t = t / d - 1) * t) + b;\n  },\n  easeInOutCirc: function(t, b, _c, d) {\n    var c = _c - b;\n    if ((t /= d / 2) < 1) {\n      return -c / 2 * (Math.sqrt(1 - t * t) - 1) + b;\n    } else {\n      return c / 2 * (Math.sqrt(1 - (t -= 2) * t) + 1) + b;\n    }\n  },\n  easeInElastic: function(t, b, _c, d) {\n    var c = _c - b;\n    var a, p, s;\n    s = 1.70158;\n    p = 0;\n    a = c;\n    if (t === 0) {\n      return b;\n    } else if ((t /= d) === 1) {\n      return b + c;\n    }\n    if (!p) {\n      p = d * 0.3;\n    }\n    if (a < Math.abs(c)) {\n      a = c;\n      s = p / 4;\n    } else {\n      s = p / (2 * Math.PI) * Math.asin(c / a);\n    }\n    return -(a * Math.pow(2, 10 * (t -= 1)) * Math.sin((t * d - s) * (2 * Math.PI) / p)) + b;\n  },\n  easeOutElastic: function(t, b, _c, d) {\n    var c = _c - b;\n    var a, p, s;\n    s = 1.70158;\n    p = 0;\n    a = c;\n    if (t === 0) {\n      return b;\n    } else if ((t /= d) === 1) {\n      return b + c;\n    }\n    if (!p) {\n      p = d * 0.3;\n    }\n    if (a < Math.abs(c)) {\n      a = c;\n      s = p / 4;\n    } else {\n      s = p / (2 * Math.PI) * Math.asin(c / a);\n    }\n    return a * Math.pow(2, -10 * t) * Math.sin((t * d - s) * (2 * Math.PI) / p) + c + b;\n  },\n  easeInOutElastic: function(t, b, _c, d) {\n    var c = _c - b;\n    var a, p, s;\n    s = 1.70158;\n    p = 0;\n    a = c;\n    if (t === 0) {\n      return b;\n    } else if ((t /= d / 2) === 2) {\n      return b + c;\n    }\n    if (!p) {\n      p = d * (0.3 * 1.5);\n    }\n    if (a < Math.abs(c)) {\n      a = c;\n      s = p / 4;\n    } else {\n      s = p / (2 * Math.PI) * Math.asin(c / a);\n    }\n    if (t < 1) {\n      return -0.5 * (a * Math.pow(2, 10 * (t -= 1)) * Math.sin((t * d - s) * (2 * Math.PI) / p)) + b;\n    } else {\n      return a * Math.pow(2, -10 * (t -= 1)) * Math.sin((t * d - s) * (2 * Math.PI) / p) * 0.5 + c + b;\n    }\n  },\n  easeInBack: function(t, b, _c, d, s) {\n    var c = _c - b;\n    if (s === void 0) {\n      s = 1.70158;\n    }\n    return c * (t /= d) * t * ((s + 1) * t - s) + b;\n  },\n  easeOutBack: function(t, b, _c, d, s) {\n    var c = _c - b;\n    if (s === void 0) {\n      s = 1.70158;\n    }\n    return c * ((t = t / d - 1) * t * ((s + 1) * t + s) + 1) + b;\n  },\n  easeInOutBack: function(t, b, _c, d, s) {\n    var c = _c - b;\n    if (s === void 0) {\n      s = 1.70158;\n    }\n    if ((t /= d / 2) < 1) {\n      return c / 2 * (t * t * (((s *= 1.525) + 1) * t - s)) + b;\n    } else {\n      return c / 2 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2) + b;\n    }\n  },\n  easeInBounce: function(t, b, _c, d) {\n    var c = _c - b;\n    var v;\n    v = tweenFunctions.easeOutBounce(d - t, 0, c, d);\n    return c - v + b;\n  },\n  easeOutBounce: function(t, b, _c, d) {\n    var c = _c - b;\n    if ((t /= d) < 1 / 2.75) {\n      return c * (7.5625 * t * t) + b;\n    } else if (t < 2 / 2.75) {\n      return c * (7.5625 * (t -= 1.5 / 2.75) * t + 0.75) + b;\n    } else if (t < 2.5 / 2.75) {\n      return c * (7.5625 * (t -= 2.25 / 2.75) * t + 0.9375) + b;\n    } else {\n      return c * (7.5625 * (t -= 2.625 / 2.75) * t + 0.984375) + b;\n    }\n  },\n  easeInOutBounce: function(t, b, _c, d) {\n    var c = _c - b;\n    var v;\n    if (t < d / 2) {\n      v = tweenFunctions.easeInBounce(t * 2, 0, c, d);\n      return v * 0.5 + b;\n    } else {\n      v = tweenFunctions.easeOutBounce(t * 2 - d, 0, c, d);\n      return v * 0.5 + c * 0.5 + b;\n    }\n  }\n};\n\nmodule.exports = tweenFunctions;\n", "import { ICircle } from './Circle'\nimport { IPoint } from './Point'\nimport { IRect } from './Rect'\n\nexport function norm(value: number, min: number, max: number) {\n  return (value - min) / (max - min)\n}\n\nexport function lerp(lnorm: number, min: number, max: number) {\n  return (max - min) * lnorm + min\n}\n\nexport function map(\n  value: number,\n  sourceMin: number,\n  sourceMax: number,\n  destMin: number,\n  destMax: number,\n) {\n  return lerp(norm(value, sourceMin, sourceMax), destMin, destMax)\n}\n\nexport function clamp(value: number, min: number, max: number) {\n  return Math.min(Math.max(value, Math.min(min, max)), Math.max(min, max))\n}\n\nexport function distance(p0: IPoint, p1: IPoint) {\n  const dx = p1.x - p0.x\n  const dy = p1.y - p0.y\n  return Math.sqrt(dx * dx + dy * dy)\n}\n\nexport function distanceXY(x0: number, y0: number, x1: number, y1: number) {\n  const dx = x1 - x0\n  const dy = y1 - y0\n  return Math.sqrt(dx * dx + dy * dy)\n}\n\nexport function circleCollision(c0: ICircle, c1: ICircle) {\n  return distance(c0, c1) <= c0.radius + c1.radius\n}\n\nexport function circlePointCollision(x: number, y: number, circle: ICircle) {\n  return distanceXY(x, y, circle.x, circle.y) < circle.radius\n}\n\nexport function inRange(value: number, min: number, max: number) {\n  return value >= Math.min(min, max) && value <= Math.max(min, max)\n}\n\nexport function pointInRect(p: IPoint, rect: IRect) {\n  return (\n    inRange(p.x, rect.x, rect.x + rect.w) &&\n    inRange(p.y, rect.y, rect.y + rect.h)\n  )\n}\n\nexport function rangeIntersect(\n  min0: number,\n  max0: number,\n  min1: number,\n  max1: number,\n) {\n  return (\n    Math.max(min0, max0) >= Math.min(min1, max1) &&\n    Math.min(min0, max0) <= Math.max(min1, max1)\n  )\n}\n\nexport function rectIntersect(r0: IRect, r1: IRect) {\n  return (\n    rangeIntersect(r0.x, r0.x + r0.w, r1.x, r1.x + r1.w) &&\n    rangeIntersect(r0.y, r0.y + r0.h, r1.y, r1.y + r1.h)\n  )\n}\n\nexport function degreesToRads(degrees: number) {\n  return (degrees * Math.PI) / 180\n}\n\nexport function radsToDegrees(radians: number) {\n  return (radians * 180) / Math.PI\n}\n\nexport function randomRange(min: number, max: number) {\n  return min + Math.random() * (max - min)\n}\n\nexport function randomInt(min: number, max: number) {\n  return Math.floor(min + Math.random() * (max - min + 1))\n}\n", "import { IConfettiOptions } from './Confetti'\nimport { degreesToRads, randomInt, randomRange } from './utils'\n\nexport enum ParticleShape {\n  Circle = 0,\n  Square = 1,\n  Strip = 2,\n}\n\nenum RotationDirection {\n  Positive = 1,\n  Negative = -1,\n}\n\nconst DEFAULT_FRAME_TIME = 1000 / 60\n\nexport default class Particle {\n  constructor(\n    context: CanvasRenderingContext2D,\n    getOptions: () => IConfettiOptions,\n    x: number,\n    y: number,\n  ) {\n    this.getOptions = getOptions\n    const { colors, initialVelocityX, initialVelocityY } = this.getOptions()\n    this.context = context\n    this.x = x\n    this.y = y\n    this.w = randomRange(5, 20)\n    this.h = randomRange(5, 20)\n    this.radius = randomRange(5, 10)\n    this.vx =\n      typeof initialVelocityX === 'number'\n        ? randomRange(-initialVelocityX, initialVelocityX)\n        : randomRange(initialVelocityX.min, initialVelocityX.max)\n    this.vy =\n      typeof initialVelocityY === 'number'\n        ? randomRange(-initialVelocityY, 0)\n        : randomRange(initialVelocityY.min, initialVelocityY.max)\n    this.shape = randomInt(0, 2)\n    this.angle = degreesToRads(randomRange(0, 360))\n    this.angularSpin = randomRange(-0.2, 0.2)\n    this.color = colors[Math.floor(Math.random() * colors.length)]\n    this.rotateY = randomRange(0, 1)\n    this.rotationDirection = randomRange(0, 1)\n      ? RotationDirection.Positive\n      : RotationDirection.Negative\n  }\n\n  context: CanvasRenderingContext2D\n\n  radius: number\n\n  x: number\n\n  y: number\n\n  w: number\n\n  h: number\n\n  vx: number\n\n  vy: number\n\n  shape: ParticleShape\n\n  angle: number\n\n  angularSpin: number\n\n  color: string\n\n  // Actually used as scaleY to simulate rotation cheaply\n  rotateY: number\n\n  rotationDirection: RotationDirection\n\n  getOptions: () => IConfettiOptions\n\n  update(elapsed: number) {\n    const { gravity, wind, friction, opacity, drawShape } = this.getOptions()\n    const frameTimeMultiplier = elapsed / DEFAULT_FRAME_TIME\n    this.x += this.vx * frameTimeMultiplier\n    this.y += this.vy * frameTimeMultiplier\n    this.vy += gravity * frameTimeMultiplier\n    this.vx += wind * frameTimeMultiplier\n    this.vx *= friction ** frameTimeMultiplier\n    this.vy *= friction ** frameTimeMultiplier\n    if (\n      this.rotateY >= 1 &&\n      this.rotationDirection === RotationDirection.Positive\n    ) {\n      this.rotationDirection = RotationDirection.Negative\n    } else if (\n      this.rotateY <= -1 &&\n      this.rotationDirection === RotationDirection.Negative\n    ) {\n      this.rotationDirection = RotationDirection.Positive\n    }\n\n    const rotateDelta = 0.1 * this.rotationDirection * frameTimeMultiplier\n\n    this.rotateY += rotateDelta\n    this.angle += this.angularSpin\n    this.context.save()\n    this.context.translate(this.x, this.y)\n    this.context.rotate(this.angle)\n    this.context.scale(1, this.rotateY)\n    this.context.rotate(this.angle)\n    this.context.beginPath()\n    this.context.fillStyle = this.color\n    this.context.strokeStyle = this.color\n    this.context.globalAlpha = opacity\n    this.context.lineCap = 'round'\n    this.context.lineWidth = 2\n    if (drawShape && typeof drawShape === 'function') {\n      drawShape.call(this, this.context)\n    } else {\n      switch (this.shape) {\n        case ParticleShape.Circle: {\n          this.context.beginPath()\n          this.context.arc(0, 0, this.radius, 0, 2 * Math.PI)\n          this.context.fill()\n          break\n        }\n        case ParticleShape.Square: {\n          this.context.fillRect(-this.w / 2, -this.h / 2, this.w, this.h)\n          break\n        }\n        case ParticleShape.Strip: {\n          this.context.fillRect(-this.w / 6, -this.h / 2, this.w / 3, this.h)\n          break\n        }\n      }\n    }\n    this.context.closePath()\n    this.context.restore()\n  }\n}\n", "import { IConfettiOptions } from './Confetti'\nimport Particle from './Particle'\nimport { IRect } from './Rect'\nimport { randomRange } from './utils'\n\nexport interface IParticleGenerator extends IRect {\n  removeParticleAt: (index: number) => void\n  getParticle: () => void\n  animate: (elapsed: number) => boolean\n  particles: Particle[]\n  particlesGenerated: number\n}\n\nexport default class ParticleGenerator implements IParticleGenerator {\n  constructor(canvas: HTMLCanvasElement, getOptions: () => IConfettiOptions) {\n    this.canvas = canvas\n    const ctx = this.canvas.getContext('2d')\n    if (!ctx) {\n      throw new Error('Could not get canvas context')\n    }\n    this.context = ctx\n    this.getOptions = getOptions\n  }\n\n  canvas: HTMLCanvasElement\n\n  context: CanvasRenderingContext2D\n\n  getOptions: () => IConfettiOptions\n\n  x = 0\n\n  y = 0\n\n  w = 0\n\n  h = 0\n\n  lastNumberOfPieces = 0\n\n  tweenProgress = 0\n\n  tweenFrom = 0\n\n  particles: Particle[] = []\n\n  particlesGenerated = 0\n\n  removeParticleAt = (i: number) => {\n    this.particles.splice(i, 1)\n  }\n\n  getParticle = () => {\n    const newParticleX = randomRange(this.x, this.w + this.x)\n    const newParticleY = randomRange(this.y, this.h + this.y)\n    return new Particle(\n      this.context,\n      this.getOptions,\n      newParticleX,\n      newParticleY,\n    )\n  }\n\n  animate = (elapsed: number): boolean => {\n    const { canvas, context, particlesGenerated, lastNumberOfPieces } = this\n    const {\n      run,\n      recycle,\n      numberOfPieces,\n      debug,\n      tweenFunction,\n      tweenDuration,\n    } = this.getOptions()\n    if (!run) {\n      return false\n    }\n\n    const nP = this.particles.length\n    const activeCount = recycle ? nP : particlesGenerated\n\n    // Initial population\n    if (activeCount < numberOfPieces) {\n      // Use the numberOfPieces prop as a key to reset the easing timing\n      if (lastNumberOfPieces !== numberOfPieces) {\n        this.tweenProgress = 0\n        this.tweenFrom = activeCount\n        this.lastNumberOfPieces = numberOfPieces\n      }\n\n      // Clamp tweenProgress between 0 and tweenDuration\n      this.tweenProgress = Math.min(\n        tweenDuration,\n        Math.max(0, this.tweenProgress + elapsed),\n      )\n      const tweenedVal = tweenFunction(\n        this.tweenProgress,\n        this.tweenFrom,\n        numberOfPieces,\n        tweenDuration,\n      )\n      const numToAdd = Math.round(tweenedVal - activeCount)\n      for (let i = 0; i < numToAdd; i++) {\n        this.particles.push(this.getParticle())\n      }\n      this.particlesGenerated += numToAdd\n    }\n    if (debug) {\n      // Draw debug text\n      context.font = '12px sans-serif'\n      context.fillStyle = '#333'\n      context.textAlign = 'right'\n      context.fillText(\n        `Particles: ${nP}`,\n        canvas.width - 10,\n        canvas.height - 20,\n      )\n    }\n\n    // Maintain the population, iterating backwards to prevent issues when removing particles\n    for (let i = this.particles.length - 1; i >= 0; i--) {\n      const p = this.particles[i]\n      // Update each particle's position\n      p.update(elapsed)\n      // Prune the off-canvas particles\n      if (\n        p.y > canvas.height ||\n        p.y < -100 ||\n        p.x > canvas.width + 100 ||\n        p.x < -100\n      ) {\n        if (recycle && activeCount <= numberOfPieces) {\n          // Replace the particle with a brand new one\n          this.particles[i] = this.getParticle()\n        } else {\n          this.removeParticleAt(i)\n        }\n      }\n    }\n    return nP > 0 || activeCount < numberOfPieces\n  }\n}\n", "import * as tweens from 'tween-functions'\nimport ParticleGenerator from './ParticleGenerator'\nimport { IRect } from './Rect'\n\nexport interface IConfettiOptions {\n  /**\n   * Width of the component\n   * @default window.width\n   */\n  width: number\n  /**\n   * Height of the component\n   * @default window.height\n   */\n  height: number\n  /**\n   * Max number of confetti pieces to render.\n   * @default 200\n   */\n  numberOfPieces: number\n  /**\n   * Slows movement of pieces. (lower number = slower confetti)\n   * @default 0.99\n   */\n  friction: number\n  /**\n   * Blows confetti along the X axis.\n   * @default 0\n   */\n  wind: number\n  /**\n   * How fast it falls (pixels per frame)\n   * @default 0.1\n   */\n  gravity: number\n  /**\n   * How fast the confetti is emitted horizontally\n   * @default 4\n   */\n  initialVelocityX: { min: number; max: number } | number\n  /**\n   * How fast the confetti is emitted vertically\n   * @default 10\n   */\n  initialVelocityY: { min: number; max: number } | number\n  /**\n   * Array of colors to choose from.\n   */\n  colors: string[]\n  /**\n   * Opacity of the confetti.\n   * @default 1\n   */\n  opacity: number\n  /**\n   * If false, only numberOfPieces will be emitted and then stops. If true, when a confetto goes offscreen, a new one will be emitted.\n   * @default true\n   */\n  recycle: boolean\n  /**\n   * If false, stops the requestAnimationFrame loop.\n   * @default true\n   */\n  run: boolean\n  /**\n   * The frame rate of the animation. If set, the animation will be throttled to that frame rate.\n   * @default undefined\n   */\n  frameRate?: number\n  /**\n   * Renders some debug text on the canvas.\n   * @default false\n   */\n  debug: boolean\n  /**\n   * A Rect defining the area where the confetti will spawn.\n   * @default {\n   *   x: 0,\n   *   y: 0,\n   *   w: canvas.width,\n   *   h: 0\n   * }\n   */\n  confettiSource: IRect\n  /**\n   * Controls the rate at which confetti is spawned.\n   * @default easeInOutQuad\n   */\n  tweenFunction: (\n    currentTime: number,\n    currentValue: number,\n    targetValue: number,\n    duration: number,\n    s?: number,\n  ) => number\n  /**\n   * Number of milliseconds it should take to spawn numberOfPieces.\n   * @default 5000\n   */\n  tweenDuration: number\n  /**\n   * Function to draw your own confetti shapes.\n   */\n  drawShape?: (context: CanvasRenderingContext2D) => void\n  /**\n   * Function called when all confetti has fallen off-canvas.\n   */\n  onConfettiComplete?: (confettiInstance?: Confetti) => void\n}\n\nexport const confettiDefaults: Pick<\n  IConfettiOptions,\n  Exclude<keyof IConfettiOptions, 'confettiSource'>\n> = {\n  width: typeof window !== 'undefined' ? window.innerWidth : 300,\n  height: typeof window !== 'undefined' ? window.innerHeight : 200,\n  numberOfPieces: 200,\n  friction: 0.99,\n  wind: 0,\n  gravity: 0.1,\n  initialVelocityX: 4,\n  initialVelocityY: 10,\n  colors: [\n    '#f44336',\n    '#e91e63',\n    '#9c27b0',\n    '#673ab7',\n    '#3f51b5',\n    '#2196f3',\n    '#03a9f4',\n    '#00bcd4',\n    '#009688',\n    '#4CAF50',\n    '#8BC34A',\n    '#CDDC39',\n    '#FFEB3B',\n    '#FFC107',\n    '#FF9800',\n    '#FF5722',\n    '#795548',\n  ],\n  opacity: 1.0,\n  debug: false,\n  tweenFunction: tweens.easeInOutQuad,\n  tweenDuration: 5000,\n  recycle: true,\n  run: true,\n}\n\nexport class Confetti {\n  constructor(canvas: HTMLCanvasElement, opts: Partial<IConfettiOptions>) {\n    this.canvas = canvas\n    const ctx = this.canvas.getContext('2d')\n    if (!ctx) {\n      throw new Error('Could not get canvas context')\n    }\n    this.context = ctx\n\n    this.generator = new ParticleGenerator(\n      this.canvas,\n      () => this.options as IConfettiOptions,\n    )\n    this.options = opts\n    this.update()\n  }\n\n  canvas: HTMLCanvasElement\n\n  context: CanvasRenderingContext2D\n\n  _options!: IConfettiOptions\n\n  generator: ParticleGenerator\n\n  rafId?: number\n\n  lastFrameTime = 0\n\n  get options(): Partial<IConfettiOptions> {\n    return this._options\n  }\n\n  set options(opts: Partial<IConfettiOptions>) {\n    const lastRunState = this._options?.run\n    const lastRecycleState = this._options?.recycle\n    this.setOptionsWithDefaults(opts)\n    if (this.generator) {\n      Object.assign(this.generator, this.options.confettiSource)\n      if (\n        typeof opts.recycle === 'boolean' &&\n        opts.recycle &&\n        lastRecycleState === false\n      ) {\n        this.generator.lastNumberOfPieces = this.generator.particles.length\n      }\n    }\n    if (typeof opts.run === 'boolean' && opts.run && lastRunState === false) {\n      this.update()\n    }\n  }\n\n  setOptionsWithDefaults = (opts: Partial<IConfettiOptions>) => {\n    const computedConfettiDefaults = {\n      confettiSource: {\n        x: 0,\n        y: 0,\n        w: this.canvas.width,\n        h: 0,\n      },\n    }\n    this._options = {\n      ...computedConfettiDefaults,\n      ...confettiDefaults,\n      ...opts,\n    }\n    Object.assign(this, opts.confettiSource)\n  }\n\n  update = (timestamp = 0) => {\n    const {\n      options: { run, onConfettiComplete, frameRate },\n      canvas,\n      context,\n    } = this\n    // Cap elapsed time to 50ms to prevent large time steps\n    const elapsed = Math.min(timestamp - this.lastFrameTime, 50)\n    // Throttle the frame rate if set\n    if (frameRate && elapsed < 1000 / frameRate) {\n      this.rafId = requestAnimationFrame(this.update)\n      return\n    }\n\n    this.lastFrameTime = timestamp - (frameRate ? elapsed % frameRate : 0)\n\n    if (run) {\n      context.fillStyle = 'white'\n      context.clearRect(0, 0, canvas.width, canvas.height)\n    }\n    if (this.generator.animate(elapsed)) {\n      this.rafId = requestAnimationFrame(this.update)\n    } else {\n      if (\n        onConfettiComplete &&\n        typeof onConfettiComplete === 'function' &&\n        this.generator.particlesGenerated > 0\n      ) {\n        onConfettiComplete.call(this, this)\n      }\n      this._options.run = false\n    }\n  }\n\n  reset = () => {\n    if (this.generator && this.generator.particlesGenerated > 0) {\n      this.generator.particlesGenerated = 0\n      this.generator.particles = []\n      this.generator.lastNumberOfPieces = 0\n    }\n  }\n\n  stop = () => {\n    this.options = { run: false }\n    if (this.rafId) {\n      cancelAnimationFrame(this.rafId)\n      this.rafId = undefined\n    }\n  }\n}\n\nexport default Confetti\n", "import React from 'react'\nimport Confetti, { IConfettiOptions, confettiDefaults } from './Confetti'\n\nexport type { IConfettiOptions } from './Confetti'\n\nconst ref = React.createRef<HTMLCanvasElement>()\n\nexport type Props = Partial<IConfettiOptions> &\n  React.CanvasHTMLAttributes<HTMLCanvasElement> & {\n    canvasRef?: React.Ref<HTMLCanvasElement>\n  }\n\nclass ReactConfettiInternal extends React.Component<Props> {\n  static readonly defaultProps = {\n    ...confettiDefaults,\n  }\n\n  static readonly displayName = 'ReactConfetti'\n\n  constructor(props: Props) {\n    super(props)\n    this.canvas = (props.canvasRef as React.RefObject<HTMLCanvasElement>) || ref\n  }\n\n  canvas: React.RefObject<HTMLCanvasElement | null> = React.createRef()\n\n  confetti?: Confetti\n\n  componentDidMount() {\n    if (this.canvas.current) {\n      const opts = extractCanvasProps(this.props)[0]\n      this.confetti = new Confetti(this.canvas.current, opts)\n    }\n  }\n\n  componentDidUpdate() {\n    const confettiOptions = extractCanvasProps(this.props)[0]\n    if (this.confetti) {\n      this.confetti.options = confettiOptions as IConfettiOptions\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.confetti) {\n      this.confetti.stop()\n    }\n    this.confetti = undefined\n  }\n\n  render() {\n    const [confettiOptions, passedProps] = extractCanvasProps(this.props)\n    const canvasStyles = {\n      zIndex: 2,\n      position: 'absolute' as const,\n      pointerEvents: 'none' as const,\n      top: 0,\n      left: 0,\n      bottom: 0,\n      right: 0,\n      ...passedProps.style,\n    }\n    return (\n      <canvas\n        width={confettiOptions.width}\n        height={confettiOptions.height}\n        ref={this.canvas}\n        {...passedProps}\n        style={canvasStyles}\n      />\n    )\n  }\n}\n\ninterface Refs {\n  [key: string]: React.Ref<HTMLElement>\n}\nfunction extractCanvasProps(\n  props: Partial<IConfettiOptions> | any,\n): [\n  Partial<IConfettiOptions>,\n  Partial<React.CanvasHTMLAttributes<HTMLCanvasElement>>,\n  Refs,\n] {\n  const confettiOptions: Partial<IConfettiOptions> = {}\n  const refs: Refs = {}\n  const rest: any = {}\n  const confettiOptionKeys = [\n    ...Object.keys(confettiDefaults),\n    'confettiSource',\n    'drawShape',\n    'onConfettiComplete',\n    'frameRate',\n  ]\n  const refProps = ['canvasRef']\n  for (const prop in props) {\n    const val = props[prop as string]\n    if (confettiOptionKeys.includes(prop)) {\n      confettiOptions[prop as keyof IConfettiOptions] = val\n    } else if (refProps.includes(prop)) {\n      refProps[prop as any] = val\n    } else {\n      rest[prop] = val\n    }\n  }\n  return [confettiOptions, rest, refs]\n}\n\nexport const ReactConfetti = React.forwardRef<HTMLCanvasElement, Props>(\n  (props, ref) => <ReactConfettiInternal canvasRef={ref} {...props} />,\n)\n\nexport default ReactConfetti\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAGA,QAAI,iBAAiB;AAAA,MACnB,QAAQ,SAAS,GAAG,GAAG,IAAI,GAAG;AAC5B,YAAI,IAAI,KAAK;AACb,eAAO,IAAI,IAAI,IAAI;AAAA,MACrB;AAAA,MACA,YAAY,SAAS,GAAG,GAAG,IAAI,GAAG;AAChC,YAAI,IAAI,KAAK;AACb,eAAO,KAAK,KAAK,KAAK,IAAI;AAAA,MAC5B;AAAA,MACA,aAAa,SAAS,GAAG,GAAG,IAAI,GAAG;AACjC,YAAI,IAAI,KAAK;AACb,eAAO,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK;AAAA,MACnC;AAAA,MACA,eAAe,SAAS,GAAG,GAAG,IAAI,GAAG;AACnC,YAAI,IAAI,KAAK;AACb,aAAK,KAAK,IAAI,KAAK,GAAG;AACpB,iBAAO,IAAI,IAAI,IAAI,IAAI;AAAA,QACzB,OAAO;AACL,iBAAO,CAAC,IAAI,KAAM,EAAE,KAAM,IAAI,KAAK,KAAK;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,aAAa,SAAS,GAAG,GAAG,IAAI,GAAG;AACjC,YAAI,IAAI,KAAK;AACb,eAAO,KAAK,KAAK,KAAK,IAAI,IAAI;AAAA,MAChC;AAAA,MACA,cAAc,SAAS,GAAG,GAAG,IAAI,GAAG;AAClC,YAAI,IAAI,KAAK;AACb,eAAO,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK;AAAA,MAC7C;AAAA,MACA,gBAAgB,SAAS,GAAG,GAAG,IAAI,GAAG;AACpC,YAAI,IAAI,KAAK;AACb,aAAK,KAAK,IAAI,KAAK,GAAG;AACpB,iBAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,QAC7B,OAAO;AACL,iBAAO,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,KAAK;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,aAAa,SAAS,GAAG,GAAG,IAAI,GAAG;AACjC,YAAI,IAAI,KAAK;AACb,eAAO,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI;AAAA,MACpC;AAAA,MACA,cAAc,SAAS,GAAG,GAAG,IAAI,GAAG;AAClC,YAAI,IAAI,KAAK;AACb,eAAO,CAAC,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK;AAAA,MAClD;AAAA,MACA,gBAAgB,SAAS,GAAG,GAAG,IAAI,GAAG;AACpC,YAAI,IAAI,KAAK;AACb,aAAK,KAAK,IAAI,KAAK,GAAG;AACpB,iBAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,QACjC,OAAO;AACL,iBAAO,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,aAAa,SAAS,GAAG,GAAG,IAAI,GAAG;AACjC,YAAI,IAAI,KAAK;AACb,eAAO,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI;AAAA,MACxC;AAAA,MACA,cAAc,SAAS,GAAG,GAAG,IAAI,GAAG;AAClC,YAAI,IAAI,KAAK;AACb,eAAO,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AAAA,MACrD;AAAA,MACA,gBAAgB,SAAS,GAAG,GAAG,IAAI,GAAG;AACpC,YAAI,IAAI,KAAK;AACb,aAAK,KAAK,IAAI,KAAK,GAAG;AACpB,iBAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,QACrC,OAAO;AACL,iBAAO,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AAAA,QAClD;AAAA,MACF;AAAA,MACA,YAAY,SAAS,GAAG,GAAG,IAAI,GAAG;AAChC,YAAI,IAAI,KAAK;AACb,eAAO,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,IAAI,IAAI;AAAA,MACpD;AAAA,MACA,aAAa,SAAS,GAAG,GAAG,IAAI,GAAG;AACjC,YAAI,IAAI,KAAK;AACb,eAAO,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,IAAI;AAAA,MAC/C;AAAA,MACA,eAAe,SAAS,GAAG,GAAG,IAAI,GAAG;AACnC,YAAI,IAAI,KAAK;AACb,eAAO,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK;AAAA,MACpD;AAAA,MACA,YAAY,SAAS,GAAG,GAAG,IAAI,GAAG;AAChC,YAAI,IAAI,KAAK;AACb,eAAQ,KAAG,IAAK,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,IAAE,IAAI,EAAE,IAAI;AAAA,MACxD;AAAA,MACA,aAAa,SAAS,GAAG,GAAG,IAAI,GAAG;AACjC,YAAI,IAAI,KAAK;AACb,eAAQ,KAAG,IAAK,IAAE,IAAI,KAAK,CAAC,KAAK,IAAI,GAAG,MAAM,IAAE,CAAC,IAAI,KAAK;AAAA,MAC5D;AAAA,MACA,eAAe,SAAS,GAAG,GAAG,IAAI,GAAG;AACnC,YAAI,IAAI,KAAK;AACb,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT;AACA,YAAI,MAAM,GAAG;AACX,iBAAO,IAAI;AAAA,QACb;AACA,aAAK,KAAK,IAAI,KAAK,GAAG;AACpB,iBAAO,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,EAAE,IAAI;AAAA,QAC7C,OAAO;AACL,iBAAO,IAAI,KAAK,CAAC,KAAK,IAAI,GAAG,MAAM,EAAE,CAAC,IAAI,KAAK;AAAA,QACjD;AAAA,MACF;AAAA,MACA,YAAY,SAAS,GAAG,GAAG,IAAI,GAAG;AAChC,YAAI,IAAI,KAAK;AACb,eAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK;AAAA,MAClD;AAAA,MACA,aAAa,SAAS,GAAG,GAAG,IAAI,GAAG;AACjC,YAAI,IAAI,KAAK;AACb,eAAO,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI;AAAA,MAClD;AAAA,MACA,eAAe,SAAS,GAAG,GAAG,IAAI,GAAG;AACnC,YAAI,IAAI,KAAK;AACb,aAAK,KAAK,IAAI,KAAK,GAAG;AACpB,iBAAO,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK;AAAA,QAC/C,OAAO;AACL,iBAAO,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK;AAAA,QACrD;AAAA,MACF;AAAA,MACA,eAAe,SAAS,GAAG,GAAG,IAAI,GAAG;AACnC,YAAI,IAAI,KAAK;AACb,YAAI,GAAG,GAAG;AACV,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT,YAAY,KAAK,OAAO,GAAG;AACzB,iBAAO,IAAI;AAAA,QACb;AACA,YAAI,CAAC,GAAG;AACN,cAAI,IAAI;AAAA,QACV;AACA,YAAI,IAAI,KAAK,IAAI,CAAC,GAAG;AACnB,cAAI;AACJ,cAAI,IAAI;AAAA,QACV,OAAO;AACL,cAAI,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,QACzC;AACA,eAAO,EAAE,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC,KAAK;AAAA,MACzF;AAAA,MACA,gBAAgB,SAAS,GAAG,GAAG,IAAI,GAAG;AACpC,YAAI,IAAI,KAAK;AACb,YAAI,GAAG,GAAG;AACV,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT,YAAY,KAAK,OAAO,GAAG;AACzB,iBAAO,IAAI;AAAA,QACb;AACA,YAAI,CAAC,GAAG;AACN,cAAI,IAAI;AAAA,QACV;AACA,YAAI,IAAI,KAAK,IAAI,CAAC,GAAG;AACnB,cAAI;AACJ,cAAI,IAAI;AAAA,QACV,OAAO;AACL,cAAI,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,QACzC;AACA,eAAO,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI;AAAA,MACpF;AAAA,MACA,kBAAkB,SAAS,GAAG,GAAG,IAAI,GAAG;AACtC,YAAI,IAAI,KAAK;AACb,YAAI,GAAG,GAAG;AACV,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,MAAM,GAAG;AACX,iBAAO;AAAA,QACT,YAAY,KAAK,IAAI,OAAO,GAAG;AAC7B,iBAAO,IAAI;AAAA,QACb;AACA,YAAI,CAAC,GAAG;AACN,cAAI,KAAK,MAAM;AAAA,QACjB;AACA,YAAI,IAAI,KAAK,IAAI,CAAC,GAAG;AACnB,cAAI;AACJ,cAAI,IAAI;AAAA,QACV,OAAO;AACL,cAAI,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,QACzC;AACA,YAAI,IAAI,GAAG;AACT,iBAAO,QAAQ,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC,KAAK;AAAA,QAC/F,OAAO;AACL,iBAAO,IAAI,KAAK,IAAI,GAAG,OAAO,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,MAAM,IAAI;AAAA,QACjG;AAAA,MACF;AAAA,MACA,YAAY,SAAS,GAAG,GAAG,IAAI,GAAG,GAAG;AACnC,YAAI,IAAI,KAAK;AACb,YAAI,MAAM,QAAQ;AAChB,cAAI;AAAA,QACN;AACA,eAAO,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,MAChD;AAAA,MACA,aAAa,SAAS,GAAG,GAAG,IAAI,GAAG,GAAG;AACpC,YAAI,IAAI,KAAK;AACb,YAAI,MAAM,QAAQ;AAChB,cAAI;AAAA,QACN;AACA,eAAO,MAAM,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,MAC7D;AAAA,MACA,eAAe,SAAS,GAAG,GAAG,IAAI,GAAG,GAAG;AACtC,YAAI,IAAI,KAAK;AACb,YAAI,MAAM,QAAQ;AAChB,cAAI;AAAA,QACN;AACA,aAAK,KAAK,IAAI,KAAK,GAAG;AACpB,iBAAO,IAAI,KAAK,IAAI,OAAO,KAAK,SAAS,KAAK,IAAI,MAAM;AAAA,QAC1D,OAAO;AACL,iBAAO,IAAI,MAAM,KAAK,KAAK,OAAO,KAAK,SAAS,KAAK,IAAI,KAAK,KAAK;AAAA,QACrE;AAAA,MACF;AAAA,MACA,cAAc,SAAS,GAAG,GAAG,IAAI,GAAG;AAClC,YAAI,IAAI,KAAK;AACb,YAAI;AACJ,YAAI,eAAe,cAAc,IAAI,GAAG,GAAG,GAAG,CAAC;AAC/C,eAAO,IAAI,IAAI;AAAA,MACjB;AAAA,MACA,eAAe,SAAS,GAAG,GAAG,IAAI,GAAG;AACnC,YAAI,IAAI,KAAK;AACb,aAAK,KAAK,KAAK,IAAI,MAAM;AACvB,iBAAO,KAAK,SAAS,IAAI,KAAK;AAAA,QAChC,WAAW,IAAI,IAAI,MAAM;AACvB,iBAAO,KAAK,UAAU,KAAK,MAAM,QAAQ,IAAI,QAAQ;AAAA,QACvD,WAAW,IAAI,MAAM,MAAM;AACzB,iBAAO,KAAK,UAAU,KAAK,OAAO,QAAQ,IAAI,UAAU;AAAA,QAC1D,OAAO;AACL,iBAAO,KAAK,UAAU,KAAK,QAAQ,QAAQ,IAAI,YAAY;AAAA,QAC7D;AAAA,MACF;AAAA,MACA,iBAAiB,SAAS,GAAG,GAAG,IAAI,GAAG;AACrC,YAAI,IAAI,KAAK;AACb,YAAI;AACJ,YAAI,IAAI,IAAI,GAAG;AACb,cAAI,eAAe,aAAa,IAAI,GAAG,GAAG,GAAG,CAAC;AAC9C,iBAAO,IAAI,MAAM;AAAA,QACnB,OAAO;AACL,cAAI,eAAe,cAAc,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AACnD,iBAAO,IAAI,MAAM,IAAI,MAAM;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;;;;AC5KX,SAAU,cAAc,SAAe;AAC3C,SAAQ,UAAU,KAAK,KAAM;AAC/B;AAMgB,SAAA,YAAY,KAAa,KAAW;AAClD,SAAO,MAAM,KAAK,OAAM,KAAM,MAAM;AACtC;AAEgB,SAAA,UAAU,KAAa,KAAW;AAChD,SAAO,KAAK,MAAM,MAAM,KAAK,OAAM,KAAM,MAAM,MAAM,EAAE;AACzD;ACvFA,IAAY;CAAZ,SAAYA,gBAAa;AACvB,EAAAA,eAAAA,eAAA,QAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,QAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,OAAA,IAAA,CAAA,IAAA;AACF,GAJY,kBAAA,gBAIX,CAAA,EAAA;AAED,IAAK;CAAL,SAAKC,oBAAiB;AACpB,EAAAA,mBAAAA,mBAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,mBAAAA,mBAAA,UAAA,IAAA,EAAA,IAAA;AACF,GAHK,sBAAA,oBAGJ,CAAA,EAAA;AAED,IAAM,qBAAqB,MAAO;AAEpB,IAAO,WAAP,MAAe;EAC3B,YACE,SACA,YACA,GACA,GAAS;AAET,SAAK,aAAa;AAClB,UAAM,EAAE,QAAQ,kBAAkB,iBAAgB,IAAK,KAAK,WAAU;AACtE,SAAK,UAAU;AACf,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI,YAAY,GAAG,EAAE;AAC1B,SAAK,IAAI,YAAY,GAAG,EAAE;AAC1B,SAAK,SAAS,YAAY,GAAG,EAAE;AAC/B,SAAK,KACH,OAAO,qBAAqB,WACxB,YAAY,CAAC,kBAAkB,gBAAgB,IAC/C,YAAY,iBAAiB,KAAK,iBAAiB,GAAG;AAC5D,SAAK,KACH,OAAO,qBAAqB,WACxB,YAAY,CAAC,kBAAkB,CAAC,IAChC,YAAY,iBAAiB,KAAK,iBAAiB,GAAG;AAC5D,SAAK,QAAQ,UAAU,GAAG,CAAC;AAC3B,SAAK,QAAQ,cAAc,YAAY,GAAG,GAAG,CAAC;AAC9C,SAAK,cAAc,YAAY,MAAM,GAAG;AACxC,SAAK,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,OAAO,MAAM,CAAC;AAC7D,SAAK,UAAU,YAAY,GAAG,CAAC;AAC/B,SAAK,oBAAoB,YAAY,GAAG,CAAC,IACrC,kBAAkB,WAClB,kBAAkB;;EAkCxB,OAAO,SAAe;AACpB,UAAM,EAAE,SAAS,MAAM,UAAU,SAAS,UAAS,IAAK,KAAK,WAAU;AACvE,UAAM,sBAAsB,UAAU;AACtC,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,MAAM,UAAU;AACrB,SAAK,MAAM,OAAO;AAClB,SAAK,MAAM,YAAY;AACvB,SAAK,MAAM,YAAY;AACvB,QACE,KAAK,WAAW,KAChB,KAAK,sBAAsB,kBAAkB,UAC7C;AACA,WAAK,oBAAoB,kBAAkB;eAE3C,KAAK,WAAW,MAChB,KAAK,sBAAsB,kBAAkB,UAC7C;AACA,WAAK,oBAAoB,kBAAkB;;AAG7C,UAAM,cAAc,MAAM,KAAK,oBAAoB;AAEnD,SAAK,WAAW;AAChB,SAAK,SAAS,KAAK;AACnB,SAAK,QAAQ,KAAI;AACjB,SAAK,QAAQ,UAAU,KAAK,GAAG,KAAK,CAAC;AACrC,SAAK,QAAQ,OAAO,KAAK,KAAK;AAC9B,SAAK,QAAQ,MAAM,GAAG,KAAK,OAAO;AAClC,SAAK,QAAQ,OAAO,KAAK,KAAK;AAC9B,SAAK,QAAQ,UAAS;AACtB,SAAK,QAAQ,YAAY,KAAK;AAC9B,SAAK,QAAQ,cAAc,KAAK;AAChC,SAAK,QAAQ,cAAc;AAC3B,SAAK,QAAQ,UAAU;AACvB,SAAK,QAAQ,YAAY;AACzB,QAAI,aAAa,OAAO,cAAc,YAAY;AAChD,gBAAU,KAAK,MAAM,KAAK,OAAO;WAC5B;AACL,cAAQ,KAAK,OAAK;QAChB,KAAK,cAAc,QAAQ;AACzB,eAAK,QAAQ,UAAS;AACtB,eAAK,QAAQ,IAAI,GAAG,GAAG,KAAK,QAAQ,GAAG,IAAI,KAAK,EAAE;AAClD,eAAK,QAAQ,KAAI;AACjB;;QAEF,KAAK,cAAc,QAAQ;AACzB,eAAK,QAAQ,SAAS,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;AAC9D;;QAEF,KAAK,cAAc,OAAO;AACxB,eAAK,QAAQ,SAAS,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC;AAClE;;;;AAIN,SAAK,QAAQ,UAAS;AACtB,SAAK,QAAQ,QAAO;;AAEvB;AC9Ha,IAAO,oBAAP,MAAwB;EACpC,YAAY,QAA2B,YAAkC;AAgBzE,SAAC,IAAG;AAEJ,SAAC,IAAG;AAEJ,SAAC,IAAG;AAEJ,SAAC,IAAG;AAEJ,SAAkB,qBAAG;AAErB,SAAa,gBAAG;AAEhB,SAAS,YAAG;AAEZ,SAAS,YAAe,CAAA;AAExB,SAAkB,qBAAG;AAErB,SAAA,mBAAmB,CAAC,MAAa;AAC/B,WAAK,UAAU,OAAO,GAAG,CAAC;IAC5B;AAEA,SAAW,cAAG,MAAK;AACjB,YAAM,eAAe,YAAY,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC;AACxD,YAAM,eAAe,YAAY,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC;AACxD,aAAO,IAAI,SACT,KAAK,SACL,KAAK,YACL,cACA,YAAY;IAEhB;AAEA,SAAA,UAAU,CAAC,YAA4B;AACrC,YAAM,EAAE,QAAAC,SAAQ,SAAS,oBAAoB,mBAAkB,IAAK;AACpE,YAAM,EACJ,KACA,SACA,gBACA,OACA,eACA,cAAa,IACX,KAAK,WAAU;AACnB,UAAI,CAAC,KAAK;AACR,eAAO;;AAGT,YAAM,KAAK,KAAK,UAAU;AAC1B,YAAM,cAAc,UAAU,KAAK;AAGnC,UAAI,cAAc,gBAAgB;AAEhC,YAAI,uBAAuB,gBAAgB;AACzC,eAAK,gBAAgB;AACrB,eAAK,YAAY;AACjB,eAAK,qBAAqB;;AAI5B,aAAK,gBAAgB,KAAK,IACxB,eACA,KAAK,IAAI,GAAG,KAAK,gBAAgB,OAAO,CAAC;AAE3C,cAAM,aAAa,cACjB,KAAK,eACL,KAAK,WACL,gBACA,aAAa;AAEf,cAAM,WAAW,KAAK,MAAM,aAAa,WAAW;AACpD,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,eAAK,UAAU,KAAK,KAAK,YAAW,CAAE;;AAExC,aAAK,sBAAsB;;AAE7B,UAAI,OAAO;AAET,gBAAQ,OAAO;AACf,gBAAQ,YAAY;AACpB,gBAAQ,YAAY;AACpB,gBAAQ,SACN,cAAc,EAAE,IAChBA,QAAO,QAAQ,IACfA,QAAO,SAAS,EAAE;;AAKtB,eAAS,IAAI,KAAK,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AACnD,cAAM,IAAI,KAAK,UAAU,CAAC;AAE1B,UAAE,OAAO,OAAO;AAEhB,YACE,EAAE,IAAIA,QAAO,UACb,EAAE,IAAI,QACN,EAAE,IAAIA,QAAO,QAAQ,OACrB,EAAE,IAAI,MACN;AACA,cAAI,WAAW,eAAe,gBAAgB;AAE5C,iBAAK,UAAU,CAAC,IAAI,KAAK,YAAW;iBAC/B;AACL,iBAAK,iBAAiB,CAAC;;;;AAI7B,aAAO,KAAK,KAAK,cAAc;IACjC;AA5HE,SAAK,SAAS;AACd,UAAM,MAAM,KAAK,OAAO,WAAW,IAAI;AACvC,QAAI,CAAC,KAAK;AACR,YAAM,IAAI,MAAM,8BAA8B;;AAEhD,SAAK,UAAU;AACf,SAAK,aAAa;;AAuHrB;AC9BM,IAAM,mBAGT;EACF,OAAO,OAAO,WAAW,cAAc,OAAO,aAAa;EAC3D,QAAQ,OAAO,WAAW,cAAc,OAAO,cAAc;EAC7D,gBAAgB;EAChB,UAAU;EACV,MAAM;EACN,SAAS;EACT,kBAAkB;EAClB,kBAAkB;EAClB,QAAQ;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD;EACD,SAAS;EACT,OAAO;EACP,eAAsB;EACtB,eAAe;EACf,SAAS;EACT,KAAK;;IAGM,iBAAQ;EACnB,YAAY,QAA2B,MAA+B;AA0BtE,SAAa,gBAAG;AAyBhB,SAAA,yBAAyB,CAACC,UAAmC;AAC3D,YAAM,2BAA2B;QAC/B,gBAAgB;UACd,GAAG;UACH,GAAG;UACH,GAAG,KAAK,OAAO;UACf,GAAG;QACJ;;AAEH,WAAK,WAAW;QACd,GAAG;QACH,GAAG;QACH,GAAGA;;AAEL,aAAO,OAAO,MAAMA,MAAK,cAAc;IACzC;AAEA,SAAA,SAAS,CAAC,YAAY,MAAK;AACzB,YAAM,EACJ,SAAS,EAAE,KAAK,oBAAoB,UAAS,GAC7C,QAAAD,SACA,QAAO,IACL;AAEJ,YAAM,UAAU,KAAK,IAAI,YAAY,KAAK,eAAe,EAAE;AAE3D,UAAI,aAAa,UAAU,MAAO,WAAW;AAC3C,aAAK,QAAQ,sBAAsB,KAAK,MAAM;AAC9C;;AAGF,WAAK,gBAAgB,aAAa,YAAY,UAAU,YAAY;AAEpE,UAAI,KAAK;AACP,gBAAQ,YAAY;AACpB,gBAAQ,UAAU,GAAG,GAAGA,QAAO,OAAOA,QAAO,MAAM;;AAErD,UAAI,KAAK,UAAU,QAAQ,OAAO,GAAG;AACnC,aAAK,QAAQ,sBAAsB,KAAK,MAAM;aACzC;AACL,YACE,sBACA,OAAO,uBAAuB,cAC9B,KAAK,UAAU,qBAAqB,GACpC;AACA,6BAAmB,KAAK,MAAM,IAAI;;AAEpC,aAAK,SAAS,MAAM;;IAExB;AAEA,SAAK,QAAG,MAAK;AACX,UAAI,KAAK,aAAa,KAAK,UAAU,qBAAqB,GAAG;AAC3D,aAAK,UAAU,qBAAqB;AACpC,aAAK,UAAU,YAAY,CAAA;AAC3B,aAAK,UAAU,qBAAqB;;IAExC;AAEA,SAAI,OAAG,MAAK;AACV,WAAK,UAAU,EAAE,KAAK,MAAK;AAC3B,UAAI,KAAK,OAAO;AACd,6BAAqB,KAAK,KAAK;AAC/B,aAAK,QAAQ;;IAEjB;AAnHE,SAAK,SAAS;AACd,UAAM,MAAM,KAAK,OAAO,WAAW,IAAI;AACvC,QAAI,CAAC,KAAK;AACR,YAAM,IAAI,MAAM,8BAA8B;;AAEhD,SAAK,UAAU;AAEf,SAAK,YAAY,IAAI,kBACnB,KAAK,QACL,MAAM,KAAK,OAA2B;AAExC,SAAK,UAAU;AACf,SAAK,OAAM;;EAeb,IAAI,UAAO;AACT,WAAO,KAAK;;EAGd,IAAI,QAAQ,MAA+B;;AACzC,UAAM,gBAAe,UAAK,aAAL,mBAAe;AACpC,UAAM,oBAAmB,UAAK,aAAL,mBAAe;AACxC,SAAK,uBAAuB,IAAI;AAChC,QAAI,KAAK,WAAW;AAClB,aAAO,OAAO,KAAK,WAAW,KAAK,QAAQ,cAAc;AACzD,UACE,OAAO,KAAK,YAAY,aACxB,KAAK,WACL,qBAAqB,OACrB;AACA,aAAK,UAAU,qBAAqB,KAAK,UAAU,UAAU;;;AAGjE,QAAI,OAAO,KAAK,QAAQ,aAAa,KAAK,OAAO,iBAAiB,OAAO;AACvE,WAAK,OAAM;;;AAsEhB;ACtQD,IAAM,MAAM,aAAAE,QAAM,UAAS;AAO3B,IAAM,wBAAN,cAAoC,aAAAA,QAAM,UAAgB;EAOxD,YAAY,OAAY;AACtB,UAAM,KAAK;AAIb,SAAA,SAAoD,aAAAA,QAAM,UAAS;AAHjE,SAAK,SAAU,MAAM,aAAoD;;EAO3E,oBAAiB;AACf,QAAI,KAAK,OAAO,SAAS;AACvB,YAAM,OAAO,mBAAmB,KAAK,KAAK,EAAE,CAAC;AAC7C,WAAK,WAAW,IAAI,SAAS,KAAK,OAAO,SAAS,IAAI;;;EAI1D,qBAAkB;AAChB,UAAM,kBAAkB,mBAAmB,KAAK,KAAK,EAAE,CAAC;AACxD,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,UAAU;;;EAI5B,uBAAoB;AAClB,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,KAAI;;AAEpB,SAAK,WAAW;;EAGlB,SAAM;AACJ,UAAM,CAAC,iBAAiB,WAAW,IAAI,mBAAmB,KAAK,KAAK;AACpE,UAAM,eAAe;MACnB,QAAQ;MACR,UAAU;MACV,eAAe;MACf,KAAK;MACL,MAAM;MACN,QAAQ;MACR,OAAO;MACP,GAAG,YAAY;;AAEjB,eACEC,wBACE,UAAA,EAAA,OAAO,gBAAgB,OACvB,QAAQ,gBAAgB,QACxB,KAAK,KAAK,QACN,GAAA,aACJ,OAAO,aAAY,CACnB;;;AAvDU,sBAAA,eAAe;EAC7B,GAAG;AACJ;AAEe,sBAAW,cAAG;AA2DhC,SAAS,mBACP,OAAsC;AAMtC,QAAM,kBAA6C,CAAA;AACnD,QAAM,OAAa,CAAA;AACnB,QAAM,OAAY,CAAA;AAClB,QAAM,qBAAqB;IACzB,GAAG,OAAO,KAAK,gBAAgB;IAC/B;IACA;IACA;IACA;;AAEF,QAAM,WAAW,CAAC,WAAW;AAC7B,aAAW,QAAQ,OAAO;AACxB,UAAM,MAAM,MAAM,IAAc;AAChC,QAAI,mBAAmB,SAAS,IAAI,GAAG;AACrC,sBAAgB,IAA8B,IAAI;eACzC,SAAS,SAAS,IAAI,GAAG;AAClC,eAAS,IAAW,IAAI;WACnB;AACL,WAAK,IAAI,IAAI;;;AAGjB,SAAO,CAAC,iBAAiB,MAAM,IAAI;AACrC;AAEa,IAAA,gBAAgB,aAAAD,QAAM,WACjC,CAAC,OAAOE,aAAQD,wBAAC,uBAAsB,EAAA,WAAWC,MAAS,GAAA,MAAK,CAAI,CAAA;", "names": ["ParticleShape", "RotationDirection", "canvas", "opts", "React", "_jsx", "ref"]}