{"version": 3, "sources": ["../src/callWithRetry.ts"], "sourcesContent": ["function wait(ms: number) {\n  return new Promise(res => setTimeout(res, ms));\n}\n\nconst MAX_NUMBER_OF_RETRIES = 5;\n\n/**\n * Retry callback function every few hundred ms (with an exponential backoff\n * based on the current attempt) until the maximum attempts has reached or\n * the callback is executed successfully. The default number of maximum\n * attempts is 5 and retries are triggered when callback throws an error.\n */\nexport async function callWithRetry<T>(\n  fn: (...args: unknown[]) => Promise<T>,\n  attempt = 1,\n  maxAttempts = MAX_NUMBER_OF_RETRIES,\n): Promise<T> {\n  try {\n    return await fn();\n  } catch (e) {\n    if (attempt >= maxAttempts) {\n      throw e;\n    }\n    await wait(2 ** attempt * 100);\n\n    return callWithRetry(fn, attempt + 1, maxAttempts);\n  }\n}\n"], "mappings": ";AAAA,SAAS,KAAK,IAAY;AACxB,SAAO,IAAI,QAAQ,SAAO,WAAW,KAAK,EAAE,CAAC;AAC/C;AAEA,IAAM,wBAAwB;AAQ9B,eAAsB,cACpB,IACA,UAAU,GACV,cAAc,uBACF;AACZ,MAAI;AACF,WAAO,MAAM,GAAG;AAAA,EAClB,SAAS,GAAG;AACV,QAAI,WAAW,aAAa;AAC1B,YAAM;AAAA,IACR;AACA,UAAM,KAAK,KAAK,UAAU,GAAG;AAE7B,WAAO,cAAc,IAAI,UAAU,GAAG,WAAW;AAAA,EACnD;AACF;", "names": []}