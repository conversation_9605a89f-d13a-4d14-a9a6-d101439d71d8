{"version": 3, "sources": ["../../tsparticles-particles.js/esm/marcb<PERSON><PERSON>lin/Particles.js", "../../tsparticles-particles.js/esm/Vincent<PERSON>/particles.js", "../../tsparticles-particles.js/esm/index.js", "../../tsparticles-move-base/esm/Utils.js", "../../tsparticles-move-base/esm/BaseMover.js", "../../tsparticles-move-base/esm/index.js", "../../tsparticles-shape-circle/esm/CircleDrawer.js", "../../tsparticles-shape-circle/esm/index.js", "../../tsparticles-updater-color/esm/Utils.js", "../../tsparticles-updater-color/esm/ColorUpdater.js", "../../tsparticles-updater-color/esm/index.js", "../../tsparticles-updater-opacity/esm/Utils.js", "../../tsparticles-updater-opacity/esm/OpacityUpdater.js", "../../tsparticles-updater-opacity/esm/index.js", "../../tsparticles-updater-out-modes/esm/Utils.js", "../../tsparticles-updater-out-modes/esm/BounceOutMode.js", "../../tsparticles-updater-out-modes/esm/DestroyOutMode.js", "../../tsparticles-updater-out-modes/esm/NoneOutMode.js", "../../tsparticles-updater-out-modes/esm/OutOutMode.js", "../../tsparticles-updater-out-modes/esm/OutOfCanvasUpdater.js", "../../tsparticles-updater-out-modes/esm/index.js", "../../tsparticles-updater-size/esm/Utils.js", "../../tsparticles-updater-size/esm/SizeUpdater.js", "../../tsparticles-updater-size/esm/index.js", "../../tsparticles-basic/esm/index.js", "../../tsparticles-plugin-easing-quad/esm/index.js", "../../tsparticles-interaction-external-attract/esm/Options/Classes/Attract.js", "../../tsparticles-interaction-external-attract/esm/Attractor.js", "../../tsparticles-interaction-external-attract/esm/index.js", "../../tsparticles-interaction-external-bounce/esm/Options/Classes/Bounce.js", "../../tsparticles-interaction-external-bounce/esm/Bouncer.js", "../../tsparticles-interaction-external-bounce/esm/index.js", "../../tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleBase.js", "../../tsparticles-interaction-external-bubble/esm/Options/Classes/BubbleDiv.js", "../../tsparticles-interaction-external-bubble/esm/Options/Classes/Bubble.js", "../../tsparticles-interaction-external-bubble/esm/Utils.js", "../../tsparticles-interaction-external-bubble/esm/Bubbler.js", "../../tsparticles-interaction-external-bubble/esm/index.js", "../../tsparticles-interaction-external-connect/esm/Options/Classes/ConnectLinks.js", "../../tsparticles-interaction-external-connect/esm/Options/Classes/Connect.js", "../../tsparticles-interaction-external-connect/esm/Utils.js", "../../tsparticles-interaction-external-connect/esm/Connector.js", "../../tsparticles-interaction-external-connect/esm/index.js", "../../tsparticles-interaction-external-grab/esm/Options/Classes/GrabLinks.js", "../../tsparticles-interaction-external-grab/esm/Options/Classes/Grab.js", "../../tsparticles-interaction-external-grab/esm/Utils.js", "../../tsparticles-interaction-external-grab/esm/Grabber.js", "../../tsparticles-interaction-external-grab/esm/index.js", "../../tsparticles-interaction-external-pause/esm/Pauser.js", "../../tsparticles-interaction-external-pause/esm/index.js", "../../tsparticles-interaction-external-push/esm/Options/Classes/Push.js", "../../tsparticles-interaction-external-push/esm/Pusher.js", "../../tsparticles-interaction-external-push/esm/index.js", "../../tsparticles-interaction-external-remove/esm/Options/Classes/Remove.js", "../../tsparticles-interaction-external-remove/esm/Remover.js", "../../tsparticles-interaction-external-remove/esm/index.js", "../../tsparticles-interaction-external-repulse/esm/Options/Classes/RepulseBase.js", "../../tsparticles-interaction-external-repulse/esm/Options/Classes/RepulseDiv.js", "../../tsparticles-interaction-external-repulse/esm/Options/Classes/Repulse.js", "../../tsparticles-interaction-external-repulse/esm/Repulser.js", "../../tsparticles-interaction-external-repulse/esm/index.js", "../../tsparticles-interaction-external-slow/esm/Options/Classes/Slow.js", "../../tsparticles-interaction-external-slow/esm/Slower.js", "../../tsparticles-interaction-external-slow/esm/index.js", "../../tsparticles-shape-image/esm/GifUtils/Constants.js", "../../tsparticles-shape-image/esm/GifUtils/ByteStream.js", "../../tsparticles-shape-image/esm/GifUtils/Utils.js", "../../tsparticles-shape-image/esm/Utils.js", "../../tsparticles-shape-image/esm/ImageDrawer.js", "../../tsparticles-shape-image/esm/Options/Classes/Preload.js", "../../tsparticles-shape-image/esm/ImagePreloader.js", "../../tsparticles-shape-image/esm/index.js", "../../tsparticles-updater-life/esm/Options/Classes/LifeDelay.js", "../../tsparticles-updater-life/esm/Options/Classes/LifeDuration.js", "../../tsparticles-updater-life/esm/Options/Classes/Life.js", "../../tsparticles-updater-life/esm/LifeUpdater.js", "../../tsparticles-updater-life/esm/index.js", "../../tsparticles-shape-line/esm/LineDrawer.js", "../../tsparticles-shape-line/esm/index.js", "../../tsparticles-move-parallax/esm/ParallaxMover.js", "../../tsparticles-move-parallax/esm/index.js", "../../tsparticles-interaction-particles-attract/esm/Attractor.js", "../../tsparticles-interaction-particles-attract/esm/index.js", "../../tsparticles-interaction-particles-collisions/esm/Absorb.js", "../../tsparticles-interaction-particles-collisions/esm/Bounce.js", "../../tsparticles-interaction-particles-collisions/esm/Destroy.js", "../../tsparticles-interaction-particles-collisions/esm/ResolveCollision.js", "../../tsparticles-interaction-particles-collisions/esm/Collider.js", "../../tsparticles-interaction-particles-collisions/esm/index.js", "../../tsparticles-interaction-particles-links/esm/CircleWarp.js", "../../tsparticles-interaction-particles-links/esm/Options/Classes/LinksShadow.js", "../../tsparticles-interaction-particles-links/esm/Options/Classes/LinksTriangle.js", "../../tsparticles-interaction-particles-links/esm/Options/Classes/Links.js", "../../tsparticles-interaction-particles-links/esm/Linker.js", "../../tsparticles-interaction-particles-links/esm/interaction.js", "../../tsparticles-interaction-particles-links/esm/Utils.js", "../../tsparticles-interaction-particles-links/esm/LinkInstance.js", "../../tsparticles-interaction-particles-links/esm/plugin.js", "../../tsparticles-interaction-particles-links/esm/index.js", "../../tsparticles-shape-polygon/esm/PolygonDrawerBase.js", "../../tsparticles-shape-polygon/esm/PolygonDrawer.js", "../../tsparticles-shape-polygon/esm/TriangleDrawer.js", "../../tsparticles-shape-polygon/esm/index.js", "../../tsparticles-updater-rotate/esm/Options/Classes/RotateAnimation.js", "../../tsparticles-updater-rotate/esm/Options/Classes/Rotate.js", "../../tsparticles-updater-rotate/esm/RotateUpdater.js", "../../tsparticles-updater-rotate/esm/index.js", "../../tsparticles-shape-square/esm/SquareDrawer.js", "../../tsparticles-shape-square/esm/index.js", "../../tsparticles-shape-star/esm/StarDrawer.js", "../../tsparticles-shape-star/esm/index.js", "../../tsparticles-updater-stroke-color/esm/Utils.js", "../../tsparticles-updater-stroke-color/esm/StrokeColorUpdater.js", "../../tsparticles-updater-stroke-color/esm/index.js", "../../tsparticles-shape-text/esm/TextDrawer.js", "../../tsparticles-shape-text/esm/index.js", "../../tsparticles-slim/esm/index.js"], "sourcesContent": ["import { tsParticles } from \"tsparticles-engine\";\nexport class Particles {\n    static init(options) {\n        const particles = new Particles(), selector = options.selector;\n        if (!selector) {\n            throw new Error(\"No selector provided\");\n        }\n        const el = document.querySelector(selector);\n        if (!el) {\n            throw new Error(\"No element found for selector\");\n        }\n        tsParticles\n            .set(selector.replace(\".\", \"\").replace(\"!\", \"\"), el, {\n            fullScreen: {\n                enable: false,\n            },\n            particles: {\n                color: {\n                    value: options.color ?? \"!000000\",\n                },\n                links: {\n                    color: \"random\",\n                    distance: options.minDistance ?? 120,\n                    enable: options.connectParticles ?? false,\n                },\n                move: {\n                    enable: true,\n                    speed: options.speed ?? 0.5,\n                },\n                number: {\n                    value: options.maxParticles ?? 100,\n                },\n                size: {\n                    value: { min: 1, max: options.sizeVariations ?? 3 },\n                },\n            },\n            responsive: options.responsive?.map((responsive) => ({\n                maxWidth: responsive.breakpoint,\n                options: {\n                    particles: {\n                        color: {\n                            value: responsive.options?.color,\n                        },\n                        links: {\n                            distance: responsive.options?.minDistance,\n                            enable: responsive.options?.connectParticles,\n                        },\n                        number: {\n                            value: options.maxParticles,\n                        },\n                        move: {\n                            enable: true,\n                            speed: responsive.options?.speed,\n                        },\n                        size: {\n                            value: responsive.options?.sizeVariations,\n                        },\n                    },\n                },\n            })),\n        })\n            .then((container) => {\n            particles._container = container;\n        });\n        return particles;\n    }\n    destroy() {\n        const container = this._container;\n        container && container.destroy();\n    }\n    pauseAnimation() {\n        const container = this._container;\n        container && container.pause();\n    }\n    resumeAnimation() {\n        const container = this._container;\n        container && container.play();\n    }\n}\n", "const initParticlesJS = (engine) => {\n    const particlesJS = (tagId, options) => {\n        return engine.load(tagId, options);\n    };\n    particlesJS.load = (tagId, pathConfigJson, callback) => {\n        engine\n            .loadJSON(tagId, pathConfigJson)\n            .then((container) => {\n            if (container) {\n                callback(container);\n            }\n        })\n            .catch(() => {\n            callback(undefined);\n        });\n    };\n    particlesJS.setOnClickHandler = (callback) => {\n        engine.setOnClickHandler(callback);\n    };\n    const pJSDom = engine.dom();\n    return { particlesJS, pJSDom };\n};\nexport { initParticlesJS };\n", "import { Particles } from \"./marcbruederlin/Particles\";\nimport { initParticlesJS } from \"./VincentGarreau/particles\";\nconst initPjs = (engine) => {\n    const { particlesJS, pJSDom } = initParticlesJS(engine);\n    window.particlesJS = particlesJS;\n    window.pJSDom = pJSDom;\n    window.Particles = Particles;\n    return { particlesJS, pJSDom, Particles };\n};\nexport { initPjs };\n", "import { clamp, getDistances, getRandom, } from \"tsparticles-engine\";\nexport function applyDistance(particle) {\n    const initialPosition = particle.initialPosition, { dx, dy } = getDistances(initialPosition, particle.position), dxFixed = Math.abs(dx), dyFixed = Math.abs(dy), { maxDistance } = particle.retina, hDistance = maxDistance.horizontal, vDistance = maxDistance.vertical;\n    if (!hDistance && !vDistance) {\n        return;\n    }\n    if (((hDistance && dxFixed >= hDistance) || (vDistance && dyFixed >= vDistance)) && !particle.misplaced) {\n        particle.misplaced = (!!hDistance && dxFixed > hDistance) || (!!vDistance && dyFixed > vDistance);\n        if (hDistance) {\n            particle.velocity.x = particle.velocity.y / 2 - particle.velocity.x;\n        }\n        if (vDistance) {\n            particle.velocity.y = particle.velocity.x / 2 - particle.velocity.y;\n        }\n    }\n    else if ((!hDistance || dxFixed < hDistance) && (!vDistance || dyFixed < vDistance) && particle.misplaced) {\n        particle.misplaced = false;\n    }\n    else if (particle.misplaced) {\n        const pos = particle.position, vel = particle.velocity;\n        if (hDistance && ((pos.x < initialPosition.x && vel.x < 0) || (pos.x > initialPosition.x && vel.x > 0))) {\n            vel.x *= -getRandom();\n        }\n        if (vDistance && ((pos.y < initialPosition.y && vel.y < 0) || (pos.y > initialPosition.y && vel.y > 0))) {\n            vel.y *= -getRandom();\n        }\n    }\n}\nexport function move(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta) {\n    applyPath(particle, delta);\n    const gravityOptions = particle.gravity, gravityFactor = gravityOptions?.enable && gravityOptions.inverse ? -1 : 1;\n    if (moveDrift && moveSpeed) {\n        particle.velocity.x += (moveDrift * delta.factor) / (60 * moveSpeed);\n    }\n    if (gravityOptions?.enable && moveSpeed) {\n        particle.velocity.y += (gravityFactor * (gravityOptions.acceleration * delta.factor)) / (60 * moveSpeed);\n    }\n    const decay = particle.moveDecay;\n    particle.velocity.multTo(decay);\n    const velocity = particle.velocity.mult(moveSpeed);\n    if (gravityOptions?.enable &&\n        maxSpeed > 0 &&\n        ((!gravityOptions.inverse && velocity.y >= 0 && velocity.y >= maxSpeed) ||\n            (gravityOptions.inverse && velocity.y <= 0 && velocity.y <= -maxSpeed))) {\n        velocity.y = gravityFactor * maxSpeed;\n        if (moveSpeed) {\n            particle.velocity.y = velocity.y / moveSpeed;\n        }\n    }\n    const zIndexOptions = particle.options.zIndex, zVelocityFactor = (1 - particle.zIndexFactor) ** zIndexOptions.velocityRate;\n    velocity.multTo(zVelocityFactor);\n    const { position } = particle;\n    position.addTo(velocity);\n    if (moveOptions.vibrate) {\n        position.x += Math.sin(position.x * Math.cos(position.y));\n        position.y += Math.cos(position.y * Math.sin(position.x));\n    }\n}\nexport function spin(particle, moveSpeed) {\n    const container = particle.container;\n    if (!particle.spin) {\n        return;\n    }\n    const updateFunc = {\n        x: particle.spin.direction === \"clockwise\" ? Math.cos : Math.sin,\n        y: particle.spin.direction === \"clockwise\" ? Math.sin : Math.cos,\n    };\n    particle.position.x = particle.spin.center.x + particle.spin.radius * updateFunc.x(particle.spin.angle);\n    particle.position.y = particle.spin.center.y + particle.spin.radius * updateFunc.y(particle.spin.angle);\n    particle.spin.radius += particle.spin.acceleration;\n    const maxCanvasSize = Math.max(container.canvas.size.width, container.canvas.size.height);\n    if (particle.spin.radius > maxCanvasSize / 2) {\n        particle.spin.radius = maxCanvasSize / 2;\n        particle.spin.acceleration *= -1;\n    }\n    else if (particle.spin.radius < 0) {\n        particle.spin.radius = 0;\n        particle.spin.acceleration *= -1;\n    }\n    particle.spin.angle += (moveSpeed / 100) * (1 - particle.spin.radius / maxCanvasSize);\n}\nexport function applyPath(particle, delta) {\n    const particlesOptions = particle.options, pathOptions = particlesOptions.move.path, pathEnabled = pathOptions.enable;\n    if (!pathEnabled) {\n        return;\n    }\n    if (particle.lastPathTime <= particle.pathDelay) {\n        particle.lastPathTime += delta.value;\n        return;\n    }\n    const path = particle.pathGenerator?.generate(particle, delta);\n    if (path) {\n        particle.velocity.addTo(path);\n    }\n    if (pathOptions.clamp) {\n        particle.velocity.x = clamp(particle.velocity.x, -1, 1);\n        particle.velocity.y = clamp(particle.velocity.y, -1, 1);\n    }\n    particle.lastPathTime -= particle.pathDelay;\n}\nexport function getProximitySpeedFactor(particle) {\n    return particle.slow.inRange ? particle.slow.factor : 1;\n}\n", "import { getDistance, getRangeMax, getRangeValue, } from \"tsparticles-engine\";\nimport { applyDistance, getProximitySpeedFactor, move, spin } from \"./Utils\";\nconst diffFactor = 2;\nexport class BaseMover {\n    constructor() {\n        this._initSpin = (particle) => {\n            const container = particle.container, options = particle.options, spinOptions = options.move.spin;\n            if (!spinOptions.enable) {\n                return;\n            }\n            const spinPos = spinOptions.position ?? { x: 50, y: 50 }, spinCenter = {\n                x: (spinPos.x / 100) * container.canvas.size.width,\n                y: (spinPos.y / 100) * container.canvas.size.height,\n            }, pos = particle.getPosition(), distance = getDistance(pos, spinCenter), spinAcceleration = getRangeValue(spinOptions.acceleration);\n            particle.retina.spinAcceleration = spinAcceleration * container.retina.pixelRatio;\n            particle.spin = {\n                center: spinCenter,\n                direction: particle.velocity.x >= 0 ? \"clockwise\" : \"counter-clockwise\",\n                angle: particle.velocity.angle,\n                radius: distance,\n                acceleration: particle.retina.spinAcceleration,\n            };\n        };\n    }\n    init(particle) {\n        const options = particle.options, gravityOptions = options.move.gravity;\n        particle.gravity = {\n            enable: gravityOptions.enable,\n            acceleration: getRangeValue(gravityOptions.acceleration),\n            inverse: gravityOptions.inverse,\n        };\n        this._initSpin(particle);\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && particle.options.move.enable;\n    }\n    move(particle, delta) {\n        const particleOptions = particle.options, moveOptions = particleOptions.move;\n        if (!moveOptions.enable) {\n            return;\n        }\n        const container = particle.container, pxRatio = container.retina.pixelRatio, slowFactor = getProximitySpeedFactor(particle), baseSpeed = (particle.retina.moveSpeed ??= getRangeValue(moveOptions.speed) * pxRatio) *\n            container.retina.reduceFactor, moveDrift = (particle.retina.moveDrift ??= getRangeValue(particle.options.move.drift) * pxRatio), maxSize = getRangeMax(particleOptions.size.value) * pxRatio, sizeFactor = moveOptions.size ? particle.getRadius() / maxSize : 1, moveSpeed = (baseSpeed * sizeFactor * slowFactor * (delta.factor || 1)) / diffFactor, maxSpeed = particle.retina.maxSpeed ?? container.retina.maxSpeed;\n        if (moveOptions.spin.enable) {\n            spin(particle, moveSpeed);\n        }\n        else {\n            move(particle, moveOptions, moveSpeed, maxSpeed, moveDrift, delta);\n        }\n        applyDistance(particle);\n    }\n}\n", "import { BaseMover } from \"./BaseMover\";\nexport async function loadBaseMover(engine, refresh = true) {\n    await engine.addMover(\"base\", () => new BaseMover(), refresh);\n}\n", "import { isObject } from \"tsparticles-engine\";\nexport class CircleDrawer {\n    draw(context, particle, radius) {\n        if (!particle.circleRange) {\n            particle.circleRange = { min: 0, max: Math.PI * 2 };\n        }\n        const circleRange = particle.circleRange;\n        context.arc(0, 0, radius, circleRange.min, circleRange.max, false);\n    }\n    getSidesCount() {\n        return 12;\n    }\n    particleInit(container, particle) {\n        const shapeData = particle.shapeData, angle = shapeData?.angle ?? {\n            max: 360,\n            min: 0,\n        };\n        particle.circleRange = !isObject(angle)\n            ? {\n                min: 0,\n                max: (angle * Math.PI) / 180,\n            }\n            : { min: (angle.min * Math.PI) / 180, max: (angle.max * Math.PI) / 180 };\n    }\n}\n", "import { CircleDrawer } from \"./CircleDrawer\";\nexport async function loadCircleShape(engine, refresh = true) {\n    await engine.addShape(\"circle\", new CircleDrawer(), refresh);\n}\n", "import { randomInRange, } from \"tsparticles-engine\";\nfunction updateColorValue(delta, colorValue, valueAnimation, max, decrease) {\n    if (!colorValue ||\n        !valueAnimation.enable ||\n        ((colorValue.maxLoops ?? 0) > 0 && (colorValue.loops ?? 0) > (colorValue.maxLoops ?? 0))) {\n        return;\n    }\n    if (!colorValue.time) {\n        colorValue.time = 0;\n    }\n    if ((colorValue.delayTime ?? 0) > 0 && colorValue.time < (colorValue.delayTime ?? 0)) {\n        colorValue.time += delta.value;\n    }\n    if ((colorValue.delayTime ?? 0) > 0 && colorValue.time < (colorValue.delayTime ?? 0)) {\n        return;\n    }\n    const offset = randomInRange(valueAnimation.offset), velocity = (colorValue.velocity ?? 0) * delta.factor + offset * 3.6, decay = colorValue.decay ?? 1;\n    if (!decrease || colorValue.status === \"increasing\") {\n        colorValue.value += velocity;\n        if (colorValue.value > max) {\n            if (!colorValue.loops) {\n                colorValue.loops = 0;\n            }\n            colorValue.loops++;\n            if (decrease) {\n                colorValue.status = \"decreasing\";\n                colorValue.value -= colorValue.value % max;\n            }\n        }\n    }\n    else {\n        colorValue.value -= velocity;\n        if (colorValue.value < 0) {\n            if (!colorValue.loops) {\n                colorValue.loops = 0;\n            }\n            colorValue.loops++;\n            colorValue.status = \"increasing\";\n            colorValue.value += colorValue.value;\n        }\n    }\n    if (colorValue.velocity && decay !== 1) {\n        colorValue.velocity *= decay;\n    }\n    if (colorValue.value > max) {\n        colorValue.value %= max;\n    }\n}\nexport function updateColor(particle, delta) {\n    const { h: hAnimation, s: sAnimation, l: lAnimation } = particle.options.color.animation, { color } = particle;\n    if (!color) {\n        return;\n    }\n    const { h, s, l } = color;\n    if (h) {\n        updateColorValue(delta, h, hAnimation, 360, false);\n    }\n    if (s) {\n        updateColorValue(delta, s, sAnimation, 100, true);\n    }\n    if (l) {\n        updateColorValue(delta, l, lAnimation, 100, true);\n    }\n}\n", "import { getHslAnimationFromHsl, rangeColorToHsl, } from \"tsparticles-engine\";\nimport { updateColor } from \"./Utils\";\nexport class ColorUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const hslColor = rangeColorToHsl(particle.options.color, particle.id, particle.options.reduceDuplicates);\n        if (hslColor) {\n            particle.color = getHslAnimationFromHsl(hslColor, particle.options.color.animation, this.container.retina.reduceFactor);\n        }\n    }\n    isEnabled(particle) {\n        const { h: hAnimation, s: sAnimation, l: lAnimation } = particle.options.color.animation, { color } = particle;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            ((color?.h.value !== undefined && hAnimation.enable) ||\n                (color?.s.value !== undefined && sAnimation.enable) ||\n                (color?.l.value !== undefined && lAnimation.enable)));\n    }\n    update(particle, delta) {\n        updateColor(particle, delta);\n    }\n}\n", "import { ColorUpdater } from \"./ColorUpdater\";\nexport async function loadColorUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"color\", (container) => new ColorUpdater(container), refresh);\n}\n", "import { clamp } from \"tsparticles-engine\";\nfunction checkDestroy(particle, value, minValue, maxValue) {\n    switch (particle.options.opacity.animation.destroy) {\n        case \"max\":\n            if (value >= maxValue) {\n                particle.destroy();\n            }\n            break;\n        case \"min\":\n            if (value <= minValue) {\n                particle.destroy();\n            }\n            break;\n    }\n}\nexport function updateOpacity(particle, delta) {\n    const data = particle.opacity;\n    if (particle.destroyed || !data?.enable || ((data.maxLoops ?? 0) > 0 && (data.loops ?? 0) > (data.maxLoops ?? 0))) {\n        return;\n    }\n    const minValue = data.min, maxValue = data.max, decay = data.decay ?? 1;\n    if (!data.time) {\n        data.time = 0;\n    }\n    if ((data.delayTime ?? 0) > 0 && data.time < (data.delayTime ?? 0)) {\n        data.time += delta.value;\n    }\n    if ((data.delayTime ?? 0) > 0 && data.time < (data.delayTime ?? 0)) {\n        return;\n    }\n    switch (data.status) {\n        case \"increasing\":\n            if (data.value >= maxValue) {\n                data.status = \"decreasing\";\n                if (!data.loops) {\n                    data.loops = 0;\n                }\n                data.loops++;\n            }\n            else {\n                data.value += (data.velocity ?? 0) * delta.factor;\n            }\n            break;\n        case \"decreasing\":\n            if (data.value <= minValue) {\n                data.status = \"increasing\";\n                if (!data.loops) {\n                    data.loops = 0;\n                }\n                data.loops++;\n            }\n            else {\n                data.value -= (data.velocity ?? 0) * delta.factor;\n            }\n            break;\n    }\n    if (data.velocity && data.decay !== 1) {\n        data.velocity *= decay;\n    }\n    checkDestroy(particle, data.value, minValue, maxValue);\n    if (!particle.destroyed) {\n        data.value = clamp(data.value, minValue, maxValue);\n    }\n}\n", "import { getRandom, getRangeValue, initParticleNumericAnimationValue, } from \"tsparticles-engine\";\nimport { updateOpacity } from \"./Utils\";\nexport class OpacityUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const opacityOptions = particle.options.opacity;\n        particle.opacity = initParticleNumericAnimationValue(opacityOptions, 1);\n        const opacityAnimation = opacityOptions.animation;\n        if (opacityAnimation.enable) {\n            particle.opacity.velocity =\n                (getRangeValue(opacityAnimation.speed) / 100) * this.container.retina.reduceFactor;\n            if (!opacityAnimation.sync) {\n                particle.opacity.velocity *= getRandom();\n            }\n        }\n    }\n    isEnabled(particle) {\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            !!particle.opacity &&\n            particle.opacity.enable &&\n            ((particle.opacity.maxLoops ?? 0) <= 0 ||\n                ((particle.opacity.maxLoops ?? 0) > 0 &&\n                    (particle.opacity.loops ?? 0) < (particle.opacity.maxLoops ?? 0))));\n    }\n    reset(particle) {\n        if (particle.opacity) {\n            particle.opacity.time = 0;\n            particle.opacity.loops = 0;\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateOpacity(particle, delta);\n    }\n}\n", "import { OpacityUpdater } from \"./OpacityUpdater\";\nexport async function loadOpacityUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"opacity\", (container) => new OpacityUpdater(container), refresh);\n}\n", "import { getValue } from \"tsparticles-engine\";\nexport function bounceHorizontal(data) {\n    if ((data.outMode !== \"bounce\" &&\n        data.outMode !== \"bounce-horizontal\" &&\n        data.outMode !== \"bounceHorizontal\" &&\n        data.outMode !== \"split\") ||\n        (data.direction !== \"left\" && data.direction !== \"right\")) {\n        return;\n    }\n    if (data.bounds.right < 0 && data.direction === \"left\") {\n        data.particle.position.x = data.size + data.offset.x;\n    }\n    else if (data.bounds.left > data.canvasSize.width && data.direction === \"right\") {\n        data.particle.position.x = data.canvasSize.width - data.size - data.offset.x;\n    }\n    const velocity = data.particle.velocity.x;\n    let bounced = false;\n    if ((data.direction === \"right\" && data.bounds.right >= data.canvasSize.width && velocity > 0) ||\n        (data.direction === \"left\" && data.bounds.left <= 0 && velocity < 0)) {\n        const newVelocity = getValue(data.particle.options.bounce.horizontal);\n        data.particle.velocity.x *= -newVelocity;\n        bounced = true;\n    }\n    if (!bounced) {\n        return;\n    }\n    const minPos = data.offset.x + data.size;\n    if (data.bounds.right >= data.canvasSize.width && data.direction === \"right\") {\n        data.particle.position.x = data.canvasSize.width - minPos;\n    }\n    else if (data.bounds.left <= 0 && data.direction === \"left\") {\n        data.particle.position.x = minPos;\n    }\n    if (data.outMode === \"split\") {\n        data.particle.destroy();\n    }\n}\nexport function bounceVertical(data) {\n    if ((data.outMode !== \"bounce\" &&\n        data.outMode !== \"bounce-vertical\" &&\n        data.outMode !== \"bounceVertical\" &&\n        data.outMode !== \"split\") ||\n        (data.direction !== \"bottom\" && data.direction !== \"top\")) {\n        return;\n    }\n    if (data.bounds.bottom < 0 && data.direction === \"top\") {\n        data.particle.position.y = data.size + data.offset.y;\n    }\n    else if (data.bounds.top > data.canvasSize.height && data.direction === \"bottom\") {\n        data.particle.position.y = data.canvasSize.height - data.size - data.offset.y;\n    }\n    const velocity = data.particle.velocity.y;\n    let bounced = false;\n    if ((data.direction === \"bottom\" && data.bounds.bottom >= data.canvasSize.height && velocity > 0) ||\n        (data.direction === \"top\" && data.bounds.top <= 0 && velocity < 0)) {\n        const newVelocity = getValue(data.particle.options.bounce.vertical);\n        data.particle.velocity.y *= -newVelocity;\n        bounced = true;\n    }\n    if (!bounced) {\n        return;\n    }\n    const minPos = data.offset.y + data.size;\n    if (data.bounds.bottom >= data.canvasSize.height && data.direction === \"bottom\") {\n        data.particle.position.y = data.canvasSize.height - minPos;\n    }\n    else if (data.bounds.top <= 0 && data.direction === \"top\") {\n        data.particle.position.y = minPos;\n    }\n    if (data.outMode === \"split\") {\n        data.particle.destroy();\n    }\n}\n", "import { calculateBounds, } from \"tsparticles-engine\";\nimport { bounceHorizontal, bounceVertical } from \"./Utils\";\nexport class BounceOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\n            \"bounce\",\n            \"bounce-vertical\",\n            \"bounce-horizontal\",\n            \"bounceVertical\",\n            \"bounceHorizontal\",\n            \"split\",\n        ];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        let handled = false;\n        for (const [, plugin] of container.plugins) {\n            if (plugin.particleBounce !== undefined) {\n                handled = plugin.particleBounce(particle, delta, direction);\n            }\n            if (handled) {\n                break;\n            }\n        }\n        if (handled) {\n            return;\n        }\n        const pos = particle.getPosition(), offset = particle.offset, size = particle.getRadius(), bounds = calculateBounds(pos, size), canvasSize = container.canvas.size;\n        bounceHorizontal({ particle, outMode, direction, bounds, canvasSize, offset, size });\n        bounceVertical({ particle, outMode, direction, bounds, canvasSize, offset, size });\n    }\n}\n", "import { Vector, getDistances, isPointInside, } from \"tsparticles-engine\";\nexport class DestroyOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\"destroy\"];\n    }\n    update(particle, direction, _delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        switch (particle.outType) {\n            case \"normal\":\n            case \"outside\":\n                if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n                    return;\n                }\n                break;\n            case \"inside\": {\n                const { dx, dy } = getDistances(particle.position, particle.moveCenter);\n                const { x: vx, y: vy } = particle.velocity;\n                if ((vx < 0 && dx > particle.moveCenter.radius) ||\n                    (vy < 0 && dy > particle.moveCenter.radius) ||\n                    (vx >= 0 && dx < -particle.moveCenter.radius) ||\n                    (vy >= 0 && dy < -particle.moveCenter.radius)) {\n                    return;\n                }\n                break;\n            }\n        }\n        container.particles.remove(particle, undefined, true);\n    }\n}\n", "import { Vector, isPointInside, } from \"tsparticles-engine\";\nexport class NoneOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\"none\"];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        if ((particle.options.move.distance.horizontal &&\n            (direction === \"left\" || direction === \"right\")) ||\n            (particle.options.move.distance.vertical &&\n                (direction === \"top\" || direction === \"bottom\"))) {\n            return;\n        }\n        const gravityOptions = particle.options.move.gravity, container = this.container;\n        const canvasSize = container.canvas.size;\n        const pRadius = particle.getRadius();\n        if (!gravityOptions.enable) {\n            if ((particle.velocity.y > 0 && particle.position.y <= canvasSize.height + pRadius) ||\n                (particle.velocity.y < 0 && particle.position.y >= -pRadius) ||\n                (particle.velocity.x > 0 && particle.position.x <= canvasSize.width + pRadius) ||\n                (particle.velocity.x < 0 && particle.position.x >= -pRadius)) {\n                return;\n            }\n            if (!isPointInside(particle.position, container.canvas.size, Vector.origin, pRadius, direction)) {\n                container.particles.remove(particle);\n            }\n        }\n        else {\n            const position = particle.position;\n            if ((!gravityOptions.inverse &&\n                position.y > canvasSize.height + pRadius &&\n                direction === \"bottom\") ||\n                (gravityOptions.inverse && position.y < -pRadius && direction === \"top\")) {\n                container.particles.remove(particle);\n            }\n        }\n    }\n}\n", "import { Vector, calculateBounds, getDistances, getRandom, isPointInside, randomInRange, } from \"tsparticles-engine\";\nexport class OutOutMode {\n    constructor(container) {\n        this.container = container;\n        this.modes = [\"out\"];\n    }\n    update(particle, direction, delta, outMode) {\n        if (!this.modes.includes(outMode)) {\n            return;\n        }\n        const container = this.container;\n        switch (particle.outType) {\n            case \"inside\": {\n                const { x: vx, y: vy } = particle.velocity;\n                const circVec = Vector.origin;\n                circVec.length = particle.moveCenter.radius;\n                circVec.angle = particle.velocity.angle + Math.PI;\n                circVec.addTo(Vector.create(particle.moveCenter));\n                const { dx, dy } = getDistances(particle.position, circVec);\n                if ((vx <= 0 && dx >= 0) || (vy <= 0 && dy >= 0) || (vx >= 0 && dx <= 0) || (vy >= 0 && dy <= 0)) {\n                    return;\n                }\n                particle.position.x = Math.floor(randomInRange({\n                    min: 0,\n                    max: container.canvas.size.width,\n                }));\n                particle.position.y = Math.floor(randomInRange({\n                    min: 0,\n                    max: container.canvas.size.height,\n                }));\n                const { dx: newDx, dy: newDy } = getDistances(particle.position, particle.moveCenter);\n                particle.direction = Math.atan2(-newDy, -newDx);\n                particle.velocity.angle = particle.direction;\n                break;\n            }\n            default: {\n                if (isPointInside(particle.position, container.canvas.size, Vector.origin, particle.getRadius(), direction)) {\n                    return;\n                }\n                switch (particle.outType) {\n                    case \"outside\": {\n                        particle.position.x =\n                            Math.floor(randomInRange({\n                                min: -particle.moveCenter.radius,\n                                max: particle.moveCenter.radius,\n                            })) + particle.moveCenter.x;\n                        particle.position.y =\n                            Math.floor(randomInRange({\n                                min: -particle.moveCenter.radius,\n                                max: particle.moveCenter.radius,\n                            })) + particle.moveCenter.y;\n                        const { dx, dy } = getDistances(particle.position, particle.moveCenter);\n                        if (particle.moveCenter.radius) {\n                            particle.direction = Math.atan2(dy, dx);\n                            particle.velocity.angle = particle.direction;\n                        }\n                        break;\n                    }\n                    case \"normal\": {\n                        const wrap = particle.options.move.warp, canvasSize = container.canvas.size, newPos = {\n                            bottom: canvasSize.height + particle.getRadius() + particle.offset.y,\n                            left: -particle.getRadius() - particle.offset.x,\n                            right: canvasSize.width + particle.getRadius() + particle.offset.x,\n                            top: -particle.getRadius() - particle.offset.y,\n                        }, sizeValue = particle.getRadius(), nextBounds = calculateBounds(particle.position, sizeValue);\n                        if (direction === \"right\" &&\n                            nextBounds.left > canvasSize.width + particle.offset.x) {\n                            particle.position.x = newPos.left;\n                            particle.initialPosition.x = particle.position.x;\n                            if (!wrap) {\n                                particle.position.y = getRandom() * canvasSize.height;\n                                particle.initialPosition.y = particle.position.y;\n                            }\n                        }\n                        else if (direction === \"left\" && nextBounds.right < -particle.offset.x) {\n                            particle.position.x = newPos.right;\n                            particle.initialPosition.x = particle.position.x;\n                            if (!wrap) {\n                                particle.position.y = getRandom() * canvasSize.height;\n                                particle.initialPosition.y = particle.position.y;\n                            }\n                        }\n                        if (direction === \"bottom\" &&\n                            nextBounds.top > canvasSize.height + particle.offset.y) {\n                            if (!wrap) {\n                                particle.position.x = getRandom() * canvasSize.width;\n                                particle.initialPosition.x = particle.position.x;\n                            }\n                            particle.position.y = newPos.top;\n                            particle.initialPosition.y = particle.position.y;\n                        }\n                        else if (direction === \"top\" && nextBounds.bottom < -particle.offset.y) {\n                            if (!wrap) {\n                                particle.position.x = getRandom() * canvasSize.width;\n                                particle.initialPosition.x = particle.position.x;\n                            }\n                            particle.position.y = newPos.bottom;\n                            particle.initialPosition.y = particle.position.y;\n                        }\n                        break;\n                    }\n                }\n                break;\n            }\n        }\n    }\n}\n", "import { BounceOutMode } from \"./BounceOutMode\";\nimport { DestroyOutMode } from \"./DestroyOutMode\";\nimport { NoneOutMode } from \"./NoneOutMode\";\nimport { OutOutMode } from \"./OutOutMode\";\nexport class OutOfCanvasUpdater {\n    constructor(container) {\n        this.container = container;\n        this._updateOutMode = (particle, delta, outMode, direction) => {\n            for (const updater of this.updaters) {\n                updater.update(particle, direction, delta, outMode);\n            }\n        };\n        this.updaters = [\n            new BounceOutMode(container),\n            new DestroyOutMode(container),\n            new OutOutMode(container),\n            new NoneOutMode(container),\n        ];\n    }\n    init() {\n    }\n    isEnabled(particle) {\n        return !particle.destroyed && !particle.spawning;\n    }\n    update(particle, delta) {\n        const outModes = particle.options.move.outModes;\n        this._updateOutMode(particle, delta, outModes.bottom ?? outModes.default, \"bottom\");\n        this._updateOutMode(particle, delta, outModes.left ?? outModes.default, \"left\");\n        this._updateOutMode(particle, delta, outModes.right ?? outModes.default, \"right\");\n        this._updateOutMode(particle, delta, outModes.top ?? outModes.default, \"top\");\n    }\n}\n", "import { OutOfCanvasUpdater } from \"./OutOfCanvasUpdater\";\nexport async function loadOutModesUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"outModes\", (container) => new OutOfCanvasUpdater(container), refresh);\n}\n", "import { clamp } from \"tsparticles-engine\";\nfunction checkDestroy(particle, value, minValue, maxValue) {\n    switch (particle.options.size.animation.destroy) {\n        case \"max\":\n            if (value >= maxValue) {\n                particle.destroy();\n            }\n            break;\n        case \"min\":\n            if (value <= minValue) {\n                particle.destroy();\n            }\n            break;\n    }\n}\nexport function updateSize(particle, delta) {\n    const data = particle.size;\n    if (particle.destroyed ||\n        !data ||\n        !data.enable ||\n        ((data.maxLoops ?? 0) > 0 && (data.loops ?? 0) > (data.maxLoops ?? 0))) {\n        return;\n    }\n    const sizeVelocity = (data.velocity ?? 0) * delta.factor, minValue = data.min, maxValue = data.max, decay = data.decay ?? 1;\n    if (!data.time) {\n        data.time = 0;\n    }\n    if ((data.delayTime ?? 0) > 0 && data.time < (data.delayTime ?? 0)) {\n        data.time += delta.value;\n    }\n    if ((data.delayTime ?? 0) > 0 && data.time < (data.delayTime ?? 0)) {\n        return;\n    }\n    switch (data.status) {\n        case \"increasing\":\n            if (data.value >= maxValue) {\n                data.status = \"decreasing\";\n                if (!data.loops) {\n                    data.loops = 0;\n                }\n                data.loops++;\n            }\n            else {\n                data.value += sizeVelocity;\n            }\n            break;\n        case \"decreasing\":\n            if (data.value <= minValue) {\n                data.status = \"increasing\";\n                if (!data.loops) {\n                    data.loops = 0;\n                }\n                data.loops++;\n            }\n            else {\n                data.value -= sizeVelocity;\n            }\n    }\n    if (data.velocity && decay !== 1) {\n        data.velocity *= decay;\n    }\n    checkDestroy(particle, data.value, minValue, maxValue);\n    if (!particle.destroyed) {\n        data.value = clamp(data.value, minValue, maxValue);\n    }\n}\n", "import { getRandom } from \"tsparticles-engine\";\nimport { updateSize } from \"./Utils\";\nexport class SizeUpdater {\n    init(particle) {\n        const container = particle.container, sizeOptions = particle.options.size, sizeAnimation = sizeOptions.animation;\n        if (sizeAnimation.enable) {\n            particle.size.velocity =\n                ((particle.retina.sizeAnimationSpeed ?? container.retina.sizeAnimationSpeed) / 100) *\n                    container.retina.reduceFactor;\n            if (!sizeAnimation.sync) {\n                particle.size.velocity *= getRandom();\n            }\n        }\n    }\n    isEnabled(particle) {\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            particle.size.enable &&\n            ((particle.size.maxLoops ?? 0) <= 0 ||\n                ((particle.size.maxLoops ?? 0) > 0 && (particle.size.loops ?? 0) < (particle.size.maxLoops ?? 0))));\n    }\n    reset(particle) {\n        particle.size.loops = 0;\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateSize(particle, delta);\n    }\n}\n", "import { SizeUpdater } from \"./SizeUpdater\";\nexport async function loadSizeUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"size\", () => new SizeUpdater(), refresh);\n}\n", "import { loadBaseMover } from \"tsparticles-move-base\";\nimport { loadCircleShape } from \"tsparticles-shape-circle\";\nimport { loadColorUpdater } from \"tsparticles-updater-color\";\nimport { loadOpacityUpdater } from \"tsparticles-updater-opacity\";\nimport { loadOutModesUpdater } from \"tsparticles-updater-out-modes\";\nimport { loadSizeUpdater } from \"tsparticles-updater-size\";\nexport async function loadBasic(engine, refresh = true) {\n    await loadBaseMover(engine, false);\n    await loadCircleShape(engine, false);\n    await loadColorUpdater(engine, false);\n    await loadOpacityUpdater(engine, false);\n    await loadOutModesUpdater(engine, false);\n    await loadSizeUpdater(engine, false);\n    await engine.refresh(refresh);\n}\n", "import { addEasing } from \"tsparticles-engine\";\nexport async function loadEasingQuadPlugin() {\n    addEasing(\"ease-in-quad\", (value) => value ** 2);\n    addEasing(\"ease-out-quad\", (value) => 1 - (1 - value) ** 2);\n    addEasing(\"ease-in-out-quad\", (value) => (value < 0.5 ? 2 * value ** 2 : 1 - (-2 * value + 2) ** 2 / 2));\n}\n", "export class Attract {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.easing = \"ease-out-quad\";\n        this.factor = 1;\n        this.maxSpeed = 50;\n        this.speed = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.easing !== undefined) {\n            this.easing = data.easing;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = data.maxSpeed;\n        }\n        if (data.speed !== undefined) {\n            this.speed = data.speed;\n        }\n    }\n}\n", "import { Circle, ExternalInteractorBase, Vector, clamp, getDistances, getEasing, isInArray, mouseMoveEvent, } from \"tsparticles-engine\";\nimport { Attract } from \"./Options/Classes/Attract\";\nexport class Attractor extends ExternalInteractorBase {\n    constructor(engine, container) {\n        super(container);\n        this._clickAttract = () => {\n            const container = this.container;\n            if (!container.attract) {\n                container.attract = { particles: [] };\n            }\n            const { attract } = container;\n            if (!attract.finish) {\n                if (!attract.count) {\n                    attract.count = 0;\n                }\n                attract.count++;\n                if (attract.count === container.particles.count) {\n                    attract.finish = true;\n                }\n            }\n            if (attract.clicking) {\n                const mousePos = container.interactivity.mouse.clickPosition, attractRadius = container.retina.attractModeDistance;\n                if (!attractRadius || attractRadius < 0 || !mousePos) {\n                    return;\n                }\n                this._processAttract(mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius));\n            }\n            else if (attract.clicking === false) {\n                attract.particles = [];\n            }\n            return;\n        };\n        this._hoverAttract = () => {\n            const container = this.container, mousePos = container.interactivity.mouse.position, attractRadius = container.retina.attractModeDistance;\n            if (!attractRadius || attractRadius < 0 || !mousePos) {\n                return;\n            }\n            this._processAttract(mousePos, attractRadius, new Circle(mousePos.x, mousePos.y, attractRadius));\n        };\n        this._processAttract = (position, attractRadius, area) => {\n            const container = this.container, attractOptions = container.actualOptions.interactivity.modes.attract;\n            if (!attractOptions) {\n                return;\n            }\n            const query = container.particles.quadTree.query(area, (p) => this.isEnabled(p));\n            for (const particle of query) {\n                const { dx, dy, distance } = getDistances(particle.position, position);\n                const velocity = attractOptions.speed * attractOptions.factor;\n                const attractFactor = clamp(getEasing(attractOptions.easing)(1 - distance / attractRadius) * velocity, 0, attractOptions.maxSpeed);\n                const normVec = Vector.create(distance === 0 ? velocity : (dx / distance) * attractFactor, distance === 0 ? velocity : (dy / distance) * attractFactor);\n                particle.position.subFrom(normVec);\n            }\n        };\n        this._engine = engine;\n        if (!container.attract) {\n            container.attract = { particles: [] };\n        }\n        this.handleClickMode = (mode) => {\n            const options = this.container.actualOptions, attract = options.interactivity.modes.attract;\n            if (!attract || mode !== \"attract\") {\n                return;\n            }\n            if (!container.attract) {\n                container.attract = { particles: [] };\n            }\n            container.attract.clicking = true;\n            container.attract.count = 0;\n            for (const particle of container.attract.particles) {\n                if (!this.isEnabled(particle)) {\n                    continue;\n                }\n                particle.velocity.setTo(particle.initialVelocity);\n            }\n            container.attract.particles = [];\n            container.attract.finish = false;\n            setTimeout(() => {\n                if (container.destroyed) {\n                    return;\n                }\n                if (!container.attract) {\n                    container.attract = { particles: [] };\n                }\n                container.attract.clicking = false;\n            }, attract.duration * 1000);\n        };\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, attract = container.actualOptions.interactivity.modes.attract;\n        if (!attract) {\n            return;\n        }\n        container.retina.attractModeDistance = attract.distance * container.retina.pixelRatio;\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions, mouseMoveStatus = container.interactivity.status === mouseMoveEvent, events = options.interactivity.events, hoverEnabled = events.onHover.enable, hoverMode = events.onHover.mode, clickEnabled = events.onClick.enable, clickMode = events.onClick.mode;\n        if (mouseMoveStatus && hoverEnabled && isInArray(\"attract\", hoverMode)) {\n            this._hoverAttract();\n        }\n        else if (clickEnabled && isInArray(\"attract\", clickMode)) {\n            this._clickAttract();\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? options.interactivity).events;\n        if ((!mouse.position || !events.onHover.enable) && (!mouse.clickPosition || !events.onClick.enable)) {\n            return false;\n        }\n        const hoverMode = events.onHover.mode, clickMode = events.onClick.mode;\n        return isInArray(\"attract\", hoverMode) || isInArray(\"attract\", clickMode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.attract) {\n            options.attract = new Attract();\n        }\n        for (const source of sources) {\n            options.attract.load(source?.attract);\n        }\n    }\n    reset() {\n    }\n}\n", "import { Attractor } from \"./Attractor\";\nexport async function loadExternalAttractInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalAttract\", (container) => new Attractor(engine, container), refresh);\n}\nexport * from \"./Options/Classes/Attract\";\nexport * from \"./Options/Interfaces/IAttract\";\n", "export class Bounce {\n    constructor() {\n        this.distance = 200;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n    }\n}\n", "import { Circle, ExternalInteractorBase, Rectangle, Vector, calculateBounds, circleBounce, circleBounceDataFromParticle, divModeExecute, isDivModeEnabled, isInArray, mouseMoveEvent, rectBounce, } from \"tsparticles-engine\";\nimport { Bounce } from \"./Options/Classes/Bounce\";\nexport class <PERSON>uncer extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this._processBounce = (position, radius, area) => {\n            const query = this.container.particles.quadTree.query(area, (p) => this.isEnabled(p));\n            for (const particle of query) {\n                if (area instanceof Circle) {\n                    circleBounce(circleBounceDataFromParticle(particle), {\n                        position,\n                        radius,\n                        mass: (radius ** 2 * Math.PI) / 2,\n                        velocity: Vector.origin,\n                        factor: Vector.origin,\n                    });\n                }\n                else if (area instanceof Rectangle) {\n                    rectBounce(particle, calculateBounds(position, radius));\n                }\n            }\n        };\n        this._processMouseBounce = () => {\n            const container = this.container, pxRatio = container.retina.pixelRatio, tolerance = 10 * pxRatio, mousePos = container.interactivity.mouse.position, radius = container.retina.bounceModeDistance;\n            if (!radius || radius < 0 || !mousePos) {\n                return;\n            }\n            this._processBounce(mousePos, radius, new Circle(mousePos.x, mousePos.y, radius + tolerance));\n        };\n        this._singleSelectorBounce = (selector, div) => {\n            const container = this.container, query = document.querySelectorAll(selector);\n            if (!query.length) {\n                return;\n            }\n            query.forEach((item) => {\n                const elem = item, pxRatio = container.retina.pixelRatio, pos = {\n                    x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n                    y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio,\n                }, radius = (elem.offsetWidth / 2) * pxRatio, tolerance = 10 * pxRatio, area = div.type === \"circle\"\n                    ? new Circle(pos.x, pos.y, radius + tolerance)\n                    : new Rectangle(elem.offsetLeft * pxRatio - tolerance, elem.offsetTop * pxRatio - tolerance, elem.offsetWidth * pxRatio + tolerance * 2, elem.offsetHeight * pxRatio + tolerance * 2);\n                this._processBounce(pos, radius, area);\n            });\n        };\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, bounce = container.actualOptions.interactivity.modes.bounce;\n        if (!bounce) {\n            return;\n        }\n        container.retina.bounceModeDistance = bounce.distance * container.retina.pixelRatio;\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions, events = options.interactivity.events, mouseMoveStatus = container.interactivity.status === mouseMoveEvent, hoverEnabled = events.onHover.enable, hoverMode = events.onHover.mode, divs = events.onDiv;\n        if (mouseMoveStatus && hoverEnabled && isInArray(\"bounce\", hoverMode)) {\n            this._processMouseBounce();\n        }\n        else {\n            divModeExecute(\"bounce\", divs, (selector, div) => this._singleSelectorBounce(selector, div));\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? options.interactivity).events, divs = events.onDiv;\n        return ((mouse.position && events.onHover.enable && isInArray(\"bounce\", events.onHover.mode)) ||\n            isDivModeEnabled(\"bounce\", divs));\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.bounce) {\n            options.bounce = new Bounce();\n        }\n        for (const source of sources) {\n            options.bounce.load(source?.bounce);\n        }\n    }\n    reset() {\n    }\n}\n", "import { Bouncer } from \"./Bouncer\";\nexport async function loadExternalBounceInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalBounce\", (container) => new Bouncer(container), refresh);\n}\nexport * from \"./Options/Classes/Bounce\";\nexport * from \"./Options/Interfaces/IBounce\";\n", "import { OptionsColor, executeOnSingleOr<PERSON>ultiple, isArray, } from \"tsparticles-engine\";\nexport class BubbleBase {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.mix = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.mix !== undefined) {\n            this.mix = data.mix;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        if (data.color !== undefined) {\n            const sourceColor = isArray(this.color) ? undefined : this.color;\n            this.color = executeOnSingleOrMultiple(data.color, (color) => {\n                return OptionsColor.create(sourceColor, color);\n            });\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n    }\n}\n", "import { executeOnSingleOrMultiple, } from \"tsparticles-engine\";\nimport { BubbleBase } from \"./BubbleBase\";\nexport class BubbleDiv extends BubbleBase {\n    constructor() {\n        super();\n        this.selectors = [];\n    }\n    get ids() {\n        return executeOnSingleOrMultiple(this.selectors, (t) => t.replace(\"#\", \"\"));\n    }\n    set ids(value) {\n        this.selectors = executeOnSingleOrMultiple(value, (t) => `#${t}`);\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.ids !== undefined) {\n            this.ids = data.ids;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n    }\n}\n", "import { executeOnSingleOrMultiple, } from \"tsparticles-engine\";\nimport { BubbleBase } from \"./BubbleBase\";\nimport { BubbleDiv } from \"./BubbleDiv\";\nexport class Bubble extends BubbleBase {\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        this.divs = executeOnSingleOrMultiple(data.divs, (div) => {\n            const tmp = new BubbleDiv();\n            tmp.load(div);\n            return tmp;\n        });\n    }\n}\n", "import { clamp } from \"tsparticles-engine\";\nexport function calculateBubbleValue(particleValue, modeValue, optionsValue, ratio) {\n    if (modeValue >= optionsValue) {\n        const value = particleValue + (modeValue - optionsValue) * ratio;\n        return clamp(value, particleValue, modeValue);\n    }\n    else if (modeValue < optionsValue) {\n        const value = particleValue - (optionsValue - modeValue) * ratio;\n        return clamp(value, modeValue, particleValue);\n    }\n}\n", "import { Circle, ExternalInteractorBase, Rectangle, colorMix, divMode, divModeExecute, getDistance, getRangeMax, isDivModeEnabled, isInArray, itemFromSingleOrMultiple, mouseLeaveEvent, mouseMoveEvent, rangeColorToHsl, rgbToHsl, } from \"tsparticles-engine\";\nimport { Bubble } from \"./Options/Classes/Bubble\";\nimport { calculateBubbleValue } from \"./Utils\";\nexport class Bubbler extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this._clickBubble = () => {\n            const container = this.container, options = container.actualOptions, mouseClickPos = container.interactivity.mouse.clickPosition, bubbleOptions = options.interactivity.modes.bubble;\n            if (!bubbleOptions || !mouseClickPos) {\n                return;\n            }\n            if (!container.bubble) {\n                container.bubble = {};\n            }\n            const distance = container.retina.bubbleModeDistance;\n            if (!distance || distance < 0) {\n                return;\n            }\n            const query = container.particles.quadTree.queryCircle(mouseClickPos, distance, (p) => this.isEnabled(p)), { bubble } = container;\n            for (const particle of query) {\n                if (!bubble.clicking) {\n                    continue;\n                }\n                particle.bubble.inRange = !bubble.durationEnd;\n                const pos = particle.getPosition(), distMouse = getDistance(pos, mouseClickPos), timeSpent = (new Date().getTime() - (container.interactivity.mouse.clickTime || 0)) / 1000;\n                if (timeSpent > bubbleOptions.duration) {\n                    bubble.durationEnd = true;\n                }\n                if (timeSpent > bubbleOptions.duration * 2) {\n                    bubble.clicking = false;\n                    bubble.durationEnd = false;\n                }\n                const sizeData = {\n                    bubbleObj: {\n                        optValue: container.retina.bubbleModeSize,\n                        value: particle.bubble.radius,\n                    },\n                    particlesObj: {\n                        optValue: getRangeMax(particle.options.size.value) * container.retina.pixelRatio,\n                        value: particle.size.value,\n                    },\n                    type: \"size\",\n                };\n                this._process(particle, distMouse, timeSpent, sizeData);\n                const opacityData = {\n                    bubbleObj: {\n                        optValue: bubbleOptions.opacity,\n                        value: particle.bubble.opacity,\n                    },\n                    particlesObj: {\n                        optValue: getRangeMax(particle.options.opacity.value),\n                        value: particle.opacity?.value ?? 1,\n                    },\n                    type: \"opacity\",\n                };\n                this._process(particle, distMouse, timeSpent, opacityData);\n                if (!bubble.durationEnd && distMouse <= distance) {\n                    this._hoverBubbleColor(particle, distMouse);\n                }\n                else {\n                    delete particle.bubble.color;\n                }\n            }\n        };\n        this._hoverBubble = () => {\n            const container = this.container, mousePos = container.interactivity.mouse.position, distance = container.retina.bubbleModeDistance;\n            if (!distance || distance < 0 || mousePos === undefined) {\n                return;\n            }\n            const query = container.particles.quadTree.queryCircle(mousePos, distance, (p) => this.isEnabled(p));\n            for (const particle of query) {\n                particle.bubble.inRange = true;\n                const pos = particle.getPosition(), pointDistance = getDistance(pos, mousePos), ratio = 1 - pointDistance / distance;\n                if (pointDistance <= distance) {\n                    if (ratio >= 0 && container.interactivity.status === mouseMoveEvent) {\n                        this._hoverBubbleSize(particle, ratio);\n                        this._hoverBubbleOpacity(particle, ratio);\n                        this._hoverBubbleColor(particle, ratio);\n                    }\n                }\n                else {\n                    this.reset(particle);\n                }\n                if (container.interactivity.status === mouseLeaveEvent) {\n                    this.reset(particle);\n                }\n            }\n        };\n        this._hoverBubbleColor = (particle, ratio, divBubble) => {\n            const options = this.container.actualOptions, bubbleOptions = divBubble ?? options.interactivity.modes.bubble;\n            if (!bubbleOptions) {\n                return;\n            }\n            if (!particle.bubble.finalColor) {\n                const modeColor = bubbleOptions.color;\n                if (!modeColor) {\n                    return;\n                }\n                const bubbleColor = itemFromSingleOrMultiple(modeColor);\n                particle.bubble.finalColor = rangeColorToHsl(bubbleColor);\n            }\n            if (!particle.bubble.finalColor) {\n                return;\n            }\n            if (bubbleOptions.mix) {\n                particle.bubble.color = undefined;\n                const pColor = particle.getFillColor();\n                particle.bubble.color = pColor\n                    ? rgbToHsl(colorMix(pColor, particle.bubble.finalColor, 1 - ratio, ratio))\n                    : particle.bubble.finalColor;\n            }\n            else {\n                particle.bubble.color = particle.bubble.finalColor;\n            }\n        };\n        this._hoverBubbleOpacity = (particle, ratio, divBubble) => {\n            const container = this.container, options = container.actualOptions, modeOpacity = divBubble?.opacity ?? options.interactivity.modes.bubble?.opacity;\n            if (!modeOpacity) {\n                return;\n            }\n            const optOpacity = particle.options.opacity.value, pOpacity = particle.opacity?.value ?? 1, opacity = calculateBubbleValue(pOpacity, modeOpacity, getRangeMax(optOpacity), ratio);\n            if (opacity !== undefined) {\n                particle.bubble.opacity = opacity;\n            }\n        };\n        this._hoverBubbleSize = (particle, ratio, divBubble) => {\n            const container = this.container, modeSize = divBubble?.size ? divBubble.size * container.retina.pixelRatio : container.retina.bubbleModeSize;\n            if (modeSize === undefined) {\n                return;\n            }\n            const optSize = getRangeMax(particle.options.size.value) * container.retina.pixelRatio, pSize = particle.size.value, size = calculateBubbleValue(pSize, modeSize, optSize, ratio);\n            if (size !== undefined) {\n                particle.bubble.radius = size;\n            }\n        };\n        this._process = (particle, distMouse, timeSpent, data) => {\n            const container = this.container, bubbleParam = data.bubbleObj.optValue, options = container.actualOptions, bubbleOptions = options.interactivity.modes.bubble;\n            if (!bubbleOptions || bubbleParam === undefined) {\n                return;\n            }\n            const bubbleDuration = bubbleOptions.duration, bubbleDistance = container.retina.bubbleModeDistance, particlesParam = data.particlesObj.optValue, pObjBubble = data.bubbleObj.value, pObj = data.particlesObj.value || 0, type = data.type;\n            if (!bubbleDistance || bubbleDistance < 0 || bubbleParam === particlesParam) {\n                return;\n            }\n            if (!container.bubble) {\n                container.bubble = {};\n            }\n            if (container.bubble.durationEnd) {\n                if (pObjBubble) {\n                    if (type === \"size\") {\n                        delete particle.bubble.radius;\n                    }\n                    if (type === \"opacity\") {\n                        delete particle.bubble.opacity;\n                    }\n                }\n            }\n            else {\n                if (distMouse <= bubbleDistance) {\n                    const obj = pObjBubble ?? pObj;\n                    if (obj !== bubbleParam) {\n                        const value = pObj - (timeSpent * (pObj - bubbleParam)) / bubbleDuration;\n                        if (type === \"size\") {\n                            particle.bubble.radius = value;\n                        }\n                        if (type === \"opacity\") {\n                            particle.bubble.opacity = value;\n                        }\n                    }\n                }\n                else {\n                    if (type === \"size\") {\n                        delete particle.bubble.radius;\n                    }\n                    if (type === \"opacity\") {\n                        delete particle.bubble.opacity;\n                    }\n                }\n            }\n        };\n        this._singleSelectorHover = (delta, selector, div) => {\n            const container = this.container, selectors = document.querySelectorAll(selector), bubble = container.actualOptions.interactivity.modes.bubble;\n            if (!bubble || !selectors.length) {\n                return;\n            }\n            selectors.forEach((item) => {\n                const elem = item, pxRatio = container.retina.pixelRatio, pos = {\n                    x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n                    y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio,\n                }, repulseRadius = (elem.offsetWidth / 2) * pxRatio, area = div.type === \"circle\"\n                    ? new Circle(pos.x, pos.y, repulseRadius)\n                    : new Rectangle(elem.offsetLeft * pxRatio, elem.offsetTop * pxRatio, elem.offsetWidth * pxRatio, elem.offsetHeight * pxRatio), query = container.particles.quadTree.query(area, (p) => this.isEnabled(p));\n                for (const particle of query) {\n                    if (!area.contains(particle.getPosition())) {\n                        continue;\n                    }\n                    particle.bubble.inRange = true;\n                    const divs = bubble.divs, divBubble = divMode(divs, elem);\n                    if (!particle.bubble.div || particle.bubble.div !== elem) {\n                        this.clear(particle, delta, true);\n                        particle.bubble.div = elem;\n                    }\n                    this._hoverBubbleSize(particle, 1, divBubble);\n                    this._hoverBubbleOpacity(particle, 1, divBubble);\n                    this._hoverBubbleColor(particle, 1, divBubble);\n                }\n            });\n        };\n        if (!container.bubble) {\n            container.bubble = {};\n        }\n        this.handleClickMode = (mode) => {\n            if (mode !== \"bubble\") {\n                return;\n            }\n            if (!container.bubble) {\n                container.bubble = {};\n            }\n            container.bubble.clicking = true;\n        };\n    }\n    clear(particle, delta, force) {\n        if (particle.bubble.inRange && !force) {\n            return;\n        }\n        delete particle.bubble.div;\n        delete particle.bubble.opacity;\n        delete particle.bubble.radius;\n        delete particle.bubble.color;\n    }\n    init() {\n        const container = this.container, bubble = container.actualOptions.interactivity.modes.bubble;\n        if (!bubble) {\n            return;\n        }\n        container.retina.bubbleModeDistance = bubble.distance * container.retina.pixelRatio;\n        if (bubble.size !== undefined) {\n            container.retina.bubbleModeSize = bubble.size * container.retina.pixelRatio;\n        }\n    }\n    async interact(delta) {\n        const options = this.container.actualOptions, events = options.interactivity.events, onHover = events.onHover, onClick = events.onClick, hoverEnabled = onHover.enable, hoverMode = onHover.mode, clickEnabled = onClick.enable, clickMode = onClick.mode, divs = events.onDiv;\n        if (hoverEnabled && isInArray(\"bubble\", hoverMode)) {\n            this._hoverBubble();\n        }\n        else if (clickEnabled && isInArray(\"bubble\", clickMode)) {\n            this._clickBubble();\n        }\n        else {\n            divModeExecute(\"bubble\", divs, (selector, div) => this._singleSelectorHover(delta, selector, div));\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? options.interactivity).events, { onClick, onDiv, onHover } = events, divBubble = isDivModeEnabled(\"bubble\", onDiv);\n        if (!(divBubble || (onHover.enable && mouse.position) || (onClick.enable && mouse.clickPosition))) {\n            return false;\n        }\n        return isInArray(\"bubble\", onHover.mode) || isInArray(\"bubble\", onClick.mode) || divBubble;\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.bubble) {\n            options.bubble = new Bubble();\n        }\n        for (const source of sources) {\n            options.bubble.load(source?.bubble);\n        }\n    }\n    reset(particle) {\n        particle.bubble.inRange = false;\n    }\n}\n", "import { Bubbler } from \"./Bubbler\";\nexport async function loadExternalBubbleInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalBubble\", (container) => new Bubbler(container), refresh);\n}\nexport * from \"./Options/Classes/BubbleBase\";\nexport * from \"./Options/Classes/BubbleDiv\";\nexport * from \"./Options/Classes/Bubble\";\nexport * from \"./Options/Interfaces/IBubbleBase\";\nexport * from \"./Options/Interfaces/IBubbleDiv\";\nexport * from \"./Options/Interfaces/IBubble\";\n", "export class ConnectLinks {\n    constructor() {\n        this.opacity = 0.5;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n", "import { ConnectLinks } from \"./ConnectLinks\";\nexport class Connect {\n    constructor() {\n        this.distance = 80;\n        this.links = new ConnectLinks();\n        this.radius = 60;\n    }\n    get lineLinked() {\n        return this.links;\n    }\n    set lineLinked(value) {\n        this.links = value;\n    }\n    get line_linked() {\n        return this.links;\n    }\n    set line_linked(value) {\n        this.links = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        this.links.load(data.links ?? data.lineLinked ?? data.line_linked);\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n", "import { colorMix, drawLine, getStyleFromHsl, getStyleFromRgb, } from \"tsparticles-engine\";\nexport function gradient(context, p1, p2, opacity) {\n    const gradStop = Math.floor(p2.getRadius() / p1.getRadius()), color1 = p1.getFillColor(), color2 = p2.getFillColor();\n    if (!color1 || !color2) {\n        return;\n    }\n    const sourcePos = p1.getPosition(), destPos = p2.getPosition(), midRgb = colorMix(color1, color2, p1.getRadius(), p2.getRadius()), grad = context.createLinearGradient(sourcePos.x, sourcePos.y, destPos.x, destPos.y);\n    grad.addColorStop(0, getStyleFromHsl(color1, opacity));\n    grad.addColorStop(gradStop > 1 ? 1 : gradStop, getStyleFromRgb(midRgb, opacity));\n    grad.addColorStop(1, getStyleFromHsl(color2, opacity));\n    return grad;\n}\nexport function drawConnectLine(context, width, lineStyle, begin, end) {\n    drawLine(context, begin, end);\n    context.lineWidth = width;\n    context.strokeStyle = lineStyle;\n    context.stroke();\n}\nexport function lineStyle(container, ctx, p1, p2) {\n    const options = container.actualOptions, connectOptions = options.interactivity.modes.connect;\n    if (!connectOptions) {\n        return;\n    }\n    return gradient(ctx, p1, p2, connectOptions.links.opacity);\n}\nexport function drawConnection(container, p1, p2) {\n    container.canvas.draw((ctx) => {\n        const ls = lineStyle(container, ctx, p1, p2);\n        if (!ls) {\n            return;\n        }\n        const pos1 = p1.getPosition(), pos2 = p2.getPosition();\n        drawConnectLine(ctx, p1.retina.linksWidth ?? 0, ls, pos1, pos2);\n    });\n}\n", "import { ExternalInteractorBase, isInArray, } from \"tsparticles-engine\";\nimport { Connect } from \"./Options/Classes/Connect\";\nimport { drawConnection } from \"./Utils\";\nexport class Connector extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, connect = container.actualOptions.interactivity.modes.connect;\n        if (!connect) {\n            return;\n        }\n        container.retina.connectModeDistance = connect.distance * container.retina.pixelRatio;\n        container.retina.connectModeRadius = connect.radius * container.retina.pixelRatio;\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions;\n        if (options.interactivity.events.onHover.enable && container.interactivity.status === \"pointermove\") {\n            const mousePos = container.interactivity.mouse.position;\n            if (!container.retina.connectModeDistance ||\n                container.retina.connectModeDistance < 0 ||\n                !container.retina.connectModeRadius ||\n                container.retina.connectModeRadius < 0 ||\n                !mousePos) {\n                return;\n            }\n            const distance = Math.abs(container.retina.connectModeRadius), query = container.particles.quadTree.queryCircle(mousePos, distance, (p) => this.isEnabled(p));\n            let i = 0;\n            for (const p1 of query) {\n                const pos1 = p1.getPosition();\n                for (const p2 of query.slice(i + 1)) {\n                    const pos2 = p2.getPosition(), distMax = Math.abs(container.retina.connectModeDistance), xDiff = Math.abs(pos1.x - pos2.x), yDiff = Math.abs(pos1.y - pos2.y);\n                    if (xDiff < distMax && yDiff < distMax) {\n                        drawConnection(container, p1, p2);\n                    }\n                }\n                ++i;\n            }\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n        if (!(events.onHover.enable && mouse.position)) {\n            return false;\n        }\n        return isInArray(\"connect\", events.onHover.mode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.connect) {\n            options.connect = new Connect();\n        }\n        for (const source of sources) {\n            options.connect.load(source?.connect);\n        }\n    }\n    reset() {\n    }\n}\n", "import { Connector } from \"./Connector\";\nexport async function loadExternalConnectInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalConnect\", (container) => new Connector(container), refresh);\n}\nexport * from \"./Options/Classes/Connect\";\nexport * from \"./Options/Classes/ConnectLinks\";\nexport * from \"./Options/Interfaces/IConnect\";\nexport * from \"./Options/Interfaces/IConnectLinks\";\n", "import { OptionsColor } from \"tsparticles-engine\";\nexport class GrabLinks {\n    constructor() {\n        this.blink = false;\n        this.consent = false;\n        this.opacity = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.blink !== undefined) {\n            this.blink = data.blink;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.consent !== undefined) {\n            this.consent = data.consent;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n", "import { GrabLinks } from \"./GrabLinks\";\nexport class Grab {\n    constructor() {\n        this.distance = 100;\n        this.links = new GrabLinks();\n    }\n    get lineLinked() {\n        return this.links;\n    }\n    set lineLinked(value) {\n        this.links = value;\n    }\n    get line_linked() {\n        return this.links;\n    }\n    set line_linked(value) {\n        this.links = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        this.links.load(data.links ?? data.lineLinked ?? data.line_linked);\n    }\n}\n", "import { drawLine, getStyleFromRgb } from \"tsparticles-engine\";\nexport function drawGrabLine(context, width, begin, end, colorLine, opacity) {\n    drawLine(context, begin, end);\n    context.strokeStyle = getStyleFromRgb(colorLine, opacity);\n    context.lineWidth = width;\n    context.stroke();\n}\nexport function drawGrab(container, particle, lineColor, opacity, mousePos) {\n    container.canvas.draw((ctx) => {\n        const beginPos = particle.getPosition();\n        drawGrabLine(ctx, particle.retina.linksWidth ?? 0, beginPos, mousePos, lineColor, opacity);\n    });\n}\n", "import { ExternalInteractorBase, getDistance, getLinkColor, getLinkRandomColor, isInArray, mouseMoveEvent, } from \"tsparticles-engine\";\nimport { Grab } from \"./Options/Classes/Grab\";\nimport { drawGrab } from \"./Utils\";\nexport class <PERSON><PERSON><PERSON> extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, grab = container.actualOptions.interactivity.modes.grab;\n        if (!grab) {\n            return;\n        }\n        container.retina.grabModeDistance = grab.distance * container.retina.pixelRatio;\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions, interactivity = options.interactivity;\n        if (!interactivity.modes.grab ||\n            !interactivity.events.onHover.enable ||\n            container.interactivity.status !== mouseMoveEvent) {\n            return;\n        }\n        const mousePos = container.interactivity.mouse.position;\n        if (!mousePos) {\n            return;\n        }\n        const distance = container.retina.grabModeDistance;\n        if (!distance || distance < 0) {\n            return;\n        }\n        const query = container.particles.quadTree.queryCircle(mousePos, distance, (p) => this.isEnabled(p));\n        for (const particle of query) {\n            const pos = particle.getPosition(), pointDistance = getDistance(pos, mousePos);\n            if (pointDistance > distance) {\n                continue;\n            }\n            const grabLineOptions = interactivity.modes.grab.links, lineOpacity = grabLineOptions.opacity, opacityLine = lineOpacity - (pointDistance * lineOpacity) / distance;\n            if (opacityLine <= 0) {\n                continue;\n            }\n            const optColor = grabLineOptions.color ?? particle.options.links?.color;\n            if (!container.particles.grabLineColor && optColor) {\n                const linksOptions = interactivity.modes.grab.links;\n                container.particles.grabLineColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n            }\n            const colorLine = getLinkColor(particle, undefined, container.particles.grabLineColor);\n            if (!colorLine) {\n                continue;\n            }\n            drawGrab(container, particle, colorLine, opacityLine, mousePos);\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n        return events.onHover.enable && !!mouse.position && isInArray(\"grab\", events.onHover.mode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.grab) {\n            options.grab = new Grab();\n        }\n        for (const source of sources) {\n            options.grab.load(source?.grab);\n        }\n    }\n    reset() {\n    }\n}\n", "import { Grabber } from \"./Grabber\";\nexport async function loadExternalGrabInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalGrab\", (container) => new Grabber(container), refresh);\n}\nexport * from \"./Options/Classes/Grab\";\nexport * from \"./Options/Classes/GrabLinks\";\nexport * from \"./Options/Interfaces/IGrab\";\nexport * from \"./Options/Interfaces/IGrabLinks\";\n", "import { ExternalInteractorBase } from \"tsparticles-engine\";\nexport class <PERSON><PERSON> extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this.handleClickMode = (mode) => {\n            if (mode !== \"pause\") {\n                return;\n            }\n            const container = this.container;\n            if (container.getAnimationStatus()) {\n                container.pause();\n            }\n            else {\n                container.play();\n            }\n        };\n    }\n    clear() {\n    }\n    init() {\n    }\n    async interact() {\n    }\n    isEnabled() {\n        return true;\n    }\n    reset() {\n    }\n}\n", "import { Pauser } from \"./Pauser\";\nexport async function loadExternalPauseInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalPause\", (container) => new Pauser(container), refresh);\n}\n", "import { setRangeValue } from \"tsparticles-engine\";\nexport class Push {\n    constructor() {\n        this.default = true;\n        this.groups = [];\n        this.quantity = 4;\n    }\n    get particles_nb() {\n        return this.quantity;\n    }\n    set particles_nb(value) {\n        this.quantity = setRangeValue(value);\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.default !== undefined) {\n            this.default = data.default;\n        }\n        if (data.groups !== undefined) {\n            this.groups = data.groups.map((t) => t);\n        }\n        if (!this.groups.length) {\n            this.default = true;\n        }\n        const quantity = data.quantity ?? data.particles_nb;\n        if (quantity !== undefined) {\n            this.quantity = setRangeValue(quantity);\n        }\n    }\n}\n", "import { ExternalInteractorBase, getRangeValue, itemFromArray, } from \"tsparticles-engine\";\nimport { Push } from \"./Options/Classes/Push\";\nexport class Pusher extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this.handleClickMode = (mode) => {\n            if (mode !== \"push\") {\n                return;\n            }\n            const container = this.container, options = container.actualOptions, pushOptions = options.interactivity.modes.push;\n            if (!pushOptions) {\n                return;\n            }\n            const quantity = getRangeValue(pushOptions.quantity);\n            if (quantity <= 0) {\n                return;\n            }\n            const group = itemFromArray([undefined, ...pushOptions.groups]), groupOptions = group !== undefined ? container.actualOptions.particles.groups[group] : undefined;\n            container.particles.push(quantity, container.interactivity.mouse, groupOptions, group);\n        };\n    }\n    clear() {\n    }\n    init() {\n    }\n    async interact() {\n    }\n    isEnabled() {\n        return true;\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.push) {\n            options.push = new Push();\n        }\n        for (const source of sources) {\n            options.push.load(source?.push);\n        }\n    }\n    reset() {\n    }\n}\n", "import { Pusher } from \"./Pusher\";\nexport async function loadExternalPushInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalPush\", (container) => new Pusher(container), refresh);\n}\nexport * from \"./Options/Classes/Push\";\nexport * from \"./Options/Interfaces/IPush\";\n", "import { setRangeValue } from \"tsparticles-engine\";\nexport class Remove {\n    constructor() {\n        this.quantity = 2;\n    }\n    get particles_nb() {\n        return this.quantity;\n    }\n    set particles_nb(value) {\n        this.quantity = setRangeValue(value);\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        const quantity = data.quantity ?? data.particles_nb;\n        if (quantity !== undefined) {\n            this.quantity = setRangeValue(quantity);\n        }\n    }\n}\n", "import { ExternalInteractorBase, getRangeValue, } from \"tsparticles-engine\";\nimport { Remove } from \"./Options/Classes/Remove\";\nexport class Remover extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n        this.handleClickMode = (mode) => {\n            const container = this.container, options = container.actualOptions;\n            if (!options.interactivity.modes.remove || mode !== \"remove\") {\n                return;\n            }\n            const removeNb = getRangeValue(options.interactivity.modes.remove.quantity);\n            container.particles.removeQuantity(removeNb);\n        };\n    }\n    clear() {\n    }\n    init() {\n    }\n    async interact() {\n    }\n    isEnabled() {\n        return true;\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.remove) {\n            options.remove = new Remove();\n        }\n        for (const source of sources) {\n            options.remove.load(source?.remove);\n        }\n    }\n    reset() {\n    }\n}\n", "import { Remover } from \"./Remover\";\nexport async function loadExternalRemoveInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalRemove\", (container) => new Remover(container), refresh);\n}\nexport * from \"./Options/Classes/Remove\";\nexport * from \"./Options/Interfaces/IRemove\";\n", "export class RepulseBase {\n    constructor() {\n        this.distance = 200;\n        this.duration = 0.4;\n        this.factor = 100;\n        this.speed = 1;\n        this.maxSpeed = 50;\n        this.easing = \"ease-out-quad\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.duration !== undefined) {\n            this.duration = data.duration;\n        }\n        if (data.easing !== undefined) {\n            this.easing = data.easing;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.speed !== undefined) {\n            this.speed = data.speed;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = data.maxSpeed;\n        }\n    }\n}\n", "import { executeOnSingleOrMultiple, } from \"tsparticles-engine\";\nimport { RepulseBase } from \"./RepulseBase\";\nexport class RepulseDiv extends RepulseBase {\n    constructor() {\n        super();\n        this.selectors = [];\n    }\n    get ids() {\n        return executeOnSingleOrMultiple(this.selectors, (t) => t.replace(\"#\", \"\"));\n    }\n    set ids(value) {\n        this.selectors = executeOnSingleOrMultiple(value, (t) => `#${t}`);\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.ids !== undefined) {\n            this.ids = data.ids;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n    }\n}\n", "import { executeOnSingleOrMultiple, } from \"tsparticles-engine\";\nimport { RepulseBase } from \"./RepulseBase\";\nimport { RepulseDiv } from \"./RepulseDiv\";\nexport class Repulse extends RepulseBase {\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        this.divs = executeOnSingleOrMultiple(data.divs, (div) => {\n            const tmp = new RepulseDiv();\n            tmp.load(div);\n            return tmp;\n        });\n    }\n}\n", "import { Circle, ExternalInteractorBase, Rectangle, Vector, clamp, divMode, divModeExecute, getDistances, getEasing, isDivModeEnabled, isInArray, mouseMoveEvent, } from \"tsparticles-engine\";\nimport { Repulse } from \"./Options/Classes/Repulse\";\nexport class Repulser extends ExternalInteractorBase {\n    constructor(engine, container) {\n        super(container);\n        this._clickRepulse = () => {\n            const container = this.container, repulseOptions = container.actualOptions.interactivity.modes.repulse;\n            if (!repulseOptions) {\n                return;\n            }\n            const repulse = container.repulse || { particles: [] };\n            if (!repulse.finish) {\n                if (!repulse.count) {\n                    repulse.count = 0;\n                }\n                repulse.count++;\n                if (repulse.count === container.particles.count) {\n                    repulse.finish = true;\n                }\n            }\n            if (repulse.clicking) {\n                const repulseDistance = container.retina.repulseModeDistance;\n                if (!repulseDistance || repulseDistance < 0) {\n                    return;\n                }\n                const repulseRadius = Math.pow(repulseDistance / 6, 3), mouseClickPos = container.interactivity.mouse.clickPosition;\n                if (mouseClickPos === undefined) {\n                    return;\n                }\n                const range = new Circle(mouseClickPos.x, mouseClickPos.y, repulseRadius), query = container.particles.quadTree.query(range, (p) => this.isEnabled(p));\n                for (const particle of query) {\n                    const { dx, dy, distance } = getDistances(mouseClickPos, particle.position), d = distance ** 2, velocity = repulseOptions.speed, force = (-repulseRadius * velocity) / d;\n                    if (d <= repulseRadius) {\n                        repulse.particles.push(particle);\n                        const vect = Vector.create(dx, dy);\n                        vect.length = force;\n                        particle.velocity.setTo(vect);\n                    }\n                }\n            }\n            else if (repulse.clicking === false) {\n                for (const particle of repulse.particles) {\n                    particle.velocity.setTo(particle.initialVelocity);\n                }\n                repulse.particles = [];\n            }\n        };\n        this._hoverRepulse = () => {\n            const container = this.container, mousePos = container.interactivity.mouse.position, repulseRadius = container.retina.repulseModeDistance;\n            if (!repulseRadius || repulseRadius < 0 || !mousePos) {\n                return;\n            }\n            this._processRepulse(mousePos, repulseRadius, new Circle(mousePos.x, mousePos.y, repulseRadius));\n        };\n        this._processRepulse = (position, repulseRadius, area, divRepulse) => {\n            const container = this.container, query = container.particles.quadTree.query(area, (p) => this.isEnabled(p)), repulseOptions = container.actualOptions.interactivity.modes.repulse;\n            if (!repulseOptions) {\n                return;\n            }\n            for (const particle of query) {\n                const { dx, dy, distance } = getDistances(particle.position, position), velocity = (divRepulse?.speed ?? repulseOptions.speed) * repulseOptions.factor, repulseFactor = clamp(getEasing(repulseOptions.easing)(1 - distance / repulseRadius) * velocity, 0, repulseOptions.maxSpeed), normVec = Vector.create(distance === 0 ? velocity : (dx / distance) * repulseFactor, distance === 0 ? velocity : (dy / distance) * repulseFactor);\n                particle.position.addTo(normVec);\n            }\n        };\n        this._singleSelectorRepulse = (selector, div) => {\n            const container = this.container, repulse = container.actualOptions.interactivity.modes.repulse;\n            if (!repulse) {\n                return;\n            }\n            const query = document.querySelectorAll(selector);\n            if (!query.length) {\n                return;\n            }\n            query.forEach((item) => {\n                const elem = item, pxRatio = container.retina.pixelRatio, pos = {\n                    x: (elem.offsetLeft + elem.offsetWidth / 2) * pxRatio,\n                    y: (elem.offsetTop + elem.offsetHeight / 2) * pxRatio,\n                }, repulseRadius = (elem.offsetWidth / 2) * pxRatio, area = div.type === \"circle\"\n                    ? new Circle(pos.x, pos.y, repulseRadius)\n                    : new Rectangle(elem.offsetLeft * pxRatio, elem.offsetTop * pxRatio, elem.offsetWidth * pxRatio, elem.offsetHeight * pxRatio), divs = repulse.divs, divRepulse = divMode(divs, elem);\n                this._processRepulse(pos, repulseRadius, area, divRepulse);\n            });\n        };\n        this._engine = engine;\n        if (!container.repulse) {\n            container.repulse = { particles: [] };\n        }\n        this.handleClickMode = (mode) => {\n            const options = this.container.actualOptions, repulseOpts = options.interactivity.modes.repulse;\n            if (!repulseOpts || mode !== \"repulse\") {\n                return;\n            }\n            if (!container.repulse) {\n                container.repulse = { particles: [] };\n            }\n            const repulse = container.repulse;\n            repulse.clicking = true;\n            repulse.count = 0;\n            for (const particle of container.repulse.particles) {\n                if (!this.isEnabled(particle)) {\n                    continue;\n                }\n                particle.velocity.setTo(particle.initialVelocity);\n            }\n            repulse.particles = [];\n            repulse.finish = false;\n            setTimeout(() => {\n                if (container.destroyed) {\n                    return;\n                }\n                repulse.clicking = false;\n            }, repulseOpts.duration * 1000);\n        };\n    }\n    clear() {\n    }\n    init() {\n        const container = this.container, repulse = container.actualOptions.interactivity.modes.repulse;\n        if (!repulse) {\n            return;\n        }\n        container.retina.repulseModeDistance = repulse.distance * container.retina.pixelRatio;\n    }\n    async interact() {\n        const container = this.container, options = container.actualOptions, mouseMoveStatus = container.interactivity.status === mouseMoveEvent, events = options.interactivity.events, hover = events.onHover, hoverEnabled = hover.enable, hoverMode = hover.mode, click = events.onClick, clickEnabled = click.enable, clickMode = click.mode, divs = events.onDiv;\n        if (mouseMoveStatus && hoverEnabled && isInArray(\"repulse\", hoverMode)) {\n            this._hoverRepulse();\n        }\n        else if (clickEnabled && isInArray(\"repulse\", clickMode)) {\n            this._clickRepulse();\n        }\n        else {\n            divModeExecute(\"repulse\", divs, (selector, div) => this._singleSelectorRepulse(selector, div));\n        }\n    }\n    isEnabled(particle) {\n        const container = this.container, options = container.actualOptions, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? options.interactivity).events, divs = events.onDiv, hover = events.onHover, click = events.onClick, divRepulse = isDivModeEnabled(\"repulse\", divs);\n        if (!(divRepulse || (hover.enable && mouse.position) || (click.enable && mouse.clickPosition))) {\n            return false;\n        }\n        const hoverMode = hover.mode, clickMode = click.mode;\n        return isInArray(\"repulse\", hoverMode) || isInArray(\"repulse\", clickMode) || divRepulse;\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.repulse) {\n            options.repulse = new Repulse();\n        }\n        for (const source of sources) {\n            options.repulse.load(source?.repulse);\n        }\n    }\n    reset() {\n    }\n}\n", "import { Repulser } from \"./Repulser\";\nexport async function loadExternalRepulseInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalRepulse\", (container) => new Repulser(engine, container), refresh);\n}\nexport * from \"./Options/Classes/RepulseBase\";\nexport * from \"./Options/Classes/RepulseDiv\";\nexport * from \"./Options/Classes/Repulse\";\nexport * from \"./Options/Interfaces/IRepulseBase\";\nexport * from \"./Options/Interfaces/IRepulseDiv\";\nexport * from \"./Options/Interfaces/IRepulse\";\n", "export class Slow {\n    constructor() {\n        this.factor = 3;\n        this.radius = 200;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.factor !== undefined) {\n            this.factor = data.factor;\n        }\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n", "import { ExternalInteractorBase, getDistance, isInArray, } from \"tsparticles-engine\";\nimport { Slow } from \"./Options/Classes/Slow\";\nexport class Slower extends ExternalInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear(particle, delta, force) {\n        if (particle.slow.inRange && !force) {\n            return;\n        }\n        particle.slow.factor = 1;\n    }\n    init() {\n        const container = this.container, slow = container.actualOptions.interactivity.modes.slow;\n        if (!slow) {\n            return;\n        }\n        container.retina.slowModeRadius = slow.radius * container.retina.pixelRatio;\n    }\n    async interact() {\n    }\n    isEnabled(particle) {\n        const container = this.container, mouse = container.interactivity.mouse, events = (particle?.interactivity ?? container.actualOptions.interactivity).events;\n        return events.onHover.enable && !!mouse.position && isInArray(\"slow\", events.onHover.mode);\n    }\n    loadModeOptions(options, ...sources) {\n        if (!options.slow) {\n            options.slow = new Slow();\n        }\n        for (const source of sources) {\n            options.slow.load(source?.slow);\n        }\n    }\n    reset(particle) {\n        particle.slow.inRange = false;\n        const container = this.container, options = container.actualOptions, mousePos = container.interactivity.mouse.position, radius = container.retina.slowModeRadius, slowOptions = options.interactivity.modes.slow;\n        if (!slowOptions || !radius || radius < 0 || !mousePos) {\n            return;\n        }\n        const particlePos = particle.getPosition(), dist = getDistance(mousePos, particlePos), proximityFactor = dist / radius, slowFactor = slowOptions.factor, { slow } = particle;\n        if (dist > radius) {\n            return;\n        }\n        slow.inRange = true;\n        slow.factor = proximityFactor / slowFactor;\n    }\n}\n", "import { Slower } from \"./Slower\";\nexport async function loadExternalSlowInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"externalSlow\", (container) => new Slower(container), refresh);\n}\nexport * from \"./Options/Classes/Slow\";\nexport * from \"./Options/Interfaces/ISlow\";\n", "export const InterlaceOffsets = [0, 4, 2, 1];\nexport const InterlaceSteps = [8, 8, 4, 2];\n", "export class ByteStream {\n    constructor(bytes) {\n        this.pos = 0;\n        this.data = new Uint8ClampedArray(bytes);\n    }\n    getString(count) {\n        const slice = this.data.slice(this.pos, this.pos + count);\n        this.pos += slice.length;\n        return slice.reduce((acc, curr) => acc + String.fromCharCode(curr), \"\");\n    }\n    nextByte() {\n        return this.data[this.pos++];\n    }\n    nextTwoBytes() {\n        this.pos += 2;\n        return this.data[this.pos - 2] + (this.data[this.pos - 1] << 8);\n    }\n    readSubBlocks() {\n        let blockString = \"\", size = 0;\n        do {\n            size = this.data[this.pos++];\n            for (let count = size; --count >= 0; blockString += String.fromCharCode(this.data[this.pos++])) {\n            }\n        } while (size !== 0);\n        return blockString;\n    }\n    readSubBlocksBin() {\n        let size = 0, len = 0;\n        for (let offset = 0; (size = this.data[this.pos + offset]) !== 0; offset += size + 1) {\n            len += size;\n        }\n        const blockData = new Uint8Array(len);\n        for (let i = 0; (size = this.data[this.pos++]) !== 0;) {\n            for (let count = size; --count >= 0; blockData[i++] = this.data[this.pos++]) {\n            }\n        }\n        return blockData;\n    }\n    skipSubBlocks() {\n        for (; this.data[this.pos] !== 0; this.pos += this.data[this.pos] + 1) {\n        }\n        this.pos++;\n    }\n}\n", "import { InterlaceOffsets, InterlaceSteps } from \"./Constants\";\nimport { ByteStream } from \"./ByteStream\";\nfunction parseColorTable(byteStream, count) {\n    const colors = [];\n    for (let i = 0; i < count; i++) {\n        colors.push({\n            r: byteStream.data[byteStream.pos],\n            g: byteStream.data[byteStream.pos + 1],\n            b: byteStream.data[byteStream.pos + 2],\n        });\n        byteStream.pos += 3;\n    }\n    return colors;\n}\nasync function parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex) {\n    switch (byteStream.nextByte()) {\n        case 249: {\n            const frame = gif.frames[getFrameIndex(false)];\n            byteStream.pos++;\n            const packedByte = byteStream.nextByte();\n            frame.GCreserved = (packedByte & 0xe0) >>> 5;\n            frame.disposalMethod = (packedByte & 0x1c) >>> 2;\n            frame.userInputDelayFlag = (packedByte & 2) === 2;\n            const transparencyFlag = (packedByte & 1) === 1;\n            frame.delayTime = byteStream.nextTwoBytes() * 0xa;\n            const transparencyIndex = byteStream.nextByte();\n            if (transparencyFlag) {\n                getTransparencyIndex(transparencyIndex);\n            }\n            byteStream.pos++;\n            break;\n        }\n        case 255: {\n            byteStream.pos++;\n            const applicationExtension = {\n                identifier: byteStream.getString(8),\n                authenticationCode: byteStream.getString(3),\n                data: byteStream.readSubBlocksBin(),\n            };\n            gif.applicationExtensions.push(applicationExtension);\n            break;\n        }\n        case 254: {\n            gif.comments.push([getFrameIndex(false), byteStream.readSubBlocks()]);\n            break;\n        }\n        case 1: {\n            if (gif.globalColorTable.length === 0) {\n                throw new EvalError(\"plain text extension without global color table\");\n            }\n            byteStream.pos++;\n            gif.frames[getFrameIndex(false)].plainTextData = {\n                left: byteStream.nextTwoBytes(),\n                top: byteStream.nextTwoBytes(),\n                width: byteStream.nextTwoBytes(),\n                height: byteStream.nextTwoBytes(),\n                charSize: {\n                    width: byteStream.nextTwoBytes(),\n                    height: byteStream.nextTwoBytes(),\n                },\n                foregroundColor: byteStream.nextByte(),\n                backgroundColor: byteStream.nextByte(),\n                text: byteStream.readSubBlocks(),\n            };\n            break;\n        }\n        default:\n            byteStream.skipSubBlocks();\n            break;\n    }\n}\nasync function parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n    const frame = gif.frames[getFrameIndex(true)];\n    frame.left = byteStream.nextTwoBytes();\n    frame.top = byteStream.nextTwoBytes();\n    frame.width = byteStream.nextTwoBytes();\n    frame.height = byteStream.nextTwoBytes();\n    const packedByte = byteStream.nextByte(), localColorTableFlag = (packedByte & 0x80) === 0x80, interlacedFlag = (packedByte & 0x40) === 0x40;\n    frame.sortFlag = (packedByte & 0x20) === 0x20;\n    frame.reserved = (packedByte & 0x18) >>> 3;\n    const localColorCount = 1 << ((packedByte & 7) + 1);\n    if (localColorTableFlag) {\n        frame.localColorTable = parseColorTable(byteStream, localColorCount);\n    }\n    const getColor = (index) => {\n        const { r, g, b } = (localColorTableFlag ? frame.localColorTable : gif.globalColorTable)[index];\n        return { r, g, b, a: index === getTransparencyIndex(null) ? (avgAlpha ? ~~((r + g + b) / 3) : 0) : 255 };\n    };\n    const image = (() => {\n        try {\n            return new ImageData(frame.width, frame.height, { colorSpace: \"srgb\" });\n        }\n        catch (error) {\n            if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n                return null;\n            }\n            throw error;\n        }\n    })();\n    if (image == null) {\n        throw new EvalError(\"GIF frame size is to large\");\n    }\n    const minCodeSize = byteStream.nextByte(), imageData = byteStream.readSubBlocksBin(), clearCode = 1 << minCodeSize;\n    const readBits = (pos, len) => {\n        const bytePos = pos >>> 3, bitPos = pos & 7;\n        return (((imageData[bytePos] + (imageData[bytePos + 1] << 8) + (imageData[bytePos + 2] << 16)) &\n            (((1 << len) - 1) << bitPos)) >>>\n            bitPos);\n    };\n    if (interlacedFlag) {\n        for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pass = 0; pass < 4; pass++) {\n            if (InterlaceOffsets[pass] < frame.height) {\n                for (let pixelPos = 0, lineIndex = 0;;) {\n                    const last = code;\n                    code = readBits(pos, size);\n                    pos += size + 1;\n                    if (code === clearCode) {\n                        size = minCodeSize + 1;\n                        dic.length = clearCode + 2;\n                        for (let i = 0; i < dic.length; i++) {\n                            dic[i] = i < clearCode ? [i] : [];\n                        }\n                    }\n                    else {\n                        if (code >= dic.length) {\n                            dic.push(dic[last].concat(dic[last][0]));\n                        }\n                        else if (last !== clearCode) {\n                            dic.push(dic[last].concat(dic[code][0]));\n                        }\n                        for (let i = 0; i < dic[code].length; i++) {\n                            const { r, g, b, a } = getColor(dic[code][i]);\n                            image.data.set([r, g, b, a], InterlaceOffsets[pass] * frame.width +\n                                InterlaceSteps[pass] * lineIndex +\n                                (pixelPos % (frame.width * 4)));\n                            pixelPos += 4;\n                        }\n                        if (dic.length === 1 << size && size < 0xc) {\n                            size++;\n                        }\n                    }\n                    if (pixelPos === frame.width * 4 * (lineIndex + 1)) {\n                        lineIndex++;\n                        if (InterlaceOffsets[pass] + InterlaceSteps[pass] * lineIndex >= frame.height) {\n                            break;\n                        }\n                    }\n                }\n            }\n            progressCallback?.(byteStream.pos / (byteStream.data.length - 1), getFrameIndex(false) + 1, image, { x: frame.left, y: frame.top }, { width: gif.width, height: gif.height });\n        }\n        frame.image = image;\n        frame.bitmap = await createImageBitmap(image);\n    }\n    else {\n        for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pixelPos = -4;;) {\n            const last = code;\n            code = readBits(pos, size);\n            pos += size;\n            if (code === clearCode) {\n                size = minCodeSize + 1;\n                dic.length = clearCode + 2;\n                for (let i = 0; i < dic.length; i++) {\n                    dic[i] = i < clearCode ? [i] : [];\n                }\n            }\n            else {\n                if (code === clearCode + 1) {\n                    break;\n                }\n                if (code >= dic.length) {\n                    dic.push(dic[last].concat(dic[last][0]));\n                }\n                else if (last !== clearCode) {\n                    dic.push(dic[last].concat(dic[code][0]));\n                }\n                for (let i = 0; i < dic[code].length; i++) {\n                    const { r, g, b, a } = getColor(dic[code][i]);\n                    image.data.set([r, g, b, a], (pixelPos += 4));\n                }\n                if (dic.length >= 1 << size && size < 0xc) {\n                    size++;\n                }\n            }\n        }\n        frame.image = image;\n        frame.bitmap = await createImageBitmap(image);\n        progressCallback?.((byteStream.pos + 1) / byteStream.data.length, getFrameIndex(false) + 1, frame.image, { x: frame.left, y: frame.top }, { width: gif.width, height: gif.height });\n    }\n}\nasync function parseBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n    switch (byteStream.nextByte()) {\n        case 59:\n            return true;\n        case 44:\n            await parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback);\n            break;\n        case 33:\n            await parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex);\n            break;\n        default:\n            throw new EvalError(\"undefined block found\");\n    }\n    return false;\n}\nexport function getGIFLoopAmount(gif) {\n    for (const extension of gif.applicationExtensions) {\n        if (extension.identifier + extension.authenticationCode !== \"NETSCAPE2.0\") {\n            continue;\n        }\n        return extension.data[1] + (extension.data[2] << 8);\n    }\n    return NaN;\n}\nexport async function decodeGIF(gifURL, progressCallback, avgAlpha) {\n    if (!avgAlpha)\n        avgAlpha = false;\n    const res = await fetch(gifURL);\n    if (!res.ok && res.status === 404) {\n        throw new EvalError(\"file not found\");\n    }\n    const buffer = await res.arrayBuffer();\n    const gif = {\n        width: 0,\n        height: 0,\n        totalTime: 0,\n        colorRes: 0,\n        pixelAspectRatio: 0,\n        frames: [],\n        sortFlag: false,\n        globalColorTable: [],\n        backgroundImage: new ImageData(1, 1, { colorSpace: \"srgb\" }),\n        comments: [],\n        applicationExtensions: [],\n    }, byteStream = new ByteStream(new Uint8ClampedArray(buffer));\n    if (byteStream.getString(6) !== \"GIF89a\") {\n        throw new Error(\"not a supported GIF file\");\n    }\n    gif.width = byteStream.nextTwoBytes();\n    gif.height = byteStream.nextTwoBytes();\n    const packedByte = byteStream.nextByte(), globalColorTableFlag = (packedByte & 0x80) === 0x80;\n    gif.colorRes = (packedByte & 0x70) >>> 4;\n    gif.sortFlag = (packedByte & 8) === 8;\n    const globalColorCount = 1 << ((packedByte & 7) + 1), backgroundColorIndex = byteStream.nextByte();\n    gif.pixelAspectRatio = byteStream.nextByte();\n    if (gif.pixelAspectRatio !== 0) {\n        gif.pixelAspectRatio = (gif.pixelAspectRatio + 0xf) / 0x40;\n    }\n    if (globalColorTableFlag) {\n        gif.globalColorTable = parseColorTable(byteStream, globalColorCount);\n    }\n    const backgroundImage = (() => {\n        try {\n            return new ImageData(gif.width, gif.height, { colorSpace: \"srgb\" });\n        }\n        catch (error) {\n            if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n                return null;\n            }\n            throw error;\n        }\n    })();\n    if (backgroundImage == null) {\n        throw new Error(\"GIF frame size is to large\");\n    }\n    const { r, g, b } = gif.globalColorTable[backgroundColorIndex];\n    backgroundImage.data.set(globalColorTableFlag ? [r, g, b, 255] : [0, 0, 0, 0]);\n    for (let i = 4; i < backgroundImage.data.length; i *= 2) {\n        backgroundImage.data.copyWithin(i, 0, i);\n    }\n    gif.backgroundImage = backgroundImage;\n    let frameIndex = -1, incrementFrameIndex = true, transparencyIndex = -1;\n    const getframeIndex = (increment) => {\n        if (increment) {\n            incrementFrameIndex = true;\n        }\n        return frameIndex;\n    };\n    const getTransparencyIndex = (newValue) => {\n        if (newValue != null) {\n            transparencyIndex = newValue;\n        }\n        return transparencyIndex;\n    };\n    try {\n        do {\n            if (incrementFrameIndex) {\n                gif.frames.push({\n                    left: 0,\n                    top: 0,\n                    width: 0,\n                    height: 0,\n                    disposalMethod: 0,\n                    image: new ImageData(1, 1, { colorSpace: \"srgb\" }),\n                    plainTextData: null,\n                    userInputDelayFlag: false,\n                    delayTime: 0,\n                    sortFlag: false,\n                    localColorTable: [],\n                    reserved: 0,\n                    GCreserved: 0,\n                });\n                frameIndex++;\n                transparencyIndex = -1;\n                incrementFrameIndex = false;\n            }\n        } while (!(await parseBlock(byteStream, gif, avgAlpha, getframeIndex, getTransparencyIndex, progressCallback)));\n        gif.frames.length--;\n        for (const frame of gif.frames) {\n            if (frame.userInputDelayFlag && frame.delayTime === 0) {\n                gif.totalTime = Infinity;\n                break;\n            }\n            gif.totalTime += frame.delayTime;\n        }\n        return gif;\n    }\n    catch (error) {\n        if (error instanceof EvalError) {\n            throw new Error(`error while parsing frame ${frameIndex} \"${error.message}\"`);\n        }\n        throw error;\n    }\n}\n", "import { errorPrefix, getLogger, getStyleFromHsl } from \"tsparticles-engine\";\nimport { decodeGIF, getGIFLoopAmount } from \"./GifUtils/Utils\";\nconst currentColorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d.]+%?\\))|currentcolor/gi;\nfunction replaceColorSvg(imageShape, color, opacity) {\n    const { svgData } = imageShape;\n    if (!svgData) {\n        return \"\";\n    }\n    const colorStyle = getStyleFromHsl(color, opacity);\n    if (svgData.includes(\"fill\")) {\n        return svgData.replace(currentColorRegex, () => colorStyle);\n    }\n    const preFillIndex = svgData.indexOf(\">\");\n    return `${svgData.substring(0, preFillIndex)} fill=\"${colorStyle}\"${svgData.substring(preFillIndex)}`;\n}\nexport async function loadImage(image) {\n    return new Promise((resolve) => {\n        image.loading = true;\n        const img = new Image();\n        image.element = img;\n        img.addEventListener(\"load\", () => {\n            image.loading = false;\n            resolve();\n        });\n        img.addEventListener(\"error\", () => {\n            image.element = undefined;\n            image.error = true;\n            image.loading = false;\n            getLogger().error(`${errorPrefix} loading image: ${image.source}`);\n            resolve();\n        });\n        img.src = image.source;\n    });\n}\nexport async function loadGifImage(image) {\n    if (image.type !== \"gif\") {\n        await loadImage(image);\n        return;\n    }\n    image.loading = true;\n    try {\n        image.gifData = await decodeGIF(image.source);\n        image.gifLoopCount = getGIFLoopAmount(image.gifData) ?? 0;\n        if (image.gifLoopCount === 0) {\n            image.gifLoopCount = Infinity;\n        }\n    }\n    catch {\n        image.error = true;\n    }\n    image.loading = false;\n}\nexport async function downloadSvgImage(image) {\n    if (image.type !== \"svg\") {\n        await loadImage(image);\n        return;\n    }\n    image.loading = true;\n    const response = await fetch(image.source);\n    if (!response.ok) {\n        getLogger().error(`${errorPrefix} Image not found`);\n        image.error = true;\n    }\n    else {\n        image.svgData = await response.text();\n    }\n    image.loading = false;\n}\nexport function replaceImageColor(image, imageData, color, particle) {\n    const svgColoredData = replaceColorSvg(image, color, particle.opacity?.value ?? 1), imageRes = {\n        color,\n        gif: imageData.gif,\n        data: {\n            ...image,\n            svgData: svgColoredData,\n        },\n        loaded: false,\n        ratio: imageData.width / imageData.height,\n        replaceColor: imageData.replaceColor ?? imageData.replace_color,\n        source: imageData.src,\n    };\n    return new Promise((resolve) => {\n        const svg = new Blob([svgColoredData], { type: \"image/svg+xml\" }), domUrl = URL || window.URL || window.webkitURL || window, url = domUrl.createObjectURL(svg), img = new Image();\n        img.addEventListener(\"load\", () => {\n            imageRes.loaded = true;\n            imageRes.element = img;\n            resolve(imageRes);\n            domUrl.revokeObjectURL(url);\n        });\n        img.addEventListener(\"error\", async () => {\n            domUrl.revokeObjectURL(url);\n            const img2 = {\n                ...image,\n                error: false,\n                loading: true,\n            };\n            await loadImage(img2);\n            imageRes.loaded = true;\n            imageRes.element = img2.element;\n            resolve(imageRes);\n        });\n        img.src = url;\n    });\n}\n", "import { errorPrefix } from \"tsparticles-engine\";\nimport { replaceImageColor } from \"./Utils\";\nexport class ImageDrawer {\n    constructor(engine) {\n        this.loadImageShape = async (imageShape) => {\n            if (!this._engine.loadImage) {\n                throw new Error(`${errorPrefix} image shape not initialized`);\n            }\n            await this._engine.loadImage({\n                gif: imageShape.gif,\n                name: imageShape.name,\n                replaceColor: imageShape.replaceColor ?? imageShape.replace_color ?? false,\n                src: imageShape.src,\n            });\n        };\n        this._engine = engine;\n    }\n    addImage(image) {\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        this._engine.images.push(image);\n    }\n    draw(context, particle, radius, opacity, delta) {\n        const image = particle.image, element = image?.element;\n        if (!image) {\n            return;\n        }\n        context.globalAlpha = opacity;\n        if (image.gif && image.gifData) {\n            const offscreenCanvas = new OffscreenCanvas(image.gifData.width, image.gifData.height), offscreenContext = offscreenCanvas.getContext(\"2d\");\n            if (!offscreenContext) {\n                throw new Error(\"could not create offscreen canvas context\");\n            }\n            offscreenContext.imageSmoothingQuality = \"low\";\n            offscreenContext.imageSmoothingEnabled = false;\n            offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n            if (particle.gifLoopCount === undefined) {\n                particle.gifLoopCount = image.gifLoopCount ?? 0;\n            }\n            let frameIndex = particle.gifFrame ?? 0;\n            const pos = { x: -image.gifData.width * 0.5, y: -image.gifData.height * 0.5 }, frame = image.gifData.frames[frameIndex];\n            if (particle.gifTime === undefined) {\n                particle.gifTime = 0;\n            }\n            if (!frame.bitmap) {\n                return;\n            }\n            context.scale(radius / image.gifData.width, radius / image.gifData.height);\n            switch (frame.disposalMethod) {\n                case 4:\n                case 5:\n                case 6:\n                case 7:\n                case 0:\n                    offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                    context.drawImage(offscreenCanvas, pos.x, pos.y);\n                    offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                    break;\n                case 1:\n                    offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                    context.drawImage(offscreenCanvas, pos.x, pos.y);\n                    break;\n                case 2:\n                    offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                    context.drawImage(offscreenCanvas, pos.x, pos.y);\n                    offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                    if (image.gifData.globalColorTable.length === 0) {\n                        offscreenContext.putImageData(image.gifData.frames[0].image, pos.x + frame.left, pos.y + frame.top);\n                    }\n                    else {\n                        offscreenContext.putImageData(image.gifData.backgroundImage, pos.x, pos.y);\n                    }\n                    break;\n                case 3:\n                    {\n                        const previousImageData = offscreenContext.getImageData(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                        offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n                        context.drawImage(offscreenCanvas, pos.x, pos.y);\n                        offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                        offscreenContext.putImageData(previousImageData, 0, 0);\n                    }\n                    break;\n            }\n            particle.gifTime += delta.value;\n            if (particle.gifTime > frame.delayTime) {\n                particle.gifTime -= frame.delayTime;\n                if (++frameIndex >= image.gifData.frames.length) {\n                    if (--particle.gifLoopCount <= 0) {\n                        return;\n                    }\n                    frameIndex = 0;\n                    offscreenContext.clearRect(0, 0, offscreenCanvas.width, offscreenCanvas.height);\n                }\n                particle.gifFrame = frameIndex;\n            }\n            context.scale(image.gifData.width / radius, image.gifData.height / radius);\n        }\n        else if (element) {\n            const ratio = image.ratio, pos = {\n                x: -radius,\n                y: -radius,\n            };\n            context.drawImage(element, pos.x, pos.y, radius * 2, (radius * 2) / ratio);\n        }\n        context.globalAlpha = 1;\n    }\n    getSidesCount() {\n        return 12;\n    }\n    async init(container) {\n        const options = container.actualOptions;\n        if (!options.preload || !this._engine.loadImage) {\n            return;\n        }\n        for (const imageData of options.preload) {\n            await this._engine.loadImage(imageData);\n        }\n    }\n    loadShape(particle) {\n        if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n            return;\n        }\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        const imageData = particle.shapeData, image = this._engine.images.find((t) => t.name === imageData.name || t.source === imageData.src);\n        if (!image) {\n            this.loadImageShape(imageData).then(() => {\n                this.loadShape(particle);\n            });\n        }\n    }\n    particleInit(container, particle) {\n        if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n            return;\n        }\n        if (!this._engine.images) {\n            this._engine.images = [];\n        }\n        const images = this._engine.images, imageData = particle.shapeData, color = particle.getFillColor(), image = images.find((t) => t.name === imageData.name || t.source === imageData.src);\n        if (!image) {\n            return;\n        }\n        const replaceColor = imageData.replaceColor ?? imageData.replace_color ?? image.replaceColor;\n        if (image.loading) {\n            setTimeout(() => {\n                this.particleInit(container, particle);\n            });\n            return;\n        }\n        (async () => {\n            let imageRes;\n            if (image.svgData && color) {\n                imageRes = await replaceImageColor(image, imageData, color, particle);\n            }\n            else {\n                imageRes = {\n                    color,\n                    data: image,\n                    element: image.element,\n                    gif: image.gif,\n                    gifData: image.gifData,\n                    gifLoopCount: image.gifLoopCount,\n                    loaded: true,\n                    ratio: imageData.width && imageData.height ? imageData.width / imageData.height : image.ratio ?? 1,\n                    replaceColor: replaceColor,\n                    source: imageData.src,\n                };\n            }\n            if (!imageRes.ratio) {\n                imageRes.ratio = 1;\n            }\n            const fill = imageData.fill ?? particle.fill, close = imageData.close ?? particle.close, imageShape = {\n                image: imageRes,\n                fill,\n                close,\n            };\n            particle.image = imageShape.image;\n            particle.fill = imageShape.fill;\n            particle.close = imageShape.close;\n        })();\n    }\n}\n", "export class Preload {\n    constructor() {\n        this.src = \"\";\n        this.gif = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.gif !== undefined) {\n            this.gif = data.gif;\n        }\n        if (data.height !== undefined) {\n            this.height = data.height;\n        }\n        if (data.name !== undefined) {\n            this.name = data.name;\n        }\n        if (data.replaceColor !== undefined) {\n            this.replaceColor = data.replaceColor;\n        }\n        if (data.src !== undefined) {\n            this.src = data.src;\n        }\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n    }\n}\n", "import { Preload } from \"./Options/Classes/Preload\";\nexport class ImagePreloaderPlugin {\n    constructor(engine) {\n        this.id = \"imagePreloader\";\n        this._engine = engine;\n    }\n    getPlugin() {\n        return {};\n    }\n    loadOptions(options, source) {\n        if (!source || !source.preload) {\n            return;\n        }\n        if (!options.preload) {\n            options.preload = [];\n        }\n        const preloadOptions = options.preload;\n        for (const item of source.preload) {\n            const existing = preloadOptions.find((t) => t.name === item.name || t.src === item.src);\n            if (existing) {\n                existing.load(item);\n            }\n            else {\n                const preload = new Preload();\n                preload.load(item);\n                preloadOptions.push(preload);\n            }\n        }\n    }\n    needsPlugin() {\n        return true;\n    }\n}\n", "import { downloadSvgImage, loadGifImage, loadImage } from \"./Utils\";\nimport { ImageDrawer } from \"./ImageDrawer\";\nimport { ImagePreloaderPlugin } from \"./ImagePreloader\";\nimport { errorPrefix } from \"tsparticles-engine\";\nfunction addLoadImageToEngine(engine) {\n    if (engine.loadImage) {\n        return;\n    }\n    engine.loadImage = async (data) => {\n        if (!data.name && !data.src) {\n            throw new Error(`${errorPrefix} no image source provided`);\n        }\n        if (!engine.images) {\n            engine.images = [];\n        }\n        if (engine.images.find((t) => t.name === data.name || t.source === data.src)) {\n            return;\n        }\n        try {\n            const image = {\n                gif: data.gif ?? false,\n                name: data.name ?? data.src,\n                source: data.src,\n                type: data.src.substring(data.src.length - 3),\n                error: false,\n                loading: true,\n                replaceColor: data.replaceColor,\n                ratio: data.width && data.height ? data.width / data.height : undefined,\n            };\n            engine.images.push(image);\n            const imageFunc = data.gif ? loadGifImage : data.replaceColor ? downloadSvgImage : loadImage;\n            await imageFunc(image);\n        }\n        catch {\n            throw new Error(`${errorPrefix} ${data.name ?? data.src} not found`);\n        }\n    };\n}\nexport async function loadImageShape(engine, refresh = true) {\n    addLoadImageToEngine(engine);\n    const preloader = new ImagePreloaderPlugin(engine);\n    await engine.addPlugin(preloader, refresh);\n    await engine.addShape([\"image\", \"images\"], new ImageDrawer(engine), refresh);\n}\n", "import { ValueWithRandom } from \"tsparticles-engine\";\nexport class LifeDelay extends ValueWithRandom {\n    constructor() {\n        super();\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n", "import { ValueWithRandom } from \"tsparticles-engine\";\nexport class LifeDuration extends ValueWithRandom {\n    constructor() {\n        super();\n        this.random.minimumValue = 0.0001;\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n", "import { LifeDelay } from \"./LifeDelay\";\nimport { LifeDuration } from \"./LifeDuration\";\nexport class Life {\n    constructor() {\n        this.count = 0;\n        this.delay = new LifeDelay();\n        this.duration = new LifeDuration();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = data.count;\n        }\n        this.delay.load(data.delay);\n        this.duration.load(data.duration);\n    }\n}\n", "import { getRandom, getRange<PERSON><PERSON>ue, randomInRange, setRangeValue, } from \"tsparticles-engine\";\nimport { Life } from \"./Options/Classes/Life\";\nexport class LifeUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const container = this.container, particlesOptions = particle.options, lifeOptions = particlesOptions.life;\n        if (!lifeOptions) {\n            return;\n        }\n        particle.life = {\n            delay: container.retina.reduceFactor\n                ? ((getRangeValue(lifeOptions.delay.value) * (lifeOptions.delay.sync ? 1 : getRandom())) /\n                    container.retina.reduceFactor) *\n                    1000\n                : 0,\n            delayTime: 0,\n            duration: container.retina.reduceFactor\n                ? ((getRangeValue(lifeOptions.duration.value) * (lifeOptions.duration.sync ? 1 : getRandom())) /\n                    container.retina.reduceFactor) *\n                    1000\n                : 0,\n            time: 0,\n            count: lifeOptions.count,\n        };\n        if (particle.life.duration <= 0) {\n            particle.life.duration = -1;\n        }\n        if (particle.life.count <= 0) {\n            particle.life.count = -1;\n        }\n        if (particle.life) {\n            particle.spawning = particle.life.delay > 0;\n        }\n    }\n    isEnabled(particle) {\n        return !particle.destroyed;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.life) {\n            options.life = new Life();\n        }\n        for (const source of sources) {\n            options.life.load(source?.life);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle) || !particle.life) {\n            return;\n        }\n        const life = particle.life;\n        let justSpawned = false;\n        if (particle.spawning) {\n            life.delayTime += delta.value;\n            if (life.delayTime >= particle.life.delay) {\n                justSpawned = true;\n                particle.spawning = false;\n                life.delayTime = 0;\n                life.time = 0;\n            }\n            else {\n                return;\n            }\n        }\n        if (life.duration === -1) {\n            return;\n        }\n        if (particle.spawning) {\n            return;\n        }\n        if (justSpawned) {\n            life.time = 0;\n        }\n        else {\n            life.time += delta.value;\n        }\n        if (life.time < life.duration) {\n            return;\n        }\n        life.time = 0;\n        if (particle.life.count > 0) {\n            particle.life.count--;\n        }\n        if (particle.life.count === 0) {\n            particle.destroy();\n            return;\n        }\n        const canvasSize = this.container.canvas.size, widthRange = setRangeValue(0, canvasSize.width), heightRange = setRangeValue(0, canvasSize.width);\n        particle.position.x = randomInRange(widthRange);\n        particle.position.y = randomInRange(heightRange);\n        particle.spawning = true;\n        life.delayTime = 0;\n        life.time = 0;\n        particle.reset();\n        const lifeOptions = particle.options.life;\n        if (lifeOptions) {\n            life.delay = getRangeValue(lifeOptions.delay.value) * 1000;\n            life.duration = getRangeValue(lifeOptions.duration.value) * 1000;\n        }\n    }\n}\n", "import { LifeUpdater } from \"./LifeUpdater\";\nexport async function loadLifeUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"life\", (container) => new LifeUpdater(container), refresh);\n}\n", "export class LineDrawer {\n    draw(context, particle, radius) {\n        const shapeData = particle.shapeData;\n        context.moveTo(-radius / 2, 0);\n        context.lineTo(radius / 2, 0);\n        context.lineCap = shapeData?.cap ?? \"butt\";\n    }\n    getSidesCount() {\n        return 1;\n    }\n}\n", "import { LineDrawer } from \"./LineDrawer\";\nexport async function loadLineShape(engine, refresh = true) {\n    await engine.addShape(\"line\", new LineDrawer(), refresh);\n}\n", "import { isSsr } from \"tsparticles-engine\";\nexport class ParallaxMover {\n    init() {\n    }\n    isEnabled(particle) {\n        return (!isSsr() &&\n            !particle.destroyed &&\n            particle.container.actualOptions.interactivity.events.onHover.parallax.enable);\n    }\n    move(particle) {\n        const container = particle.container, options = container.actualOptions, parallaxOptions = options.interactivity.events.onHover.parallax;\n        if (isSsr() || !parallaxOptions.enable) {\n            return;\n        }\n        const parallaxForce = parallaxOptions.force, mousePos = container.interactivity.mouse.position;\n        if (!mousePos) {\n            return;\n        }\n        const canvasSize = container.canvas.size, canvasCenter = {\n            x: canvasSize.width / 2,\n            y: canvasSize.height / 2,\n        }, parallaxSmooth = parallaxOptions.smooth, factor = particle.getRadius() / parallaxForce, centerDistance = {\n            x: (mousePos.x - canvasCenter.x) * factor,\n            y: (mousePos.y - canvasCenter.y) * factor,\n        }, { offset } = particle;\n        offset.x += (centerDistance.x - offset.x) / parallaxSmooth;\n        offset.y += (centerDistance.y - offset.y) / parallaxSmooth;\n    }\n}\n", "import { ParallaxMover } from \"./ParallaxMover\";\nexport async function loadParallaxMover(engine, refresh = true) {\n    await engine.addMover(\"parallax\", () => new ParallaxMover(), refresh);\n}\n", "import { ParticlesInteractorBase, getDistances, } from \"tsparticles-engine\";\nexport class Attractor extends ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n    }\n    async interact(p1) {\n        const container = this.container, distance = p1.retina.attractDistance ?? container.retina.attractDistance, pos1 = p1.getPosition(), query = container.particles.quadTree.queryCircle(pos1, distance);\n        for (const p2 of query) {\n            if (p1 === p2 || !p2.options.move.attract.enable || p2.destroyed || p2.spawning) {\n                continue;\n            }\n            const pos2 = p2.getPosition(), { dx, dy } = getDistances(pos1, pos2), rotate = p1.options.move.attract.rotate, ax = dx / (rotate.x * 1000), ay = dy / (rotate.y * 1000), p1Factor = p2.size.value / p1.size.value, p2Factor = 1 / p1Factor;\n            p1.velocity.x -= ax * p1Factor;\n            p1.velocity.y -= ay * p1Factor;\n            p2.velocity.x += ax * p2Factor;\n            p2.velocity.y += ay * p2Factor;\n        }\n    }\n    isEnabled(particle) {\n        return particle.options.move.attract.enable;\n    }\n    reset() {\n    }\n}\n", "import { Attractor } from \"./Attractor\";\nexport async function loadParticlesAttractInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"particlesAttract\", (container) => new Attractor(container), refresh);\n}\n", "import { clamp } from \"tsparticles-engine\";\nfunction updateAbsorb(p1, r1, p2, r2, delta, pixelRatio) {\n    const factor = clamp((p1.options.collisions.absorb.speed * delta.factor) / 10, 0, r2);\n    p1.size.value += factor / 2;\n    p2.size.value -= factor;\n    if (r2 <= pixelRatio) {\n        p2.size.value = 0;\n        p2.destroy();\n    }\n}\nexport function absorb(p1, p2, delta, pixelRatio) {\n    const r1 = p1.getRadius(), r2 = p2.getRadius();\n    if (r1 === undefined && r2 !== undefined) {\n        p1.destroy();\n    }\n    else if (r1 !== undefined && r2 === undefined) {\n        p2.destroy();\n    }\n    else if (r1 !== undefined && r2 !== undefined) {\n        if (r1 >= r2) {\n            updateAbsorb(p1, r1, p2, r2, delta, pixelRatio);\n        }\n        else {\n            updateAbsorb(p2, r2, p1, r1, delta, pixelRatio);\n        }\n    }\n}\n", "import { circleBounce, circleBounceDataFromParticle, getRangeValue } from \"tsparticles-engine\";\nconst fixBounceSpeed = (p) => {\n    if (p.collisionMaxSpeed === undefined) {\n        p.collisionMaxSpeed = getRangeValue(p.options.collisions.maxSpeed);\n    }\n    if (p.velocity.length > p.collisionMaxSpeed) {\n        p.velocity.length = p.collisionMaxSpeed;\n    }\n};\nexport function bounce(p1, p2) {\n    circleBounce(circleBounceDataFromParticle(p1), circleBounceDataFromParticle(p2));\n    fixBounceSpeed(p1);\n    fixBounceSpeed(p2);\n}\n", "import { bounce } from \"./Bounce\";\nexport function destroy(p1, p2) {\n    if (!p1.unbreakable && !p2.unbreakable) {\n        bounce(p1, p2);\n    }\n    if (p1.getRadius() === undefined && p2.getRadius() !== undefined) {\n        p1.destroy();\n    }\n    else if (p1.getRadius() !== undefined && p2.getRadius() === undefined) {\n        p2.destroy();\n    }\n    else if (p1.getRadius() !== undefined && p2.getRadius() !== undefined) {\n        const deleteP = p1.getRadius() >= p2.getRadius() ? p2 : p1;\n        deleteP.destroy();\n    }\n}\n", "import { absorb } from \"./Absorb\";\nimport { bounce } from \"./Bounce\";\nimport { destroy } from \"./Destroy\";\nexport function resolveCollision(p1, p2, delta, pixelRatio) {\n    switch (p1.options.collisions.mode) {\n        case \"absorb\": {\n            absorb(p1, p2, delta, pixelRatio);\n            break;\n        }\n        case \"bounce\": {\n            bounce(p1, p2);\n            break;\n        }\n        case \"destroy\": {\n            destroy(p1, p2);\n            break;\n        }\n    }\n}\n", "import { ParticlesInteractorBase, getDistance } from \"tsparticles-engine\";\nimport { resolveCollision } from \"./ResolveCollision\";\nexport class Collider extends ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n    }\n    clear() {\n    }\n    init() {\n    }\n    async interact(p1, delta) {\n        if (p1.destroyed || p1.spawning) {\n            return;\n        }\n        const container = this.container, pos1 = p1.getPosition(), radius1 = p1.getRadius(), query = container.particles.quadTree.queryCircle(pos1, radius1 * 2);\n        for (const p2 of query) {\n            if (p1 === p2 ||\n                !p2.options.collisions.enable ||\n                p1.options.collisions.mode !== p2.options.collisions.mode ||\n                p2.destroyed ||\n                p2.spawning) {\n                continue;\n            }\n            const pos2 = p2.getPosition(), radius2 = p2.getRadius();\n            if (Math.abs(Math.round(pos1.z) - Math.round(pos2.z)) > radius1 + radius2) {\n                continue;\n            }\n            const dist = getDistance(pos1, pos2), distP = radius1 + radius2;\n            if (dist > distP) {\n                continue;\n            }\n            resolveCollision(p1, p2, delta, container.retina.pixelRatio);\n        }\n    }\n    isEnabled(particle) {\n        return particle.options.collisions.enable;\n    }\n    reset() {\n    }\n}\n", "import { Collider } from \"./Collider\";\nexport async function loadParticlesCollisionsInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"particlesCollisions\", (container) => new Collider(container), refresh);\n}\n", "import { Circle, Rectangle } from \"tsparticles-engine\";\nexport class CircleWarp extends Circle {\n    constructor(x, y, radius, canvasSize) {\n        super(x, y, radius);\n        this.canvasSize = canvasSize;\n        this.canvasSize = { ...canvasSize };\n    }\n    contains(point) {\n        const { width, height } = this.canvasSize;\n        const { x, y } = point;\n        return (super.contains(point) ||\n            super.contains({ x: x - width, y }) ||\n            super.contains({ x: x - width, y: y - height }) ||\n            super.contains({ x, y: y - height }));\n    }\n    intersects(range) {\n        if (super.intersects(range)) {\n            return true;\n        }\n        const rect = range, circle = range, newPos = {\n            x: range.position.x - this.canvasSize.width,\n            y: range.position.y - this.canvasSize.height,\n        };\n        if (circle.radius !== undefined) {\n            const biggerCircle = new Circle(newPos.x, newPos.y, circle.radius * 2);\n            return super.intersects(biggerCircle);\n        }\n        else if (rect.size !== undefined) {\n            const rectSW = new Rectangle(newPos.x, newPos.y, rect.size.width * 2, rect.size.height * 2);\n            return super.intersects(rectSW);\n        }\n        return false;\n    }\n}\n", "import { OptionsColor } from \"tsparticles-engine\";\nexport class LinksShadow {\n    constructor() {\n        this.blur = 5;\n        this.color = new OptionsColor();\n        this.color.value = \"#000\";\n        this.enable = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.blur !== undefined) {\n            this.blur = data.blur;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n", "import { OptionsColor } from \"tsparticles-engine\";\nexport class LinksTriangle {\n    constructor() {\n        this.enable = false;\n        this.frequency = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n", "import { OptionsColor } from \"tsparticles-engine\";\nimport { LinksShadow } from \"./LinksShadow\";\nimport { LinksTriangle } from \"./LinksTriangle\";\nexport class Links {\n    constructor() {\n        this.blink = false;\n        this.color = new OptionsColor();\n        this.color.value = \"#fff\";\n        this.consent = false;\n        this.distance = 100;\n        this.enable = false;\n        this.frequency = 1;\n        this.opacity = 1;\n        this.shadow = new LinksShadow();\n        this.triangles = new LinksTriangle();\n        this.width = 1;\n        this.warp = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.id !== undefined) {\n            this.id = data.id;\n        }\n        if (data.blink !== undefined) {\n            this.blink = data.blink;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.consent !== undefined) {\n            this.consent = data.consent;\n        }\n        if (data.distance !== undefined) {\n            this.distance = data.distance;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.frequency !== undefined) {\n            this.frequency = data.frequency;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n        this.shadow.load(data.shadow);\n        this.triangles.load(data.triangles);\n        if (data.width !== undefined) {\n            this.width = data.width;\n        }\n        if (data.warp !== undefined) {\n            this.warp = data.warp;\n        }\n    }\n}\n", "import { Circle, ParticlesInteractorBase, getDistances, getLinkRandomColor, } from \"tsparticles-engine\";\nimport { CircleWarp } from \"./CircleWarp\";\nimport { Links } from \"./Options/Classes/Links\";\nfunction getLinkDistance(pos1, pos2, optDistance, canvasSize, warp) {\n    const { dx, dy, distance } = getDistances(pos1, pos2);\n    if (!warp || distance <= optDistance) {\n        return distance;\n    }\n    const absDiffs = {\n        x: Math.abs(dx),\n        y: Math.abs(dy),\n    }, warpDistances = {\n        x: Math.min(absDiffs.x, canvasSize.width - absDiffs.x),\n        y: Math.min(absDiffs.y, canvasSize.height - absDiffs.y),\n    };\n    return Math.sqrt(warpDistances.x ** 2 + warpDistances.y ** 2);\n}\nexport class Linker extends ParticlesInteractorBase {\n    constructor(container) {\n        super(container);\n        this._setColor = (p1) => {\n            if (!p1.options.links) {\n                return;\n            }\n            const container = this.linkContainer, linksOptions = p1.options.links;\n            let linkColor = linksOptions.id === undefined\n                ? container.particles.linksColor\n                : container.particles.linksColors.get(linksOptions.id);\n            if (linkColor) {\n                return;\n            }\n            const optColor = linksOptions.color;\n            linkColor = getLinkRandomColor(optColor, linksOptions.blink, linksOptions.consent);\n            if (linksOptions.id === undefined) {\n                container.particles.linksColor = linkColor;\n            }\n            else {\n                container.particles.linksColors.set(linksOptions.id, linkColor);\n            }\n        };\n        this.linkContainer = container;\n    }\n    clear() {\n    }\n    init() {\n        this.linkContainer.particles.linksColor = undefined;\n        this.linkContainer.particles.linksColors = new Map();\n    }\n    async interact(p1) {\n        if (!p1.options.links) {\n            return;\n        }\n        p1.links = [];\n        const pos1 = p1.getPosition(), container = this.container, canvasSize = container.canvas.size;\n        if (pos1.x < 0 || pos1.y < 0 || pos1.x > canvasSize.width || pos1.y > canvasSize.height) {\n            return;\n        }\n        const linkOpt1 = p1.options.links, optOpacity = linkOpt1.opacity, optDistance = p1.retina.linksDistance ?? 0, warp = linkOpt1.warp, range = warp\n            ? new CircleWarp(pos1.x, pos1.y, optDistance, canvasSize)\n            : new Circle(pos1.x, pos1.y, optDistance), query = container.particles.quadTree.query(range);\n        for (const p2 of query) {\n            const linkOpt2 = p2.options.links;\n            if (p1 === p2 ||\n                !linkOpt2?.enable ||\n                linkOpt1.id !== linkOpt2.id ||\n                p2.spawning ||\n                p2.destroyed ||\n                !p2.links ||\n                p1.links.some((t) => t.destination === p2) ||\n                p2.links.some((t) => t.destination === p1)) {\n                continue;\n            }\n            const pos2 = p2.getPosition();\n            if (pos2.x < 0 || pos2.y < 0 || pos2.x > canvasSize.width || pos2.y > canvasSize.height) {\n                continue;\n            }\n            const distance = getLinkDistance(pos1, pos2, optDistance, canvasSize, warp && linkOpt2.warp);\n            if (distance > optDistance) {\n                continue;\n            }\n            const opacityLine = (1 - distance / optDistance) * optOpacity;\n            this._setColor(p1);\n            p1.links.push({\n                destination: p2,\n                opacity: opacityLine,\n            });\n        }\n    }\n    isEnabled(particle) {\n        return !!particle.options.links?.enable;\n    }\n    loadParticlesOptions(options, ...sources) {\n        if (!options.links) {\n            options.links = new Links();\n        }\n        for (const source of sources) {\n            options.links.load(source?.links ?? source?.lineLinked ?? source?.line_linked);\n        }\n    }\n    reset() {\n    }\n}\n", "import { Linker } from \"./Linker\";\nexport async function loadLinksInteraction(engine, refresh = true) {\n    await engine.addInteractor(\"particlesLinks\", (container) => new Linker(container), refresh);\n}\n", "import { drawLine, drawTriangle, getDistance, getDistances, getRandom, getStyleFromRgb, rangeColorToRgb, } from \"tsparticles-engine\";\nexport function drawLinkLine(params) {\n    let drawn = false;\n    const { begin, end, maxDistance, context, canvasSize, width, backgroundMask, colorLine, opacity, links } = params;\n    if (getDistance(begin, end) <= maxDistance) {\n        drawLine(context, begin, end);\n        drawn = true;\n    }\n    else if (links.warp) {\n        let pi1;\n        let pi2;\n        const endNE = {\n            x: end.x - canvasSize.width,\n            y: end.y,\n        };\n        const d1 = getDistances(begin, endNE);\n        if (d1.distance <= maxDistance) {\n            const yi = begin.y - (d1.dy / d1.dx) * begin.x;\n            pi1 = { x: 0, y: yi };\n            pi2 = { x: canvasSize.width, y: yi };\n        }\n        else {\n            const endSW = {\n                x: end.x,\n                y: end.y - canvasSize.height,\n            };\n            const d2 = getDistances(begin, endSW);\n            if (d2.distance <= maxDistance) {\n                const yi = begin.y - (d2.dy / d2.dx) * begin.x;\n                const xi = -yi / (d2.dy / d2.dx);\n                pi1 = { x: xi, y: 0 };\n                pi2 = { x: xi, y: canvasSize.height };\n            }\n            else {\n                const endSE = {\n                    x: end.x - canvasSize.width,\n                    y: end.y - canvasSize.height,\n                };\n                const d3 = getDistances(begin, endSE);\n                if (d3.distance <= maxDistance) {\n                    const yi = begin.y - (d3.dy / d3.dx) * begin.x;\n                    const xi = -yi / (d3.dy / d3.dx);\n                    pi1 = { x: xi, y: yi };\n                    pi2 = { x: pi1.x + canvasSize.width, y: pi1.y + canvasSize.height };\n                }\n            }\n        }\n        if (pi1 && pi2) {\n            drawLine(context, begin, pi1);\n            drawLine(context, end, pi2);\n            drawn = true;\n        }\n    }\n    if (!drawn) {\n        return;\n    }\n    context.lineWidth = width;\n    if (backgroundMask.enable) {\n        context.globalCompositeOperation = backgroundMask.composite;\n    }\n    context.strokeStyle = getStyleFromRgb(colorLine, opacity);\n    const { shadow } = links;\n    if (shadow.enable) {\n        const shadowColor = rangeColorToRgb(shadow.color);\n        if (shadowColor) {\n            context.shadowBlur = shadow.blur;\n            context.shadowColor = getStyleFromRgb(shadowColor);\n        }\n    }\n    context.stroke();\n}\nexport function drawLinkTriangle(params) {\n    const { context, pos1, pos2, pos3, backgroundMask, colorTriangle, opacityTriangle } = params;\n    drawTriangle(context, pos1, pos2, pos3);\n    if (backgroundMask.enable) {\n        context.globalCompositeOperation = backgroundMask.composite;\n    }\n    context.fillStyle = getStyleFromRgb(colorTriangle, opacityTriangle);\n    context.fill();\n}\nexport function getLinkKey(ids) {\n    ids.sort((a, b) => a - b);\n    return ids.join(\"_\");\n}\nexport function setLinkFrequency(particles, dictionary) {\n    const key = getLinkKey(particles.map((t) => t.id));\n    let res = dictionary.get(key);\n    if (res === undefined) {\n        res = getRandom();\n        dictionary.set(key, res);\n    }\n    return res;\n}\n", "import { getDistance, getLinkColor, getRandom, getRangeValue, rangeColorToRgb, } from \"tsparticles-engine\";\nimport { drawLinkLine, drawLinkTriangle, setLinkFrequency } from \"./Utils\";\nexport class LinkInstance {\n    constructor(container) {\n        this.container = container;\n        this._drawLinkLine = (p1, link) => {\n            const p1LinksOptions = p1.options.links;\n            if (!p1LinksOptions?.enable) {\n                return;\n            }\n            const container = this.container, options = container.actualOptions, p2 = link.destination, pos1 = p1.getPosition(), pos2 = p2.getPosition();\n            let opacity = link.opacity;\n            container.canvas.draw((ctx) => {\n                let colorLine;\n                const twinkle = p1.options.twinkle?.lines;\n                if (twinkle?.enable) {\n                    const twinkleFreq = twinkle.frequency, twinkleRgb = rangeColorToRgb(twinkle.color), twinkling = getRandom() < twinkleFreq;\n                    if (twinkling && twinkleRgb) {\n                        colorLine = twinkleRgb;\n                        opacity = getRangeValue(twinkle.opacity);\n                    }\n                }\n                if (!colorLine) {\n                    const linkColor = p1LinksOptions.id !== undefined\n                        ? container.particles.linksColors.get(p1LinksOptions.id)\n                        : container.particles.linksColor;\n                    colorLine = getLinkColor(p1, p2, linkColor);\n                }\n                if (!colorLine) {\n                    return;\n                }\n                const width = p1.retina.linksWidth ?? 0, maxDistance = p1.retina.linksDistance ?? 0, { backgroundMask } = options;\n                drawLinkLine({\n                    context: ctx,\n                    width,\n                    begin: pos1,\n                    end: pos2,\n                    maxDistance,\n                    canvasSize: container.canvas.size,\n                    links: p1LinksOptions,\n                    backgroundMask: backgroundMask,\n                    colorLine,\n                    opacity,\n                });\n            });\n        };\n        this._drawLinkTriangle = (p1, link1, link2) => {\n            const linksOptions = p1.options.links;\n            if (!linksOptions?.enable) {\n                return;\n            }\n            const triangleOptions = linksOptions.triangles;\n            if (!triangleOptions.enable) {\n                return;\n            }\n            const container = this.container, options = container.actualOptions, p2 = link1.destination, p3 = link2.destination, opacityTriangle = triangleOptions.opacity ?? (link1.opacity + link2.opacity) / 2;\n            if (opacityTriangle <= 0) {\n                return;\n            }\n            container.canvas.draw((ctx) => {\n                const pos1 = p1.getPosition(), pos2 = p2.getPosition(), pos3 = p3.getPosition(), linksDistance = p1.retina.linksDistance ?? 0;\n                if (getDistance(pos1, pos2) > linksDistance ||\n                    getDistance(pos3, pos2) > linksDistance ||\n                    getDistance(pos3, pos1) > linksDistance) {\n                    return;\n                }\n                let colorTriangle = rangeColorToRgb(triangleOptions.color);\n                if (!colorTriangle) {\n                    const linkColor = linksOptions.id !== undefined\n                        ? container.particles.linksColors.get(linksOptions.id)\n                        : container.particles.linksColor;\n                    colorTriangle = getLinkColor(p1, p2, linkColor);\n                }\n                if (!colorTriangle) {\n                    return;\n                }\n                drawLinkTriangle({\n                    context: ctx,\n                    pos1,\n                    pos2,\n                    pos3,\n                    backgroundMask: options.backgroundMask,\n                    colorTriangle,\n                    opacityTriangle,\n                });\n            });\n        };\n        this._drawTriangles = (options, p1, link, p1Links) => {\n            const p2 = link.destination;\n            if (!(options.links?.triangles.enable && p2.options.links?.triangles.enable)) {\n                return;\n            }\n            const vertices = p2.links?.filter((t) => {\n                const linkFreq = this._getLinkFrequency(p2, t.destination);\n                return (p2.options.links &&\n                    linkFreq <= p2.options.links.frequency &&\n                    p1Links.findIndex((l) => l.destination === t.destination) >= 0);\n            });\n            if (!vertices?.length) {\n                return;\n            }\n            for (const vertex of vertices) {\n                const p3 = vertex.destination, triangleFreq = this._getTriangleFrequency(p1, p2, p3);\n                if (triangleFreq > options.links.triangles.frequency) {\n                    continue;\n                }\n                this._drawLinkTriangle(p1, link, vertex);\n            }\n        };\n        this._getLinkFrequency = (p1, p2) => {\n            return setLinkFrequency([p1, p2], this._freqs.links);\n        };\n        this._getTriangleFrequency = (p1, p2, p3) => {\n            return setLinkFrequency([p1, p2, p3], this._freqs.triangles);\n        };\n        this._freqs = {\n            links: new Map(),\n            triangles: new Map(),\n        };\n    }\n    drawParticle(context, particle) {\n        const { links, options } = particle;\n        if (!links || links.length <= 0) {\n            return;\n        }\n        const p1Links = links.filter((l) => options.links && this._getLinkFrequency(particle, l.destination) <= options.links.frequency);\n        for (const link of p1Links) {\n            this._drawTriangles(options, particle, link, p1Links);\n            if (link.opacity > 0 && (particle.retina.linksWidth ?? 0) > 0) {\n                this._drawLinkLine(particle, link);\n            }\n        }\n    }\n    async init() {\n        this._freqs.links = new Map();\n        this._freqs.triangles = new Map();\n    }\n    particleCreated(particle) {\n        particle.links = [];\n        if (!particle.options.links) {\n            return;\n        }\n        const ratio = this.container.retina.pixelRatio, { retina } = particle, { distance, width } = particle.options.links;\n        retina.linksDistance = distance * ratio;\n        retina.linksWidth = width * ratio;\n    }\n    particleDestroyed(particle) {\n        particle.links = [];\n    }\n}\n", "import { LinkInstance } from \"./LinkInstance\";\nclass LinksPlugin {\n    constructor() {\n        this.id = \"links\";\n    }\n    getPlugin(container) {\n        return new LinkInstance(container);\n    }\n    loadOptions() {\n    }\n    needsPlugin() {\n        return true;\n    }\n}\nexport async function loadLinksPlugin(engine, refresh = true) {\n    const plugin = new LinksPlugin();\n    await engine.addPlugin(plugin, refresh);\n}\n", "import { loadLinksInteraction } from \"./interaction\";\nimport { loadLinksPlugin } from \"./plugin\";\nexport async function loadParticlesLinksInteraction(engine, refresh = true) {\n    await loadLinksInteraction(engine, refresh);\n    await loadLinksPlugin(engine, refresh);\n}\nexport * from \"./Options/Classes/Links\";\nexport * from \"./Options/Classes/LinksShadow\";\nexport * from \"./Options/Classes/LinksTriangle\";\nexport * from \"./Options/Interfaces/ILinks\";\nexport * from \"./Options/Interfaces/ILinksShadow\";\nexport * from \"./Options/Interfaces/ILinksTriangle\";\n", "import { getRangeValue } from \"tsparticles-engine\";\nexport class PolygonDrawerBase {\n    draw(context, particle, radius) {\n        const start = this.getCenter(particle, radius), side = this.getSidesData(particle, radius), sideCount = side.count.numerator * side.count.denominator, decimalSides = side.count.numerator / side.count.denominator, interiorAngleDegrees = (180 * (decimalSides - 2)) / decimalSides, interiorAngle = Math.PI - (Math.PI * interiorAngleDegrees) / 180;\n        if (!context) {\n            return;\n        }\n        context.beginPath();\n        context.translate(start.x, start.y);\n        context.moveTo(0, 0);\n        for (let i = 0; i < sideCount; i++) {\n            context.lineTo(side.length, 0);\n            context.translate(side.length, 0);\n            context.rotate(interiorAngle);\n        }\n    }\n    getSidesCount(particle) {\n        const polygon = particle.shapeData;\n        return Math.round(getRangeValue(polygon?.sides ?? polygon?.nb_sides ?? 5));\n    }\n}\n", "import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class PolygonDrawer extends PolygonDrawerBase {\n    getCenter(particle, radius) {\n        return {\n            x: -radius / (particle.sides / 3.5),\n            y: -radius / (2.66 / 3.5),\n        };\n    }\n    getSidesData(particle, radius) {\n        const sides = particle.sides;\n        return {\n            count: {\n                denominator: 1,\n                numerator: sides,\n            },\n            length: (radius * 2.66) / (sides / 3),\n        };\n    }\n}\n", "import { PolygonDrawerBase } from \"./PolygonDrawerBase\";\nexport class TriangleDrawer extends PolygonDrawerBase {\n    getCenter(particle, radius) {\n        return {\n            x: -radius,\n            y: radius / 1.66,\n        };\n    }\n    getSidesCount() {\n        return 3;\n    }\n    getSidesData(particle, radius) {\n        return {\n            count: {\n                denominator: 2,\n                numerator: 3,\n            },\n            length: radius * 2,\n        };\n    }\n}\n", "import { PolygonDrawer } from \"./PolygonDrawer\";\nimport { TriangleDrawer } from \"./TriangleDrawer\";\nexport async function loadGenericPolygonShape(engine, refresh = true) {\n    await engine.addShape(\"polygon\", new PolygonDrawer(), refresh);\n}\nexport async function loadTriangleShape(engine, refresh = true) {\n    await engine.addShape(\"triangle\", new TriangleDrawer(), refresh);\n}\nexport async function loadPolygonShape(engine, refresh = true) {\n    await loadGenericPolygonShape(engine, refresh);\n    await loadTriangleShape(engine, refresh);\n}\n", "import { setRangeValue } from \"tsparticles-engine\";\nexport class RotateAnimation {\n    constructor() {\n        this.enable = false;\n        this.speed = 0;\n        this.decay = 0;\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.decay !== undefined) {\n            this.decay = setRangeValue(data.decay);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n", "import { ValueWithRandom, } from \"tsparticles-engine\";\nimport { RotateAnimation } from \"./RotateAnimation\";\nexport class Rotate extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new RotateAnimation();\n        this.direction = \"clockwise\";\n        this.path = false;\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        this.animation.load(data.animation);\n        if (data.path !== undefined) {\n            this.path = data.path;\n        }\n    }\n}\n", "import { getRandom, getRangeValue, } from \"tsparticles-engine\";\nimport { Rotate } from \"./Options/Classes/Rotate\";\nfunction updateRotate(particle, delta) {\n    const rotate = particle.rotate, rotateOptions = particle.options.rotate;\n    if (!rotate || !rotateOptions) {\n        return;\n    }\n    const rotateAnimation = rotateOptions.animation, speed = (rotate.velocity ?? 0) * delta.factor, max = 2 * Math.PI, decay = rotate.decay ?? 1;\n    if (!rotateAnimation.enable) {\n        return;\n    }\n    switch (rotate.status) {\n        case \"increasing\":\n            rotate.value += speed;\n            if (rotate.value > max) {\n                rotate.value -= max;\n            }\n            break;\n        case \"decreasing\":\n        default:\n            rotate.value -= speed;\n            if (rotate.value < 0) {\n                rotate.value += max;\n            }\n            break;\n    }\n    if (rotate.velocity && decay !== 1) {\n        rotate.velocity *= decay;\n    }\n}\nexport class RotateUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const rotateOptions = particle.options.rotate;\n        if (!rotateOptions) {\n            return;\n        }\n        particle.rotate = {\n            enable: rotateOptions.animation.enable,\n            value: (getRangeValue(rotateOptions.value) * Math.PI) / 180,\n        };\n        particle.pathRotation = rotateOptions.path;\n        let rotateDirection = rotateOptions.direction;\n        if (rotateDirection === \"random\") {\n            const index = Math.floor(getRandom() * 2);\n            rotateDirection = index > 0 ? \"counter-clockwise\" : \"clockwise\";\n        }\n        switch (rotateDirection) {\n            case \"counter-clockwise\":\n            case \"counterClockwise\":\n                particle.rotate.status = \"decreasing\";\n                break;\n            case \"clockwise\":\n                particle.rotate.status = \"increasing\";\n                break;\n        }\n        const rotateAnimation = rotateOptions.animation;\n        if (rotateAnimation.enable) {\n            particle.rotate.decay = 1 - getRangeValue(rotateAnimation.decay);\n            particle.rotate.velocity =\n                (getRangeValue(rotateAnimation.speed) / 360) * this.container.retina.reduceFactor;\n            if (!rotateAnimation.sync) {\n                particle.rotate.velocity *= getRandom();\n            }\n        }\n        particle.rotation = particle.rotate.value;\n    }\n    isEnabled(particle) {\n        const rotate = particle.options.rotate;\n        if (!rotate) {\n            return false;\n        }\n        return !particle.destroyed && !particle.spawning && rotate.animation.enable && !rotate.path;\n    }\n    loadOptions(options, ...sources) {\n        if (!options.rotate) {\n            options.rotate = new Rotate();\n        }\n        for (const source of sources) {\n            options.rotate.load(source?.rotate);\n        }\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateRotate(particle, delta);\n        particle.rotation = particle.rotate?.value ?? 0;\n    }\n}\n", "import { RotateUpdater } from \"./RotateUpdater\";\nexport async function loadRotateUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"rotate\", (container) => new RotateUpdater(container), refresh);\n}\n", "const fixFactor = Math.sqrt(2);\nexport class SquareDrawer {\n    draw(context, particle, radius) {\n        const fixedRadius = radius / fixFactor, fixedDiameter = fixedRadius * 2;\n        context.rect(-fixedRadius, -fixedRadius, fixedDiameter, fixedDiameter);\n    }\n    getSidesCount() {\n        return 4;\n    }\n}\n", "import { SquareDrawer } from \"./SquareDrawer\";\nexport async function loadSquareShape(engine, refresh = true) {\n    await engine.addShape([\"edge\", \"square\"], new SquareDrawer(), refresh);\n}\n", "import { getRangeValue } from \"tsparticles-engine\";\nexport class StarDrawer {\n    draw(context, particle, radius) {\n        const sides = particle.sides, inset = particle.starInset ?? 2;\n        context.moveTo(0, 0 - radius);\n        for (let i = 0; i < sides; i++) {\n            context.rotate(Math.PI / sides);\n            context.lineTo(0, 0 - radius * inset);\n            context.rotate(Math.PI / sides);\n            context.lineTo(0, 0 - radius);\n        }\n    }\n    getSidesCount(particle) {\n        const star = particle.shapeData;\n        return Math.round(getRangeValue(star?.sides ?? star?.nb_sides ?? 5));\n    }\n    particleInit(container, particle) {\n        const star = particle.shapeData, inset = getRangeValue(star?.inset ?? 2);\n        particle.starInset = inset;\n    }\n}\n", "import { StarDrawer } from \"./StarDrawer\";\nexport async function loadStarShape(engine, refresh = true) {\n    await engine.addShape(\"star\", new StarDrawer(), refresh);\n}\n", "import { randomInRange, } from \"tsparticles-engine\";\nfunction updateColorValue(delta, colorValue, valueAnimation, max, decrease) {\n    if (!colorValue ||\n        !valueAnimation.enable ||\n        ((colorValue.maxLoops ?? 0) > 0 && (colorValue.loops ?? 0) > (colorValue.maxLoops ?? 0))) {\n        return;\n    }\n    if (!colorValue.time) {\n        colorValue.time = 0;\n    }\n    if ((colorValue.delayTime ?? 0) > 0 && colorValue.time < (colorValue.delayTime ?? 0)) {\n        colorValue.time += delta.value;\n    }\n    if ((colorValue.delayTime ?? 0) > 0 && colorValue.time < (colorValue.delayTime ?? 0)) {\n        return;\n    }\n    const offset = randomInRange(valueAnimation.offset), velocity = (colorValue.velocity ?? 0) * delta.factor + offset * 3.6, decay = colorValue.decay ?? 1;\n    if (!decrease || colorValue.status === \"increasing\") {\n        colorValue.value += velocity;\n        if (colorValue.value > max) {\n            if (!colorValue.loops) {\n                colorValue.loops = 0;\n            }\n            colorValue.loops++;\n            if (decrease) {\n                colorValue.status = \"decreasing\";\n                colorValue.value -= colorValue.value % max;\n            }\n        }\n    }\n    else {\n        colorValue.value -= velocity;\n        if (colorValue.value < 0) {\n            if (!colorValue.loops) {\n                colorValue.loops = 0;\n            }\n            colorValue.loops++;\n            colorValue.status = \"increasing\";\n            colorValue.value += colorValue.value;\n        }\n    }\n    if (colorValue.velocity && decay !== 1) {\n        colorValue.velocity *= decay;\n    }\n    if (colorValue.value > max) {\n        colorValue.value %= max;\n    }\n}\nexport function updateStrokeColor(particle, delta) {\n    if (!particle.strokeColor || !particle.strokeAnimation) {\n        return;\n    }\n    const { h, s, l } = particle.strokeColor, { h: hAnimation, s: sAnimation, l: lAnimation } = particle.strokeAnimation;\n    if (h) {\n        updateColorValue(delta, h, hAnimation, 360, false);\n    }\n    if (s) {\n        updateColorValue(delta, s, sAnimation, 100, true);\n    }\n    if (l) {\n        updateColorValue(delta, l, lAnimation, 100, true);\n    }\n}\n", "import { getHslAnimationFromHsl, getRangeValue, itemFromSingleOrMultiple, rangeColorToHsl, } from \"tsparticles-engine\";\nimport { updateStrokeColor } from \"./Utils\";\nexport class StrokeColorUpdater {\n    constructor(container) {\n        this.container = container;\n    }\n    init(particle) {\n        const container = this.container, options = particle.options;\n        const stroke = itemFromSingleOrMultiple(options.stroke, particle.id, options.reduceDuplicates);\n        particle.strokeWidth = getRangeValue(stroke.width) * container.retina.pixelRatio;\n        particle.strokeOpacity = getRangeValue(stroke.opacity ?? 1);\n        particle.strokeAnimation = stroke.color?.animation;\n        const strokeHslColor = rangeColorToHsl(stroke.color) ?? particle.getFillColor();\n        if (strokeHslColor) {\n            particle.strokeColor = getHslAnimationFromHsl(strokeHslColor, particle.strokeAnimation, container.retina.reduceFactor);\n        }\n    }\n    isEnabled(particle) {\n        const color = particle.strokeAnimation, { strokeColor } = particle;\n        return (!particle.destroyed &&\n            !particle.spawning &&\n            !!color &&\n            ((strokeColor?.h.value !== undefined && strokeColor.h.enable) ||\n                (strokeColor?.s.value !== undefined && strokeColor.s.enable) ||\n                (strokeColor?.l.value !== undefined && strokeColor.l.enable)));\n    }\n    update(particle, delta) {\n        if (!this.isEnabled(particle)) {\n            return;\n        }\n        updateStrokeColor(particle, delta);\n    }\n}\n", "import { StrokeColorUpdater } from \"./StrokeColorUpdater\";\nexport async function loadStrokeColorUpdater(engine, refresh = true) {\n    await engine.addParticleUpdater(\"strokeColor\", (container) => new StrokeColorUpdater(container), refresh);\n}\n", "import { executeOnSingleOrMultiple, isInArray, itemFromSingleOrMultiple, loadFont, } from \"tsparticles-engine\";\nexport const validTypes = [\"text\", \"character\", \"char\"];\nexport class TextDrawer {\n    draw(context, particle, radius, opacity) {\n        const character = particle.shapeData;\n        if (character === undefined) {\n            return;\n        }\n        const textData = character.value;\n        if (textData === undefined) {\n            return;\n        }\n        if (particle.text === undefined) {\n            particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n        }\n        const text = particle.text, style = character.style ?? \"\", weight = character.weight ?? \"400\", size = Math.round(radius) * 2, font = character.font ?? \"Verdana\", fill = particle.fill, offsetX = (text.length * radius) / 2;\n        context.font = `${style} ${weight} ${size}px \"${font}\"`;\n        const pos = {\n            x: -offsetX,\n            y: radius / 2,\n        };\n        context.globalAlpha = opacity;\n        if (fill) {\n            context.fillText(text, pos.x, pos.y);\n        }\n        else {\n            context.strokeText(text, pos.x, pos.y);\n        }\n        context.globalAlpha = 1;\n    }\n    getSidesCount() {\n        return 12;\n    }\n    async init(container) {\n        const options = container.actualOptions;\n        if (validTypes.find((t) => isInArray(t, options.particles.shape.type))) {\n            const shapeOptions = validTypes\n                .map((t) => options.particles.shape.options[t])\n                .find((t) => !!t), promises = [];\n            executeOnSingleOrMultiple(shapeOptions, (shape) => {\n                promises.push(loadFont(shape.font, shape.weight));\n            });\n            await Promise.all(promises);\n        }\n    }\n    particleInit(container, particle) {\n        if (!particle.shape || !validTypes.includes(particle.shape)) {\n            return;\n        }\n        const character = particle.shapeData;\n        if (character === undefined) {\n            return;\n        }\n        const textData = character.value;\n        if (textData === undefined) {\n            return;\n        }\n        particle.text = itemFromSingleOrMultiple(textData, particle.randomIndexData);\n    }\n}\n", "import { TextDrawer, validTypes } from \"./TextDrawer\";\nexport async function loadTextShape(engine, refresh = true) {\n    await engine.addShape(validTypes, new TextDrawer(), refresh);\n}\n", "import { initPjs } from \"tsparticles-particles.js\";\nimport { loadBasic } from \"tsparticles-basic\";\nimport { loadEasingQuadPlugin } from \"tsparticles-plugin-easing-quad\";\nimport { loadExternalAttractInteraction } from \"tsparticles-interaction-external-attract\";\nimport { loadExternalBounceInteraction } from \"tsparticles-interaction-external-bounce\";\nimport { loadExternalBubbleInteraction } from \"tsparticles-interaction-external-bubble\";\nimport { loadExternalConnectInteraction } from \"tsparticles-interaction-external-connect\";\nimport { loadExternalGrabInteraction } from \"tsparticles-interaction-external-grab\";\nimport { loadExternalPauseInteraction } from \"tsparticles-interaction-external-pause\";\nimport { loadExternalPushInteraction } from \"tsparticles-interaction-external-push\";\nimport { loadExternalRemoveInteraction } from \"tsparticles-interaction-external-remove\";\nimport { loadExternalRepulseInteraction } from \"tsparticles-interaction-external-repulse\";\nimport { loadExternalSlowInteraction } from \"tsparticles-interaction-external-slow\";\nimport { loadImageShape } from \"tsparticles-shape-image\";\nimport { loadLifeUpdater } from \"tsparticles-updater-life\";\nimport { loadLineShape } from \"tsparticles-shape-line\";\nimport { loadParallaxMover } from \"tsparticles-move-parallax\";\nimport { loadParticlesAttractInteraction } from \"tsparticles-interaction-particles-attract\";\nimport { loadParticlesCollisionsInteraction } from \"tsparticles-interaction-particles-collisions\";\nimport { loadParticlesLinksInteraction } from \"tsparticles-interaction-particles-links\";\nimport { loadPolygonShape } from \"tsparticles-shape-polygon\";\nimport { loadRotateUpdater } from \"tsparticles-updater-rotate\";\nimport { loadSquareShape } from \"tsparticles-shape-square\";\nimport { loadStarShape } from \"tsparticles-shape-star\";\nimport { loadStrokeColorUpdater } from \"tsparticles-updater-stroke-color\";\nimport { loadTextShape } from \"tsparticles-shape-text\";\nexport async function loadSlim(engine, refresh = true) {\n    initPjs(engine);\n    await loadParallaxMover(engine, false);\n    await loadExternalAttractInteraction(engine, false);\n    await loadExternalBounceInteraction(engine, false);\n    await loadExternalBubbleInteraction(engine, false);\n    await loadExternalConnectInteraction(engine, false);\n    await loadExternalGrabInteraction(engine, false);\n    await loadExternalPauseInteraction(engine, false);\n    await loadExternalPushInteraction(engine, false);\n    await loadExternalRemoveInteraction(engine, false);\n    await loadExternalRepulseInteraction(engine, false);\n    await loadExternalSlowInteraction(engine, false);\n    await loadParticlesAttractInteraction(engine, false);\n    await loadParticlesCollisionsInteraction(engine, false);\n    await loadParticlesLinksInteraction(engine, false);\n    await loadEasingQuadPlugin();\n    await loadImageShape(engine, false);\n    await loadLineShape(engine, false);\n    await loadPolygonShape(engine, false);\n    await loadSquareShape(engine, false);\n    await loadStarShape(engine, false);\n    await loadTextShape(engine, false);\n    await loadLifeUpdater(engine, false);\n    await loadRotateUpdater(engine, false);\n    await loadStrokeColorUpdater(engine, false);\n    await loadBasic(engine, refresh);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACO,IAAM,YAAN,MAAM,WAAU;AAAA,EACnB,OAAO,KAAK,SAAS;AAFzB;AAGQ,UAAM,YAAY,IAAI,WAAU,GAAG,WAAW,QAAQ;AACtD,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,MAAM,sBAAsB;AAAA,IAC1C;AACA,UAAM,KAAK,SAAS,cAAc,QAAQ;AAC1C,QAAI,CAAC,IAAI;AACL,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACnD;AACA,gBACK,IAAI,SAAS,QAAQ,KAAK,EAAE,EAAE,QAAQ,KAAK,EAAE,GAAG,IAAI;AAAA,MACrD,YAAY;AAAA,QACR,QAAQ;AAAA,MACZ;AAAA,MACA,WAAW;AAAA,QACP,OAAO;AAAA,UACH,OAAO,QAAQ,SAAS;AAAA,QAC5B;AAAA,QACA,OAAO;AAAA,UACH,OAAO;AAAA,UACP,UAAU,QAAQ,eAAe;AAAA,UACjC,QAAQ,QAAQ,oBAAoB;AAAA,QACxC;AAAA,QACA,MAAM;AAAA,UACF,QAAQ;AAAA,UACR,OAAO,QAAQ,SAAS;AAAA,QAC5B;AAAA,QACA,QAAQ;AAAA,UACJ,OAAO,QAAQ,gBAAgB;AAAA,QACnC;AAAA,QACA,MAAM;AAAA,UACF,OAAO,EAAE,KAAK,GAAG,KAAK,QAAQ,kBAAkB,EAAE;AAAA,QACtD;AAAA,MACJ;AAAA,MACA,aAAY,aAAQ,eAAR,mBAAoB,IAAI,CAAC,eAAY;AApC7D,YAAAA,KAAA;AAoCiE;AAAA,UACjD,UAAU,WAAW;AAAA,UACrB,SAAS;AAAA,YACL,WAAW;AAAA,cACP,OAAO;AAAA,gBACH,QAAOA,MAAA,WAAW,YAAX,gBAAAA,IAAoB;AAAA,cAC/B;AAAA,cACA,OAAO;AAAA,gBACH,WAAU,gBAAW,YAAX,mBAAoB;AAAA,gBAC9B,SAAQ,gBAAW,YAAX,mBAAoB;AAAA,cAChC;AAAA,cACA,QAAQ;AAAA,gBACJ,OAAO,QAAQ;AAAA,cACnB;AAAA,cACA,MAAM;AAAA,gBACF,QAAQ;AAAA,gBACR,QAAO,gBAAW,YAAX,mBAAoB;AAAA,cAC/B;AAAA,cACA,MAAM;AAAA,gBACF,QAAO,gBAAW,YAAX,mBAAoB;AAAA,cAC/B;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA;AAAA,IACJ,CAAC,EACI,KAAK,CAAC,cAAc;AACrB,gBAAU,aAAa;AAAA,IAC3B,CAAC;AACD,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,UAAM,YAAY,KAAK;AACvB,iBAAa,UAAU,QAAQ;AAAA,EACnC;AAAA,EACA,iBAAiB;AACb,UAAM,YAAY,KAAK;AACvB,iBAAa,UAAU,MAAM;AAAA,EACjC;AAAA,EACA,kBAAkB;AACd,UAAM,YAAY,KAAK;AACvB,iBAAa,UAAU,KAAK;AAAA,EAChC;AACJ;;;AC9EA,IAAM,kBAAkB,CAAC,WAAW;AAChC,QAAM,cAAc,CAAC,OAAO,YAAY;AACpC,WAAO,OAAO,KAAK,OAAO,OAAO;AAAA,EACrC;AACA,cAAY,OAAO,CAAC,OAAO,gBAAgB,aAAa;AACpD,WACK,SAAS,OAAO,cAAc,EAC9B,KAAK,CAAC,cAAc;AACrB,UAAI,WAAW;AACX,iBAAS,SAAS;AAAA,MACtB;AAAA,IACJ,CAAC,EACI,MAAM,MAAM;AACb,eAAS,MAAS;AAAA,IACtB,CAAC;AAAA,EACL;AACA,cAAY,oBAAoB,CAAC,aAAa;AAC1C,WAAO,kBAAkB,QAAQ;AAAA,EACrC;AACA,QAAM,SAAS,OAAO,IAAI;AAC1B,SAAO,EAAE,aAAa,OAAO;AACjC;;;ACnBA,IAAM,UAAU,CAAC,WAAW;AACxB,QAAM,EAAE,aAAa,OAAO,IAAI,gBAAgB,MAAM;AACtD,SAAO,cAAc;AACrB,SAAO,SAAS;AAChB,SAAO,YAAY;AACnB,SAAO,EAAE,aAAa,QAAQ,UAAU;AAC5C;;;ACPO,SAAS,cAAc,UAAU;AACpC,QAAM,kBAAkB,SAAS,iBAAiB,EAAE,IAAI,GAAG,IAAI,aAAa,iBAAiB,SAAS,QAAQ,GAAG,UAAU,KAAK,IAAI,EAAE,GAAG,UAAU,KAAK,IAAI,EAAE,GAAG,EAAE,YAAY,IAAI,SAAS,QAAQ,YAAY,YAAY,YAAY,YAAY,YAAY;AAChQ,MAAI,CAAC,aAAa,CAAC,WAAW;AAC1B;AAAA,EACJ;AACA,OAAM,aAAa,WAAW,aAAe,aAAa,WAAW,cAAe,CAAC,SAAS,WAAW;AACrG,aAAS,YAAa,CAAC,CAAC,aAAa,UAAU,aAAe,CAAC,CAAC,aAAa,UAAU;AACvF,QAAI,WAAW;AACX,eAAS,SAAS,IAAI,SAAS,SAAS,IAAI,IAAI,SAAS,SAAS;AAAA,IACtE;AACA,QAAI,WAAW;AACX,eAAS,SAAS,IAAI,SAAS,SAAS,IAAI,IAAI,SAAS,SAAS;AAAA,IACtE;AAAA,EACJ,YACU,CAAC,aAAa,UAAU,eAAe,CAAC,aAAa,UAAU,cAAc,SAAS,WAAW;AACvG,aAAS,YAAY;AAAA,EACzB,WACS,SAAS,WAAW;AACzB,UAAM,MAAM,SAAS,UAAU,MAAM,SAAS;AAC9C,QAAI,cAAe,IAAI,IAAI,gBAAgB,KAAK,IAAI,IAAI,KAAO,IAAI,IAAI,gBAAgB,KAAK,IAAI,IAAI,IAAK;AACrG,UAAI,KAAK,CAAC,UAAU;AAAA,IACxB;AACA,QAAI,cAAe,IAAI,IAAI,gBAAgB,KAAK,IAAI,IAAI,KAAO,IAAI,IAAI,gBAAgB,KAAK,IAAI,IAAI,IAAK;AACrG,UAAI,KAAK,CAAC,UAAU;AAAA,IACxB;AAAA,EACJ;AACJ;AACO,SAAS,KAAK,UAAU,aAAa,WAAW,UAAU,WAAW,OAAO;AAC/E,YAAU,UAAU,KAAK;AACzB,QAAM,iBAAiB,SAAS,SAAS,iBAAgB,iDAAgB,WAAU,eAAe,UAAU,KAAK;AACjH,MAAI,aAAa,WAAW;AACxB,aAAS,SAAS,KAAM,YAAY,MAAM,UAAW,KAAK;AAAA,EAC9D;AACA,OAAI,iDAAgB,WAAU,WAAW;AACrC,aAAS,SAAS,KAAM,iBAAiB,eAAe,eAAe,MAAM,WAAY,KAAK;AAAA,EAClG;AACA,QAAM,QAAQ,SAAS;AACvB,WAAS,SAAS,OAAO,KAAK;AAC9B,QAAM,WAAW,SAAS,SAAS,KAAK,SAAS;AACjD,OAAI,iDAAgB,WAChB,WAAW,MACT,CAAC,eAAe,WAAW,SAAS,KAAK,KAAK,SAAS,KAAK,YACzD,eAAe,WAAW,SAAS,KAAK,KAAK,SAAS,KAAK,CAAC,WAAY;AAC7E,aAAS,IAAI,gBAAgB;AAC7B,QAAI,WAAW;AACX,eAAS,SAAS,IAAI,SAAS,IAAI;AAAA,IACvC;AAAA,EACJ;AACA,QAAM,gBAAgB,SAAS,QAAQ,QAAQ,mBAAmB,IAAI,SAAS,iBAAiB,cAAc;AAC9G,WAAS,OAAO,eAAe;AAC/B,QAAM,EAAE,SAAS,IAAI;AACrB,WAAS,MAAM,QAAQ;AACvB,MAAI,YAAY,SAAS;AACrB,aAAS,KAAK,KAAK,IAAI,SAAS,IAAI,KAAK,IAAI,SAAS,CAAC,CAAC;AACxD,aAAS,KAAK,KAAK,IAAI,SAAS,IAAI,KAAK,IAAI,SAAS,CAAC,CAAC;AAAA,EAC5D;AACJ;AACO,SAAS,KAAK,UAAU,WAAW;AACtC,QAAM,YAAY,SAAS;AAC3B,MAAI,CAAC,SAAS,MAAM;AAChB;AAAA,EACJ;AACA,QAAM,aAAa;AAAA,IACf,GAAG,SAAS,KAAK,cAAc,cAAc,KAAK,MAAM,KAAK;AAAA,IAC7D,GAAG,SAAS,KAAK,cAAc,cAAc,KAAK,MAAM,KAAK;AAAA,EACjE;AACA,WAAS,SAAS,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,SAAS,WAAW,EAAE,SAAS,KAAK,KAAK;AACtG,WAAS,SAAS,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,SAAS,WAAW,EAAE,SAAS,KAAK,KAAK;AACtG,WAAS,KAAK,UAAU,SAAS,KAAK;AACtC,QAAM,gBAAgB,KAAK,IAAI,UAAU,OAAO,KAAK,OAAO,UAAU,OAAO,KAAK,MAAM;AACxF,MAAI,SAAS,KAAK,SAAS,gBAAgB,GAAG;AAC1C,aAAS,KAAK,SAAS,gBAAgB;AACvC,aAAS,KAAK,gBAAgB;AAAA,EAClC,WACS,SAAS,KAAK,SAAS,GAAG;AAC/B,aAAS,KAAK,SAAS;AACvB,aAAS,KAAK,gBAAgB;AAAA,EAClC;AACA,WAAS,KAAK,SAAU,YAAY,OAAQ,IAAI,SAAS,KAAK,SAAS;AAC3E;AACO,SAAS,UAAU,UAAU,OAAO;AAjF3C;AAkFI,QAAM,mBAAmB,SAAS,SAAS,cAAc,iBAAiB,KAAK,MAAM,cAAc,YAAY;AAC/G,MAAI,CAAC,aAAa;AACd;AAAA,EACJ;AACA,MAAI,SAAS,gBAAgB,SAAS,WAAW;AAC7C,aAAS,gBAAgB,MAAM;AAC/B;AAAA,EACJ;AACA,QAAM,QAAO,cAAS,kBAAT,mBAAwB,SAAS,UAAU;AACxD,MAAI,MAAM;AACN,aAAS,SAAS,MAAM,IAAI;AAAA,EAChC;AACA,MAAI,YAAY,OAAO;AACnB,aAAS,SAAS,IAAI,MAAM,SAAS,SAAS,GAAG,IAAI,CAAC;AACtD,aAAS,SAAS,IAAI,MAAM,SAAS,SAAS,GAAG,IAAI,CAAC;AAAA,EAC1D;AACA,WAAS,gBAAgB,SAAS;AACtC;AACO,SAAS,wBAAwB,UAAU;AAC9C,SAAO,SAAS,KAAK,UAAU,SAAS,KAAK,SAAS;AAC1D;;;ACpGA,IAAM,aAAa;AACZ,IAAM,YAAN,MAAgB;AAAA,EACnB,cAAc;AACV,SAAK,YAAY,CAAC,aAAa;AAC3B,YAAM,YAAY,SAAS,WAAW,UAAU,SAAS,SAAS,cAAc,QAAQ,KAAK;AAC7F,UAAI,CAAC,YAAY,QAAQ;AACrB;AAAA,MACJ;AACA,YAAM,UAAU,YAAY,YAAY,EAAE,GAAG,IAAI,GAAG,GAAG,GAAG,aAAa;AAAA,QACnE,GAAI,QAAQ,IAAI,MAAO,UAAU,OAAO,KAAK;AAAA,QAC7C,GAAI,QAAQ,IAAI,MAAO,UAAU,OAAO,KAAK;AAAA,MACjD,GAAG,MAAM,SAAS,YAAY,GAAG,WAAW,YAAY,KAAK,UAAU,GAAG,mBAAmB,cAAc,YAAY,YAAY;AACnI,eAAS,OAAO,mBAAmB,mBAAmB,UAAU,OAAO;AACvE,eAAS,OAAO;AAAA,QACZ,QAAQ;AAAA,QACR,WAAW,SAAS,SAAS,KAAK,IAAI,cAAc;AAAA,QACpD,OAAO,SAAS,SAAS;AAAA,QACzB,QAAQ;AAAA,QACR,cAAc,SAAS,OAAO;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,KAAK,UAAU;AACX,UAAM,UAAU,SAAS,SAAS,iBAAiB,QAAQ,KAAK;AAChE,aAAS,UAAU;AAAA,MACf,QAAQ,eAAe;AAAA,MACvB,cAAc,cAAc,eAAe,YAAY;AAAA,MACvD,SAAS,eAAe;AAAA,IAC5B;AACA,SAAK,UAAU,QAAQ;AAAA,EAC3B;AAAA,EACA,UAAU,UAAU;AAChB,WAAO,CAAC,SAAS,aAAa,SAAS,QAAQ,KAAK;AAAA,EACxD;AAAA,EACA,KAAK,UAAU,OAAO;AApC1B;AAqCQ,UAAM,kBAAkB,SAAS,SAAS,cAAc,gBAAgB;AACxE,QAAI,CAAC,YAAY,QAAQ;AACrB;AAAA,IACJ;AACA,UAAM,YAAY,SAAS,WAAW,UAAU,UAAU,OAAO,YAAY,aAAa,wBAAwB,QAAQ,GAAG,cAAa,cAAS,QAAO,cAAhB,GAAgB,YAAc,cAAc,YAAY,KAAK,IAAI,YACvM,UAAU,OAAO,cAAc,aAAa,cAAS,QAAO,cAAhB,GAAgB,YAAc,cAAc,SAAS,QAAQ,KAAK,KAAK,IAAI,UAAU,UAAU,YAAY,gBAAgB,KAAK,KAAK,IAAI,SAAS,aAAa,YAAY,OAAO,SAAS,UAAU,IAAI,UAAU,GAAG,YAAa,YAAY,aAAa,cAAc,MAAM,UAAU,KAAM,YAAY,WAAW,SAAS,OAAO,YAAY,UAAU,OAAO;AACpZ,QAAI,YAAY,KAAK,QAAQ;AACzB,WAAK,UAAU,SAAS;AAAA,IAC5B,OACK;AACD,WAAK,UAAU,aAAa,WAAW,UAAU,WAAW,KAAK;AAAA,IACrE;AACA,kBAAc,QAAQ;AAAA,EAC1B;AACJ;;;AClDA,eAAsB,cAAc,QAAQ,UAAU,MAAM;AACxD,QAAM,OAAO,SAAS,QAAQ,MAAM,IAAI,UAAU,GAAG,OAAO;AAChE;;;ACFO,IAAM,eAAN,MAAmB;AAAA,EACtB,KAAK,SAAS,UAAU,QAAQ;AAC5B,QAAI,CAAC,SAAS,aAAa;AACvB,eAAS,cAAc,EAAE,KAAK,GAAG,KAAK,KAAK,KAAK,EAAE;AAAA,IACtD;AACA,UAAM,cAAc,SAAS;AAC7B,YAAQ,IAAI,GAAG,GAAG,QAAQ,YAAY,KAAK,YAAY,KAAK,KAAK;AAAA,EACrE;AAAA,EACA,gBAAgB;AACZ,WAAO;AAAA,EACX;AAAA,EACA,aAAa,WAAW,UAAU;AAC9B,UAAM,YAAY,SAAS,WAAW,SAAQ,uCAAW,UAAS;AAAA,MAC9D,KAAK;AAAA,MACL,KAAK;AAAA,IACT;AACA,aAAS,cAAc,CAAC,SAAS,KAAK,IAChC;AAAA,MACE,KAAK;AAAA,MACL,KAAM,QAAQ,KAAK,KAAM;AAAA,IAC7B,IACE,EAAE,KAAM,MAAM,MAAM,KAAK,KAAM,KAAK,KAAM,MAAM,MAAM,KAAK,KAAM,IAAI;AAAA,EAC/E;AACJ;;;ACvBA,eAAsB,gBAAgB,QAAQ,UAAU,MAAM;AAC1D,QAAM,OAAO,SAAS,UAAU,IAAI,aAAa,GAAG,OAAO;AAC/D;;;ACFA,SAAS,iBAAiB,OAAO,YAAY,gBAAgB,KAAK,UAAU;AACxE,MAAI,CAAC,cACD,CAAC,eAAe,WACd,WAAW,YAAY,KAAK,MAAM,WAAW,SAAS,MAAM,WAAW,YAAY,IAAK;AAC1F;AAAA,EACJ;AACA,MAAI,CAAC,WAAW,MAAM;AAClB,eAAW,OAAO;AAAA,EACtB;AACA,OAAK,WAAW,aAAa,KAAK,KAAK,WAAW,QAAQ,WAAW,aAAa,IAAI;AAClF,eAAW,QAAQ,MAAM;AAAA,EAC7B;AACA,OAAK,WAAW,aAAa,KAAK,KAAK,WAAW,QAAQ,WAAW,aAAa,IAAI;AAClF;AAAA,EACJ;AACA,QAAM,SAAS,cAAc,eAAe,MAAM,GAAG,YAAY,WAAW,YAAY,KAAK,MAAM,SAAS,SAAS,KAAK,QAAQ,WAAW,SAAS;AACtJ,MAAI,CAAC,YAAY,WAAW,WAAW,cAAc;AACjD,eAAW,SAAS;AACpB,QAAI,WAAW,QAAQ,KAAK;AACxB,UAAI,CAAC,WAAW,OAAO;AACnB,mBAAW,QAAQ;AAAA,MACvB;AACA,iBAAW;AACX,UAAI,UAAU;AACV,mBAAW,SAAS;AACpB,mBAAW,SAAS,WAAW,QAAQ;AAAA,MAC3C;AAAA,IACJ;AAAA,EACJ,OACK;AACD,eAAW,SAAS;AACpB,QAAI,WAAW,QAAQ,GAAG;AACtB,UAAI,CAAC,WAAW,OAAO;AACnB,mBAAW,QAAQ;AAAA,MACvB;AACA,iBAAW;AACX,iBAAW,SAAS;AACpB,iBAAW,SAAS,WAAW;AAAA,IACnC;AAAA,EACJ;AACA,MAAI,WAAW,YAAY,UAAU,GAAG;AACpC,eAAW,YAAY;AAAA,EAC3B;AACA,MAAI,WAAW,QAAQ,KAAK;AACxB,eAAW,SAAS;AAAA,EACxB;AACJ;AACO,SAAS,YAAY,UAAU,OAAO;AACzC,QAAM,EAAE,GAAG,YAAY,GAAG,YAAY,GAAG,WAAW,IAAI,SAAS,QAAQ,MAAM,WAAW,EAAE,MAAM,IAAI;AACtG,MAAI,CAAC,OAAO;AACR;AAAA,EACJ;AACA,QAAM,EAAE,GAAG,GAAG,EAAE,IAAI;AACpB,MAAI,GAAG;AACH,qBAAiB,OAAO,GAAG,YAAY,KAAK,KAAK;AAAA,EACrD;AACA,MAAI,GAAG;AACH,qBAAiB,OAAO,GAAG,YAAY,KAAK,IAAI;AAAA,EACpD;AACA,MAAI,GAAG;AACH,qBAAiB,OAAO,GAAG,YAAY,KAAK,IAAI;AAAA,EACpD;AACJ;;;AC7DO,IAAM,eAAN,MAAmB;AAAA,EACtB,YAAY,WAAW;AACnB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,KAAK,UAAU;AACX,UAAM,WAAW,gBAAgB,SAAS,QAAQ,OAAO,SAAS,IAAI,SAAS,QAAQ,gBAAgB;AACvG,QAAI,UAAU;AACV,eAAS,QAAQ,uBAAuB,UAAU,SAAS,QAAQ,MAAM,WAAW,KAAK,UAAU,OAAO,YAAY;AAAA,IAC1H;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,EAAE,GAAG,YAAY,GAAG,YAAY,GAAG,WAAW,IAAI,SAAS,QAAQ,MAAM,WAAW,EAAE,MAAM,IAAI;AACtG,WAAQ,CAAC,SAAS,aACd,CAAC,SAAS,cACR,+BAAO,EAAE,WAAU,UAAa,WAAW,WACxC,+BAAO,EAAE,WAAU,UAAa,WAAW,WAC3C,+BAAO,EAAE,WAAU,UAAa,WAAW;AAAA,EACxD;AAAA,EACA,OAAO,UAAU,OAAO;AACpB,gBAAY,UAAU,KAAK;AAAA,EAC/B;AACJ;;;ACtBA,eAAsB,iBAAiB,QAAQ,UAAU,MAAM;AAC3D,QAAM,OAAO,mBAAmB,SAAS,CAAC,cAAc,IAAI,aAAa,SAAS,GAAG,OAAO;AAChG;;;ACFA,SAAS,aAAa,UAAU,OAAO,UAAU,UAAU;AACvD,UAAQ,SAAS,QAAQ,QAAQ,UAAU,SAAS;AAAA,IAChD,KAAK;AACD,UAAI,SAAS,UAAU;AACnB,iBAAS,QAAQ;AAAA,MACrB;AACA;AAAA,IACJ,KAAK;AACD,UAAI,SAAS,UAAU;AACnB,iBAAS,QAAQ;AAAA,MACrB;AACA;AAAA,EACR;AACJ;AACO,SAAS,cAAc,UAAU,OAAO;AAC3C,QAAM,OAAO,SAAS;AACtB,MAAI,SAAS,aAAa,EAAC,6BAAM,YAAY,KAAK,YAAY,KAAK,MAAM,KAAK,SAAS,MAAM,KAAK,YAAY,IAAK;AAC/G;AAAA,EACJ;AACA,QAAM,WAAW,KAAK,KAAK,WAAW,KAAK,KAAK,QAAQ,KAAK,SAAS;AACtE,MAAI,CAAC,KAAK,MAAM;AACZ,SAAK,OAAO;AAAA,EAChB;AACA,OAAK,KAAK,aAAa,KAAK,KAAK,KAAK,QAAQ,KAAK,aAAa,IAAI;AAChE,SAAK,QAAQ,MAAM;AAAA,EACvB;AACA,OAAK,KAAK,aAAa,KAAK,KAAK,KAAK,QAAQ,KAAK,aAAa,IAAI;AAChE;AAAA,EACJ;AACA,UAAQ,KAAK,QAAQ;AAAA,IACjB,KAAK;AACD,UAAI,KAAK,SAAS,UAAU;AACxB,aAAK,SAAS;AACd,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,QAAQ;AAAA,QACjB;AACA,aAAK;AAAA,MACT,OACK;AACD,aAAK,UAAU,KAAK,YAAY,KAAK,MAAM;AAAA,MAC/C;AACA;AAAA,IACJ,KAAK;AACD,UAAI,KAAK,SAAS,UAAU;AACxB,aAAK,SAAS;AACd,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,QAAQ;AAAA,QACjB;AACA,aAAK;AAAA,MACT,OACK;AACD,aAAK,UAAU,KAAK,YAAY,KAAK,MAAM;AAAA,MAC/C;AACA;AAAA,EACR;AACA,MAAI,KAAK,YAAY,KAAK,UAAU,GAAG;AACnC,SAAK,YAAY;AAAA,EACrB;AACA,eAAa,UAAU,KAAK,OAAO,UAAU,QAAQ;AACrD,MAAI,CAAC,SAAS,WAAW;AACrB,SAAK,QAAQ,MAAM,KAAK,OAAO,UAAU,QAAQ;AAAA,EACrD;AACJ;;;AC7DO,IAAM,iBAAN,MAAqB;AAAA,EACxB,YAAY,WAAW;AACnB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,KAAK,UAAU;AACX,UAAM,iBAAiB,SAAS,QAAQ;AACxC,aAAS,UAAU,kCAAkC,gBAAgB,CAAC;AACtE,UAAM,mBAAmB,eAAe;AACxC,QAAI,iBAAiB,QAAQ;AACzB,eAAS,QAAQ,WACZ,cAAc,iBAAiB,KAAK,IAAI,MAAO,KAAK,UAAU,OAAO;AAC1E,UAAI,CAAC,iBAAiB,MAAM;AACxB,iBAAS,QAAQ,YAAY,UAAU;AAAA,MAC3C;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,WAAQ,CAAC,SAAS,aACd,CAAC,SAAS,YACV,CAAC,CAAC,SAAS,WACX,SAAS,QAAQ,YACf,SAAS,QAAQ,YAAY,MAAM,MAC/B,SAAS,QAAQ,YAAY,KAAK,MAC/B,SAAS,QAAQ,SAAS,MAAM,SAAS,QAAQ,YAAY;AAAA,EAC9E;AAAA,EACA,MAAM,UAAU;AACZ,QAAI,SAAS,SAAS;AAClB,eAAS,QAAQ,OAAO;AACxB,eAAS,QAAQ,QAAQ;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,OAAO,UAAU,OAAO;AACpB,QAAI,CAAC,KAAK,UAAU,QAAQ,GAAG;AAC3B;AAAA,IACJ;AACA,kBAAc,UAAU,KAAK;AAAA,EACjC;AACJ;;;ACtCA,eAAsB,mBAAmB,QAAQ,UAAU,MAAM;AAC7D,QAAM,OAAO,mBAAmB,WAAW,CAAC,cAAc,IAAI,eAAe,SAAS,GAAG,OAAO;AACpG;;;ACFO,SAAS,iBAAiB,MAAM;AACnC,MAAK,KAAK,YAAY,YAClB,KAAK,YAAY,uBACjB,KAAK,YAAY,sBACjB,KAAK,YAAY,WAChB,KAAK,cAAc,UAAU,KAAK,cAAc,SAAU;AAC3D;AAAA,EACJ;AACA,MAAI,KAAK,OAAO,QAAQ,KAAK,KAAK,cAAc,QAAQ;AACpD,SAAK,SAAS,SAAS,IAAI,KAAK,OAAO,KAAK,OAAO;AAAA,EACvD,WACS,KAAK,OAAO,OAAO,KAAK,WAAW,SAAS,KAAK,cAAc,SAAS;AAC7E,SAAK,SAAS,SAAS,IAAI,KAAK,WAAW,QAAQ,KAAK,OAAO,KAAK,OAAO;AAAA,EAC/E;AACA,QAAM,WAAW,KAAK,SAAS,SAAS;AACxC,MAAI,UAAU;AACd,MAAK,KAAK,cAAc,WAAW,KAAK,OAAO,SAAS,KAAK,WAAW,SAAS,WAAW,KACvF,KAAK,cAAc,UAAU,KAAK,OAAO,QAAQ,KAAK,WAAW,GAAI;AACtE,UAAM,cAAc,SAAS,KAAK,SAAS,QAAQ,OAAO,UAAU;AACpE,SAAK,SAAS,SAAS,KAAK,CAAC;AAC7B,cAAU;AAAA,EACd;AACA,MAAI,CAAC,SAAS;AACV;AAAA,EACJ;AACA,QAAM,SAAS,KAAK,OAAO,IAAI,KAAK;AACpC,MAAI,KAAK,OAAO,SAAS,KAAK,WAAW,SAAS,KAAK,cAAc,SAAS;AAC1E,SAAK,SAAS,SAAS,IAAI,KAAK,WAAW,QAAQ;AAAA,EACvD,WACS,KAAK,OAAO,QAAQ,KAAK,KAAK,cAAc,QAAQ;AACzD,SAAK,SAAS,SAAS,IAAI;AAAA,EAC/B;AACA,MAAI,KAAK,YAAY,SAAS;AAC1B,SAAK,SAAS,QAAQ;AAAA,EAC1B;AACJ;AACO,SAAS,eAAe,MAAM;AACjC,MAAK,KAAK,YAAY,YAClB,KAAK,YAAY,qBACjB,KAAK,YAAY,oBACjB,KAAK,YAAY,WAChB,KAAK,cAAc,YAAY,KAAK,cAAc,OAAQ;AAC3D;AAAA,EACJ;AACA,MAAI,KAAK,OAAO,SAAS,KAAK,KAAK,cAAc,OAAO;AACpD,SAAK,SAAS,SAAS,IAAI,KAAK,OAAO,KAAK,OAAO;AAAA,EACvD,WACS,KAAK,OAAO,MAAM,KAAK,WAAW,UAAU,KAAK,cAAc,UAAU;AAC9E,SAAK,SAAS,SAAS,IAAI,KAAK,WAAW,SAAS,KAAK,OAAO,KAAK,OAAO;AAAA,EAChF;AACA,QAAM,WAAW,KAAK,SAAS,SAAS;AACxC,MAAI,UAAU;AACd,MAAK,KAAK,cAAc,YAAY,KAAK,OAAO,UAAU,KAAK,WAAW,UAAU,WAAW,KAC1F,KAAK,cAAc,SAAS,KAAK,OAAO,OAAO,KAAK,WAAW,GAAI;AACpE,UAAM,cAAc,SAAS,KAAK,SAAS,QAAQ,OAAO,QAAQ;AAClE,SAAK,SAAS,SAAS,KAAK,CAAC;AAC7B,cAAU;AAAA,EACd;AACA,MAAI,CAAC,SAAS;AACV;AAAA,EACJ;AACA,QAAM,SAAS,KAAK,OAAO,IAAI,KAAK;AACpC,MAAI,KAAK,OAAO,UAAU,KAAK,WAAW,UAAU,KAAK,cAAc,UAAU;AAC7E,SAAK,SAAS,SAAS,IAAI,KAAK,WAAW,SAAS;AAAA,EACxD,WACS,KAAK,OAAO,OAAO,KAAK,KAAK,cAAc,OAAO;AACvD,SAAK,SAAS,SAAS,IAAI;AAAA,EAC/B;AACA,MAAI,KAAK,YAAY,SAAS;AAC1B,SAAK,SAAS,QAAQ;AAAA,EAC1B;AACJ;;;ACtEO,IAAM,gBAAN,MAAoB;AAAA,EACvB,YAAY,WAAW;AACnB,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,OAAO,UAAU,WAAW,OAAO,SAAS;AACxC,QAAI,CAAC,KAAK,MAAM,SAAS,OAAO,GAAG;AAC/B;AAAA,IACJ;AACA,UAAM,YAAY,KAAK;AACvB,QAAI,UAAU;AACd,eAAW,CAAC,EAAE,MAAM,KAAK,UAAU,SAAS;AACxC,UAAI,OAAO,mBAAmB,QAAW;AACrC,kBAAU,OAAO,eAAe,UAAU,OAAO,SAAS;AAAA,MAC9D;AACA,UAAI,SAAS;AACT;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,SAAS;AACT;AAAA,IACJ;AACA,UAAM,MAAM,SAAS,YAAY,GAAG,SAAS,SAAS,QAAQ,OAAO,SAAS,UAAU,GAAG,SAAS,gBAAgB,KAAK,IAAI,GAAG,aAAa,UAAU,OAAO;AAC9J,qBAAiB,EAAE,UAAU,SAAS,WAAW,QAAQ,YAAY,QAAQ,KAAK,CAAC;AACnF,mBAAe,EAAE,UAAU,SAAS,WAAW,QAAQ,YAAY,QAAQ,KAAK,CAAC;AAAA,EACrF;AACJ;;;AClCO,IAAM,iBAAN,MAAqB;AAAA,EACxB,YAAY,WAAW;AACnB,SAAK,YAAY;AACjB,SAAK,QAAQ,CAAC,SAAS;AAAA,EAC3B;AAAA,EACA,OAAO,UAAU,WAAW,QAAQ,SAAS;AACzC,QAAI,CAAC,KAAK,MAAM,SAAS,OAAO,GAAG;AAC/B;AAAA,IACJ;AACA,UAAM,YAAY,KAAK;AACvB,YAAQ,SAAS,SAAS;AAAA,MACtB,KAAK;AAAA,MACL,KAAK;AACD,YAAI,cAAc,SAAS,UAAU,UAAU,OAAO,MAAM,OAAO,QAAQ,SAAS,UAAU,GAAG,SAAS,GAAG;AACzG;AAAA,QACJ;AACA;AAAA,MACJ,KAAK,UAAU;AACX,cAAM,EAAE,IAAI,GAAG,IAAI,aAAa,SAAS,UAAU,SAAS,UAAU;AACtE,cAAM,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,SAAS;AAClC,YAAK,KAAK,KAAK,KAAK,SAAS,WAAW,UACnC,KAAK,KAAK,KAAK,SAAS,WAAW,UACnC,MAAM,KAAK,KAAK,CAAC,SAAS,WAAW,UACrC,MAAM,KAAK,KAAK,CAAC,SAAS,WAAW,QAAS;AAC/C;AAAA,QACJ;AACA;AAAA,MACJ;AAAA,IACJ;AACA,cAAU,UAAU,OAAO,UAAU,QAAW,IAAI;AAAA,EACxD;AACJ;;;AC/BO,IAAM,cAAN,MAAkB;AAAA,EACrB,YAAY,WAAW;AACnB,SAAK,YAAY;AACjB,SAAK,QAAQ,CAAC,MAAM;AAAA,EACxB;AAAA,EACA,OAAO,UAAU,WAAW,OAAO,SAAS;AACxC,QAAI,CAAC,KAAK,MAAM,SAAS,OAAO,GAAG;AAC/B;AAAA,IACJ;AACA,QAAK,SAAS,QAAQ,KAAK,SAAS,eAC/B,cAAc,UAAU,cAAc,YACtC,SAAS,QAAQ,KAAK,SAAS,aAC3B,cAAc,SAAS,cAAc,WAAY;AACtD;AAAA,IACJ;AACA,UAAM,iBAAiB,SAAS,QAAQ,KAAK,SAAS,YAAY,KAAK;AACvE,UAAM,aAAa,UAAU,OAAO;AACpC,UAAM,UAAU,SAAS,UAAU;AACnC,QAAI,CAAC,eAAe,QAAQ;AACxB,UAAK,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,KAAK,WAAW,SAAS,WACtE,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,KAAK,CAAC,WACnD,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,KAAK,WAAW,QAAQ,WACrE,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,KAAK,CAAC,SAAU;AAC9D;AAAA,MACJ;AACA,UAAI,CAAC,cAAc,SAAS,UAAU,UAAU,OAAO,MAAM,OAAO,QAAQ,SAAS,SAAS,GAAG;AAC7F,kBAAU,UAAU,OAAO,QAAQ;AAAA,MACvC;AAAA,IACJ,OACK;AACD,YAAM,WAAW,SAAS;AAC1B,UAAK,CAAC,eAAe,WACjB,SAAS,IAAI,WAAW,SAAS,WACjC,cAAc,YACb,eAAe,WAAW,SAAS,IAAI,CAAC,WAAW,cAAc,OAAQ;AAC1E,kBAAU,UAAU,OAAO,QAAQ;AAAA,MACvC;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACvCO,IAAM,aAAN,MAAiB;AAAA,EACpB,YAAY,WAAW;AACnB,SAAK,YAAY;AACjB,SAAK,QAAQ,CAAC,KAAK;AAAA,EACvB;AAAA,EACA,OAAO,UAAU,WAAW,OAAO,SAAS;AACxC,QAAI,CAAC,KAAK,MAAM,SAAS,OAAO,GAAG;AAC/B;AAAA,IACJ;AACA,UAAM,YAAY,KAAK;AACvB,YAAQ,SAAS,SAAS;AAAA,MACtB,KAAK,UAAU;AACX,cAAM,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,SAAS;AAClC,cAAM,UAAU,OAAO;AACvB,gBAAQ,SAAS,SAAS,WAAW;AACrC,gBAAQ,QAAQ,SAAS,SAAS,QAAQ,KAAK;AAC/C,gBAAQ,MAAM,OAAO,OAAO,SAAS,UAAU,CAAC;AAChD,cAAM,EAAE,IAAI,GAAG,IAAI,aAAa,SAAS,UAAU,OAAO;AAC1D,YAAK,MAAM,KAAK,MAAM,KAAO,MAAM,KAAK,MAAM,KAAO,MAAM,KAAK,MAAM,KAAO,MAAM,KAAK,MAAM,GAAI;AAC9F;AAAA,QACJ;AACA,iBAAS,SAAS,IAAI,KAAK,MAAM,cAAc;AAAA,UAC3C,KAAK;AAAA,UACL,KAAK,UAAU,OAAO,KAAK;AAAA,QAC/B,CAAC,CAAC;AACF,iBAAS,SAAS,IAAI,KAAK,MAAM,cAAc;AAAA,UAC3C,KAAK;AAAA,UACL,KAAK,UAAU,OAAO,KAAK;AAAA,QAC/B,CAAC,CAAC;AACF,cAAM,EAAE,IAAI,OAAO,IAAI,MAAM,IAAI,aAAa,SAAS,UAAU,SAAS,UAAU;AACpF,iBAAS,YAAY,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK;AAC9C,iBAAS,SAAS,QAAQ,SAAS;AACnC;AAAA,MACJ;AAAA,MACA,SAAS;AACL,YAAI,cAAc,SAAS,UAAU,UAAU,OAAO,MAAM,OAAO,QAAQ,SAAS,UAAU,GAAG,SAAS,GAAG;AACzG;AAAA,QACJ;AACA,gBAAQ,SAAS,SAAS;AAAA,UACtB,KAAK,WAAW;AACZ,qBAAS,SAAS,IACd,KAAK,MAAM,cAAc;AAAA,cACrB,KAAK,CAAC,SAAS,WAAW;AAAA,cAC1B,KAAK,SAAS,WAAW;AAAA,YAC7B,CAAC,CAAC,IAAI,SAAS,WAAW;AAC9B,qBAAS,SAAS,IACd,KAAK,MAAM,cAAc;AAAA,cACrB,KAAK,CAAC,SAAS,WAAW;AAAA,cAC1B,KAAK,SAAS,WAAW;AAAA,YAC7B,CAAC,CAAC,IAAI,SAAS,WAAW;AAC9B,kBAAM,EAAE,IAAI,GAAG,IAAI,aAAa,SAAS,UAAU,SAAS,UAAU;AACtE,gBAAI,SAAS,WAAW,QAAQ;AAC5B,uBAAS,YAAY,KAAK,MAAM,IAAI,EAAE;AACtC,uBAAS,SAAS,QAAQ,SAAS;AAAA,YACvC;AACA;AAAA,UACJ;AAAA,UACA,KAAK,UAAU;AACX,kBAAM,OAAO,SAAS,QAAQ,KAAK,MAAM,aAAa,UAAU,OAAO,MAAM,SAAS;AAAA,cAClF,QAAQ,WAAW,SAAS,SAAS,UAAU,IAAI,SAAS,OAAO;AAAA,cACnE,MAAM,CAAC,SAAS,UAAU,IAAI,SAAS,OAAO;AAAA,cAC9C,OAAO,WAAW,QAAQ,SAAS,UAAU,IAAI,SAAS,OAAO;AAAA,cACjE,KAAK,CAAC,SAAS,UAAU,IAAI,SAAS,OAAO;AAAA,YACjD,GAAG,YAAY,SAAS,UAAU,GAAG,aAAa,gBAAgB,SAAS,UAAU,SAAS;AAC9F,gBAAI,cAAc,WACd,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,GAAG;AACxD,uBAAS,SAAS,IAAI,OAAO;AAC7B,uBAAS,gBAAgB,IAAI,SAAS,SAAS;AAC/C,kBAAI,CAAC,MAAM;AACP,yBAAS,SAAS,IAAI,UAAU,IAAI,WAAW;AAC/C,yBAAS,gBAAgB,IAAI,SAAS,SAAS;AAAA,cACnD;AAAA,YACJ,WACS,cAAc,UAAU,WAAW,QAAQ,CAAC,SAAS,OAAO,GAAG;AACpE,uBAAS,SAAS,IAAI,OAAO;AAC7B,uBAAS,gBAAgB,IAAI,SAAS,SAAS;AAC/C,kBAAI,CAAC,MAAM;AACP,yBAAS,SAAS,IAAI,UAAU,IAAI,WAAW;AAC/C,yBAAS,gBAAgB,IAAI,SAAS,SAAS;AAAA,cACnD;AAAA,YACJ;AACA,gBAAI,cAAc,YACd,WAAW,MAAM,WAAW,SAAS,SAAS,OAAO,GAAG;AACxD,kBAAI,CAAC,MAAM;AACP,yBAAS,SAAS,IAAI,UAAU,IAAI,WAAW;AAC/C,yBAAS,gBAAgB,IAAI,SAAS,SAAS;AAAA,cACnD;AACA,uBAAS,SAAS,IAAI,OAAO;AAC7B,uBAAS,gBAAgB,IAAI,SAAS,SAAS;AAAA,YACnD,WACS,cAAc,SAAS,WAAW,SAAS,CAAC,SAAS,OAAO,GAAG;AACpE,kBAAI,CAAC,MAAM;AACP,yBAAS,SAAS,IAAI,UAAU,IAAI,WAAW;AAC/C,yBAAS,gBAAgB,IAAI,SAAS,SAAS;AAAA,cACnD;AACA,uBAAS,SAAS,IAAI,OAAO;AAC7B,uBAAS,gBAAgB,IAAI,SAAS,SAAS;AAAA,YACnD;AACA;AAAA,UACJ;AAAA,QACJ;AACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACtGO,IAAM,qBAAN,MAAyB;AAAA,EAC5B,YAAY,WAAW;AACnB,SAAK,YAAY;AACjB,SAAK,iBAAiB,CAAC,UAAU,OAAO,SAAS,cAAc;AAC3D,iBAAW,WAAW,KAAK,UAAU;AACjC,gBAAQ,OAAO,UAAU,WAAW,OAAO,OAAO;AAAA,MACtD;AAAA,IACJ;AACA,SAAK,WAAW;AAAA,MACZ,IAAI,cAAc,SAAS;AAAA,MAC3B,IAAI,eAAe,SAAS;AAAA,MAC5B,IAAI,WAAW,SAAS;AAAA,MACxB,IAAI,YAAY,SAAS;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,UAAU,UAAU;AAChB,WAAO,CAAC,SAAS,aAAa,CAAC,SAAS;AAAA,EAC5C;AAAA,EACA,OAAO,UAAU,OAAO;AACpB,UAAM,WAAW,SAAS,QAAQ,KAAK;AACvC,SAAK,eAAe,UAAU,OAAO,SAAS,UAAU,SAAS,SAAS,QAAQ;AAClF,SAAK,eAAe,UAAU,OAAO,SAAS,QAAQ,SAAS,SAAS,MAAM;AAC9E,SAAK,eAAe,UAAU,OAAO,SAAS,SAAS,SAAS,SAAS,OAAO;AAChF,SAAK,eAAe,UAAU,OAAO,SAAS,OAAO,SAAS,SAAS,KAAK;AAAA,EAChF;AACJ;;;AC9BA,eAAsB,oBAAoB,QAAQ,UAAU,MAAM;AAC9D,QAAM,OAAO,mBAAmB,YAAY,CAAC,cAAc,IAAI,mBAAmB,SAAS,GAAG,OAAO;AACzG;;;ACFA,SAASC,cAAa,UAAU,OAAO,UAAU,UAAU;AACvD,UAAQ,SAAS,QAAQ,KAAK,UAAU,SAAS;AAAA,IAC7C,KAAK;AACD,UAAI,SAAS,UAAU;AACnB,iBAAS,QAAQ;AAAA,MACrB;AACA;AAAA,IACJ,KAAK;AACD,UAAI,SAAS,UAAU;AACnB,iBAAS,QAAQ;AAAA,MACrB;AACA;AAAA,EACR;AACJ;AACO,SAAS,WAAW,UAAU,OAAO;AACxC,QAAM,OAAO,SAAS;AACtB,MAAI,SAAS,aACT,CAAC,QACD,CAAC,KAAK,WACJ,KAAK,YAAY,KAAK,MAAM,KAAK,SAAS,MAAM,KAAK,YAAY,IAAK;AACxE;AAAA,EACJ;AACA,QAAM,gBAAgB,KAAK,YAAY,KAAK,MAAM,QAAQ,WAAW,KAAK,KAAK,WAAW,KAAK,KAAK,QAAQ,KAAK,SAAS;AAC1H,MAAI,CAAC,KAAK,MAAM;AACZ,SAAK,OAAO;AAAA,EAChB;AACA,OAAK,KAAK,aAAa,KAAK,KAAK,KAAK,QAAQ,KAAK,aAAa,IAAI;AAChE,SAAK,QAAQ,MAAM;AAAA,EACvB;AACA,OAAK,KAAK,aAAa,KAAK,KAAK,KAAK,QAAQ,KAAK,aAAa,IAAI;AAChE;AAAA,EACJ;AACA,UAAQ,KAAK,QAAQ;AAAA,IACjB,KAAK;AACD,UAAI,KAAK,SAAS,UAAU;AACxB,aAAK,SAAS;AACd,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,QAAQ;AAAA,QACjB;AACA,aAAK;AAAA,MACT,OACK;AACD,aAAK,SAAS;AAAA,MAClB;AACA;AAAA,IACJ,KAAK;AACD,UAAI,KAAK,SAAS,UAAU;AACxB,aAAK,SAAS;AACd,YAAI,CAAC,KAAK,OAAO;AACb,eAAK,QAAQ;AAAA,QACjB;AACA,aAAK;AAAA,MACT,OACK;AACD,aAAK,SAAS;AAAA,MAClB;AAAA,EACR;AACA,MAAI,KAAK,YAAY,UAAU,GAAG;AAC9B,SAAK,YAAY;AAAA,EACrB;AACA,EAAAA,cAAa,UAAU,KAAK,OAAO,UAAU,QAAQ;AACrD,MAAI,CAAC,SAAS,WAAW;AACrB,SAAK,QAAQ,MAAM,KAAK,OAAO,UAAU,QAAQ;AAAA,EACrD;AACJ;;;AC/DO,IAAM,cAAN,MAAkB;AAAA,EACrB,KAAK,UAAU;AACX,UAAM,YAAY,SAAS,WAAW,cAAc,SAAS,QAAQ,MAAM,gBAAgB,YAAY;AACvG,QAAI,cAAc,QAAQ;AACtB,eAAS,KAAK,YACR,SAAS,OAAO,sBAAsB,UAAU,OAAO,sBAAsB,MAC3E,UAAU,OAAO;AACzB,UAAI,CAAC,cAAc,MAAM;AACrB,iBAAS,KAAK,YAAY,UAAU;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,WAAQ,CAAC,SAAS,aACd,CAAC,SAAS,YACV,SAAS,KAAK,YACZ,SAAS,KAAK,YAAY,MAAM,MAC5B,SAAS,KAAK,YAAY,KAAK,MAAM,SAAS,KAAK,SAAS,MAAM,SAAS,KAAK,YAAY;AAAA,EAC1G;AAAA,EACA,MAAM,UAAU;AACZ,aAAS,KAAK,QAAQ;AAAA,EAC1B;AAAA,EACA,OAAO,UAAU,OAAO;AACpB,QAAI,CAAC,KAAK,UAAU,QAAQ,GAAG;AAC3B;AAAA,IACJ;AACA,eAAW,UAAU,KAAK;AAAA,EAC9B;AACJ;;;AC7BA,eAAsB,gBAAgB,QAAQ,UAAU,MAAM;AAC1D,QAAM,OAAO,mBAAmB,QAAQ,MAAM,IAAI,YAAY,GAAG,OAAO;AAC5E;;;ACGA,eAAsB,UAAU,QAAQ,UAAU,MAAM;AACpD,QAAM,cAAc,QAAQ,KAAK;AACjC,QAAM,gBAAgB,QAAQ,KAAK;AACnC,QAAM,iBAAiB,QAAQ,KAAK;AACpC,QAAM,mBAAmB,QAAQ,KAAK;AACtC,QAAM,oBAAoB,QAAQ,KAAK;AACvC,QAAM,gBAAgB,QAAQ,KAAK;AACnC,QAAM,OAAO,QAAQ,OAAO;AAChC;;;ACbA,eAAsB,uBAAuB;AACzC,YAAU,gBAAgB,CAAC,UAAU,SAAS,CAAC;AAC/C,YAAU,iBAAiB,CAAC,UAAU,KAAK,IAAI,UAAU,CAAC;AAC1D,YAAU,oBAAoB,CAAC,UAAW,QAAQ,MAAM,IAAI,SAAS,IAAI,KAAK,KAAK,QAAQ,MAAM,IAAI,CAAE;AAC3G;;;ACLO,IAAM,UAAN,MAAc;AAAA,EACjB,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AAAA,EACJ;AACJ;;;AC9BO,IAAM,YAAN,cAAwB,uBAAuB;AAAA,EAClD,YAAY,QAAQ,WAAW;AAC3B,UAAM,SAAS;AACf,SAAK,gBAAgB,MAAM;AACvB,YAAMC,aAAY,KAAK;AACvB,UAAI,CAACA,WAAU,SAAS;AACpB,QAAAA,WAAU,UAAU,EAAE,WAAW,CAAC,EAAE;AAAA,MACxC;AACA,YAAM,EAAE,QAAQ,IAAIA;AACpB,UAAI,CAAC,QAAQ,QAAQ;AACjB,YAAI,CAAC,QAAQ,OAAO;AAChB,kBAAQ,QAAQ;AAAA,QACpB;AACA,gBAAQ;AACR,YAAI,QAAQ,UAAUA,WAAU,UAAU,OAAO;AAC7C,kBAAQ,SAAS;AAAA,QACrB;AAAA,MACJ;AACA,UAAI,QAAQ,UAAU;AAClB,cAAM,WAAWA,WAAU,cAAc,MAAM,eAAe,gBAAgBA,WAAU,OAAO;AAC/F,YAAI,CAAC,iBAAiB,gBAAgB,KAAK,CAAC,UAAU;AAClD;AAAA,QACJ;AACA,aAAK,gBAAgB,UAAU,eAAe,IAAI,OAAO,SAAS,GAAG,SAAS,GAAG,aAAa,CAAC;AAAA,MACnG,WACS,QAAQ,aAAa,OAAO;AACjC,gBAAQ,YAAY,CAAC;AAAA,MACzB;AACA;AAAA,IACJ;AACA,SAAK,gBAAgB,MAAM;AACvB,YAAMA,aAAY,KAAK,WAAW,WAAWA,WAAU,cAAc,MAAM,UAAU,gBAAgBA,WAAU,OAAO;AACtH,UAAI,CAAC,iBAAiB,gBAAgB,KAAK,CAAC,UAAU;AAClD;AAAA,MACJ;AACA,WAAK,gBAAgB,UAAU,eAAe,IAAI,OAAO,SAAS,GAAG,SAAS,GAAG,aAAa,CAAC;AAAA,IACnG;AACA,SAAK,kBAAkB,CAAC,UAAU,eAAe,SAAS;AACtD,YAAMA,aAAY,KAAK,WAAW,iBAAiBA,WAAU,cAAc,cAAc,MAAM;AAC/F,UAAI,CAAC,gBAAgB;AACjB;AAAA,MACJ;AACA,YAAM,QAAQA,WAAU,UAAU,SAAS,MAAM,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;AAC/E,iBAAW,YAAY,OAAO;AAC1B,cAAM,EAAE,IAAI,IAAI,SAAS,IAAI,aAAa,SAAS,UAAU,QAAQ;AACrE,cAAM,WAAW,eAAe,QAAQ,eAAe;AACvD,cAAM,gBAAgB,MAAM,UAAU,eAAe,MAAM,EAAE,IAAI,WAAW,aAAa,IAAI,UAAU,GAAG,eAAe,QAAQ;AACjI,cAAM,UAAU,OAAO,OAAO,aAAa,IAAI,WAAY,KAAK,WAAY,eAAe,aAAa,IAAI,WAAY,KAAK,WAAY,aAAa;AACtJ,iBAAS,SAAS,QAAQ,OAAO;AAAA,MACrC;AAAA,IACJ;AACA,SAAK,UAAU;AACf,QAAI,CAAC,UAAU,SAAS;AACpB,gBAAU,UAAU,EAAE,WAAW,CAAC,EAAE;AAAA,IACxC;AACA,SAAK,kBAAkB,CAAC,SAAS;AAC7B,YAAM,UAAU,KAAK,UAAU,eAAe,UAAU,QAAQ,cAAc,MAAM;AACpF,UAAI,CAAC,WAAW,SAAS,WAAW;AAChC;AAAA,MACJ;AACA,UAAI,CAAC,UAAU,SAAS;AACpB,kBAAU,UAAU,EAAE,WAAW,CAAC,EAAE;AAAA,MACxC;AACA,gBAAU,QAAQ,WAAW;AAC7B,gBAAU,QAAQ,QAAQ;AAC1B,iBAAW,YAAY,UAAU,QAAQ,WAAW;AAChD,YAAI,CAAC,KAAK,UAAU,QAAQ,GAAG;AAC3B;AAAA,QACJ;AACA,iBAAS,SAAS,MAAM,SAAS,eAAe;AAAA,MACpD;AACA,gBAAU,QAAQ,YAAY,CAAC;AAC/B,gBAAU,QAAQ,SAAS;AAC3B,iBAAW,MAAM;AACb,YAAI,UAAU,WAAW;AACrB;AAAA,QACJ;AACA,YAAI,CAAC,UAAU,SAAS;AACpB,oBAAU,UAAU,EAAE,WAAW,CAAC,EAAE;AAAA,QACxC;AACA,kBAAU,QAAQ,WAAW;AAAA,MACjC,GAAG,QAAQ,WAAW,GAAI;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AACH,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,cAAc,cAAc,MAAM;AACxF,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,cAAU,OAAO,sBAAsB,QAAQ,WAAW,UAAU,OAAO;AAAA,EAC/E;AAAA,EACA,MAAM,WAAW;AACb,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,kBAAkB,UAAU,cAAc,WAAW,gBAAgB,SAAS,QAAQ,cAAc,QAAQ,eAAe,OAAO,QAAQ,QAAQ,YAAY,OAAO,QAAQ,MAAM,eAAe,OAAO,QAAQ,QAAQ,YAAY,OAAO,QAAQ;AACzT,QAAI,mBAAmB,gBAAgB,UAAU,WAAW,SAAS,GAAG;AACpE,WAAK,cAAc;AAAA,IACvB,WACS,gBAAgB,UAAU,WAAW,SAAS,GAAG;AACtD,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,QAAQ,UAAU,cAAc,OAAO,WAAU,qCAAU,kBAAiB,QAAQ,eAAe;AACxK,SAAK,CAAC,MAAM,YAAY,CAAC,OAAO,QAAQ,YAAY,CAAC,MAAM,iBAAiB,CAAC,OAAO,QAAQ,SAAS;AACjG,aAAO;AAAA,IACX;AACA,UAAM,YAAY,OAAO,QAAQ,MAAM,YAAY,OAAO,QAAQ;AAClE,WAAO,UAAU,WAAW,SAAS,KAAK,UAAU,WAAW,SAAS;AAAA,EAC5E;AAAA,EACA,gBAAgB,YAAY,SAAS;AACjC,QAAI,CAAC,QAAQ,SAAS;AAClB,cAAQ,UAAU,IAAI,QAAQ;AAAA,IAClC;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,QAAQ,KAAK,iCAAQ,OAAO;AAAA,IACxC;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;ACzHA,eAAsB,+BAA+B,QAAQ,UAAU,MAAM;AACzE,QAAM,OAAO,cAAc,mBAAmB,CAAC,cAAc,IAAI,UAAU,QAAQ,SAAS,GAAG,OAAO;AAC1G;;;ACHO,IAAM,SAAN,MAAa;AAAA,EAChB,cAAc;AACV,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AAAA,EACJ;AACJ;;;ACVO,IAAM,UAAN,cAAsB,uBAAuB;AAAA,EAChD,YAAY,WAAW;AACnB,UAAM,SAAS;AACf,SAAK,iBAAiB,CAAC,UAAU,QAAQ,SAAS;AAC9C,YAAM,QAAQ,KAAK,UAAU,UAAU,SAAS,MAAM,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;AACpF,iBAAW,YAAY,OAAO;AAC1B,YAAI,gBAAgB,QAAQ;AACxB,uBAAa,6BAA6B,QAAQ,GAAG;AAAA,YACjD;AAAA,YACA;AAAA,YACA,MAAO,UAAU,IAAI,KAAK,KAAM;AAAA,YAChC,UAAU,OAAO;AAAA,YACjB,QAAQ,OAAO;AAAA,UACnB,CAAC;AAAA,QACL,WACS,gBAAgB,WAAW;AAChC,qBAAW,UAAU,gBAAgB,UAAU,MAAM,CAAC;AAAA,QAC1D;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,sBAAsB,MAAM;AAC7B,YAAMC,aAAY,KAAK,WAAW,UAAUA,WAAU,OAAO,YAAY,YAAY,KAAK,SAAS,WAAWA,WAAU,cAAc,MAAM,UAAU,SAASA,WAAU,OAAO;AAChL,UAAI,CAAC,UAAU,SAAS,KAAK,CAAC,UAAU;AACpC;AAAA,MACJ;AACA,WAAK,eAAe,UAAU,QAAQ,IAAI,OAAO,SAAS,GAAG,SAAS,GAAG,SAAS,SAAS,CAAC;AAAA,IAChG;AACA,SAAK,wBAAwB,CAAC,UAAU,QAAQ;AAC5C,YAAMA,aAAY,KAAK,WAAW,QAAQ,SAAS,iBAAiB,QAAQ;AAC5E,UAAI,CAAC,MAAM,QAAQ;AACf;AAAA,MACJ;AACA,YAAM,QAAQ,CAAC,SAAS;AACpB,cAAM,OAAO,MAAM,UAAUA,WAAU,OAAO,YAAY,MAAM;AAAA,UAC5D,IAAI,KAAK,aAAa,KAAK,cAAc,KAAK;AAAA,UAC9C,IAAI,KAAK,YAAY,KAAK,eAAe,KAAK;AAAA,QAClD,GAAG,SAAU,KAAK,cAAc,IAAK,SAAS,YAAY,KAAK,SAAS,OAAO,IAAI,SAAS,WACtF,IAAI,OAAO,IAAI,GAAG,IAAI,GAAG,SAAS,SAAS,IAC3C,IAAI,UAAU,KAAK,aAAa,UAAU,WAAW,KAAK,YAAY,UAAU,WAAW,KAAK,cAAc,UAAU,YAAY,GAAG,KAAK,eAAe,UAAU,YAAY,CAAC;AACxL,aAAK,eAAe,KAAK,QAAQ,IAAI;AAAA,MACzC,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AACH,UAAM,YAAY,KAAK,WAAWC,UAAS,UAAU,cAAc,cAAc,MAAM;AACvF,QAAI,CAACA,SAAQ;AACT;AAAA,IACJ;AACA,cAAU,OAAO,qBAAqBA,QAAO,WAAW,UAAU,OAAO;AAAA,EAC7E;AAAA,EACA,MAAM,WAAW;AACb,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,SAAS,QAAQ,cAAc,QAAQ,kBAAkB,UAAU,cAAc,WAAW,gBAAgB,eAAe,OAAO,QAAQ,QAAQ,YAAY,OAAO,QAAQ,MAAM,OAAO,OAAO;AACtQ,QAAI,mBAAmB,gBAAgB,UAAU,UAAU,SAAS,GAAG;AACnE,WAAK,oBAAoB;AAAA,IAC7B,OACK;AACD,qBAAe,UAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,sBAAsB,UAAU,GAAG,CAAC;AAAA,IAC/F;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,QAAQ,UAAU,cAAc,OAAO,WAAU,qCAAU,kBAAiB,QAAQ,eAAe,QAAQ,OAAO,OAAO;AAC9L,WAAS,MAAM,YAAY,OAAO,QAAQ,UAAU,UAAU,UAAU,OAAO,QAAQ,IAAI,KACvF,iBAAiB,UAAU,IAAI;AAAA,EACvC;AAAA,EACA,gBAAgB,YAAY,SAAS;AACjC,QAAI,CAAC,QAAQ,QAAQ;AACjB,cAAQ,SAAS,IAAI,OAAO;AAAA,IAChC;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,OAAO,KAAK,iCAAQ,MAAM;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;AC7EA,eAAsB,8BAA8B,QAAQ,UAAU,MAAM;AACxE,QAAM,OAAO,cAAc,kBAAkB,CAAC,cAAc,IAAI,QAAQ,SAAS,GAAG,OAAO;AAC/F;;;ACFO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,MAAM;AAAA,EACf;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,QAAQ,QAAW;AACxB,WAAK,MAAM,KAAK;AAAA,IACpB;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,YAAM,cAAc,QAAQ,KAAK,KAAK,IAAI,SAAY,KAAK;AAC3D,WAAK,QAAQ,0BAA0B,KAAK,OAAO,CAAC,UAAU;AAC1D,eAAO,aAAa,OAAO,aAAa,KAAK;AAAA,MACjD,CAAC;AAAA,IACL;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;AC/BO,IAAM,YAAN,cAAwB,WAAW;AAAA,EACtC,cAAc;AACV,UAAM;AACN,SAAK,YAAY,CAAC;AAAA,EACtB;AAAA,EACA,IAAI,MAAM;AACN,WAAO,0BAA0B,KAAK,WAAW,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,EAC9E;AAAA,EACA,IAAI,IAAI,OAAO;AACX,SAAK,YAAY,0BAA0B,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;AAAA,EACpE;AAAA,EACA,KAAK,MAAM;AACP,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,QAAW;AACxB,WAAK,MAAM,KAAK;AAAA,IACpB;AACA,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,KAAK;AAAA,IAC1B;AAAA,EACJ;AACJ;;;ACtBO,IAAM,SAAN,cAAqB,WAAW;AAAA,EACnC,KAAK,MAAM;AACP,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,OAAO,0BAA0B,KAAK,MAAM,CAAC,QAAQ;AACtD,YAAM,MAAM,IAAI,UAAU;AAC1B,UAAI,KAAK,GAAG;AACZ,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACJ;;;ACdO,SAAS,qBAAqB,eAAe,WAAW,cAAc,OAAO;AAChF,MAAI,aAAa,cAAc;AAC3B,UAAM,QAAQ,iBAAiB,YAAY,gBAAgB;AAC3D,WAAO,MAAM,OAAO,eAAe,SAAS;AAAA,EAChD,WACS,YAAY,cAAc;AAC/B,UAAM,QAAQ,iBAAiB,eAAe,aAAa;AAC3D,WAAO,MAAM,OAAO,WAAW,aAAa;AAAA,EAChD;AACJ;;;ACPO,IAAM,UAAN,cAAsB,uBAAuB;AAAA,EAChD,YAAY,WAAW;AACnB,UAAM,SAAS;AACf,SAAK,eAAe,MAAM;AANlC;AAOY,YAAMC,aAAY,KAAK,WAAW,UAAUA,WAAU,eAAe,gBAAgBA,WAAU,cAAc,MAAM,eAAe,gBAAgB,QAAQ,cAAc,MAAM;AAC9K,UAAI,CAAC,iBAAiB,CAAC,eAAe;AAClC;AAAA,MACJ;AACA,UAAI,CAACA,WAAU,QAAQ;AACnB,QAAAA,WAAU,SAAS,CAAC;AAAA,MACxB;AACA,YAAM,WAAWA,WAAU,OAAO;AAClC,UAAI,CAAC,YAAY,WAAW,GAAG;AAC3B;AAAA,MACJ;AACA,YAAM,QAAQA,WAAU,UAAU,SAAS,YAAY,eAAe,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,GAAG,EAAE,OAAO,IAAIA;AACxH,iBAAW,YAAY,OAAO;AAC1B,YAAI,CAAC,OAAO,UAAU;AAClB;AAAA,QACJ;AACA,iBAAS,OAAO,UAAU,CAAC,OAAO;AAClC,cAAM,MAAM,SAAS,YAAY,GAAG,YAAY,YAAY,KAAK,aAAa,GAAG,cAAa,oBAAI,KAAK,GAAE,QAAQ,KAAKA,WAAU,cAAc,MAAM,aAAa,MAAM;AACvK,YAAI,YAAY,cAAc,UAAU;AACpC,iBAAO,cAAc;AAAA,QACzB;AACA,YAAI,YAAY,cAAc,WAAW,GAAG;AACxC,iBAAO,WAAW;AAClB,iBAAO,cAAc;AAAA,QACzB;AACA,cAAM,WAAW;AAAA,UACb,WAAW;AAAA,YACP,UAAUA,WAAU,OAAO;AAAA,YAC3B,OAAO,SAAS,OAAO;AAAA,UAC3B;AAAA,UACA,cAAc;AAAA,YACV,UAAU,YAAY,SAAS,QAAQ,KAAK,KAAK,IAAIA,WAAU,OAAO;AAAA,YACtE,OAAO,SAAS,KAAK;AAAA,UACzB;AAAA,UACA,MAAM;AAAA,QACV;AACA,aAAK,SAAS,UAAU,WAAW,WAAW,QAAQ;AACtD,cAAM,cAAc;AAAA,UAChB,WAAW;AAAA,YACP,UAAU,cAAc;AAAA,YACxB,OAAO,SAAS,OAAO;AAAA,UAC3B;AAAA,UACA,cAAc;AAAA,YACV,UAAU,YAAY,SAAS,QAAQ,QAAQ,KAAK;AAAA,YACpD,SAAO,cAAS,YAAT,mBAAkB,UAAS;AAAA,UACtC;AAAA,UACA,MAAM;AAAA,QACV;AACA,aAAK,SAAS,UAAU,WAAW,WAAW,WAAW;AACzD,YAAI,CAAC,OAAO,eAAe,aAAa,UAAU;AAC9C,eAAK,kBAAkB,UAAU,SAAS;AAAA,QAC9C,OACK;AACD,iBAAO,SAAS,OAAO;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,eAAe,MAAM;AACtB,YAAMA,aAAY,KAAK,WAAW,WAAWA,WAAU,cAAc,MAAM,UAAU,WAAWA,WAAU,OAAO;AACjH,UAAI,CAAC,YAAY,WAAW,KAAK,aAAa,QAAW;AACrD;AAAA,MACJ;AACA,YAAM,QAAQA,WAAU,UAAU,SAAS,YAAY,UAAU,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;AACnG,iBAAW,YAAY,OAAO;AAC1B,iBAAS,OAAO,UAAU;AAC1B,cAAM,MAAM,SAAS,YAAY,GAAG,gBAAgB,YAAY,KAAK,QAAQ,GAAG,QAAQ,IAAI,gBAAgB;AAC5G,YAAI,iBAAiB,UAAU;AAC3B,cAAI,SAAS,KAAKA,WAAU,cAAc,WAAW,gBAAgB;AACjE,iBAAK,iBAAiB,UAAU,KAAK;AACrC,iBAAK,oBAAoB,UAAU,KAAK;AACxC,iBAAK,kBAAkB,UAAU,KAAK;AAAA,UAC1C;AAAA,QACJ,OACK;AACD,eAAK,MAAM,QAAQ;AAAA,QACvB;AACA,YAAIA,WAAU,cAAc,WAAW,iBAAiB;AACpD,eAAK,MAAM,QAAQ;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,oBAAoB,CAAC,UAAU,OAAO,cAAc;AACrD,YAAM,UAAU,KAAK,UAAU,eAAe,gBAAgB,aAAa,QAAQ,cAAc,MAAM;AACvG,UAAI,CAAC,eAAe;AAChB;AAAA,MACJ;AACA,UAAI,CAAC,SAAS,OAAO,YAAY;AAC7B,cAAM,YAAY,cAAc;AAChC,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,cAAM,cAAc,yBAAyB,SAAS;AACtD,iBAAS,OAAO,aAAa,gBAAgB,WAAW;AAAA,MAC5D;AACA,UAAI,CAAC,SAAS,OAAO,YAAY;AAC7B;AAAA,MACJ;AACA,UAAI,cAAc,KAAK;AACnB,iBAAS,OAAO,QAAQ;AACxB,cAAM,SAAS,SAAS,aAAa;AACrC,iBAAS,OAAO,QAAQ,SAClB,SAAS,SAAS,QAAQ,SAAS,OAAO,YAAY,IAAI,OAAO,KAAK,CAAC,IACvE,SAAS,OAAO;AAAA,MAC1B,OACK;AACD,iBAAS,OAAO,QAAQ,SAAS,OAAO;AAAA,MAC5C;AAAA,IACJ;AACA,SAAK,sBAAsB,CAAC,UAAU,OAAO,cAAc;AAnHnE;AAoHY,YAAMA,aAAY,KAAK,WAAW,UAAUA,WAAU,eAAe,eAAc,uCAAW,cAAW,aAAQ,cAAc,MAAM,WAA5B,mBAAoC;AAC7I,UAAI,CAAC,aAAa;AACd;AAAA,MACJ;AACA,YAAM,aAAa,SAAS,QAAQ,QAAQ,OAAO,aAAW,cAAS,YAAT,mBAAkB,UAAS,GAAG,UAAU,qBAAqB,UAAU,aAAa,YAAY,UAAU,GAAG,KAAK;AAChL,UAAI,YAAY,QAAW;AACvB,iBAAS,OAAO,UAAU;AAAA,MAC9B;AAAA,IACJ;AACA,SAAK,mBAAmB,CAAC,UAAU,OAAO,cAAc;AACpD,YAAMA,aAAY,KAAK,WAAW,YAAW,uCAAW,QAAO,UAAU,OAAOA,WAAU,OAAO,aAAaA,WAAU,OAAO;AAC/H,UAAI,aAAa,QAAW;AACxB;AAAA,MACJ;AACA,YAAM,UAAU,YAAY,SAAS,QAAQ,KAAK,KAAK,IAAIA,WAAU,OAAO,YAAY,QAAQ,SAAS,KAAK,OAAO,OAAO,qBAAqB,OAAO,UAAU,SAAS,KAAK;AAChL,UAAI,SAAS,QAAW;AACpB,iBAAS,OAAO,SAAS;AAAA,MAC7B;AAAA,IACJ;AACA,SAAK,WAAW,CAAC,UAAU,WAAW,WAAW,SAAS;AACtD,YAAMA,aAAY,KAAK,WAAW,cAAc,KAAK,UAAU,UAAU,UAAUA,WAAU,eAAe,gBAAgB,QAAQ,cAAc,MAAM;AACxJ,UAAI,CAAC,iBAAiB,gBAAgB,QAAW;AAC7C;AAAA,MACJ;AACA,YAAM,iBAAiB,cAAc,UAAU,iBAAiBA,WAAU,OAAO,oBAAoB,iBAAiB,KAAK,aAAa,UAAU,aAAa,KAAK,UAAU,OAAO,OAAO,KAAK,aAAa,SAAS,GAAG,OAAO,KAAK;AACtO,UAAI,CAAC,kBAAkB,iBAAiB,KAAK,gBAAgB,gBAAgB;AACzE;AAAA,MACJ;AACA,UAAI,CAACA,WAAU,QAAQ;AACnB,QAAAA,WAAU,SAAS,CAAC;AAAA,MACxB;AACA,UAAIA,WAAU,OAAO,aAAa;AAC9B,YAAI,YAAY;AACZ,cAAI,SAAS,QAAQ;AACjB,mBAAO,SAAS,OAAO;AAAA,UAC3B;AACA,cAAI,SAAS,WAAW;AACpB,mBAAO,SAAS,OAAO;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ,OACK;AACD,YAAI,aAAa,gBAAgB;AAC7B,gBAAM,MAAM,cAAc;AAC1B,cAAI,QAAQ,aAAa;AACrB,kBAAM,QAAQ,OAAQ,aAAa,OAAO,eAAgB;AAC1D,gBAAI,SAAS,QAAQ;AACjB,uBAAS,OAAO,SAAS;AAAA,YAC7B;AACA,gBAAI,SAAS,WAAW;AACpB,uBAAS,OAAO,UAAU;AAAA,YAC9B;AAAA,UACJ;AAAA,QACJ,OACK;AACD,cAAI,SAAS,QAAQ;AACjB,mBAAO,SAAS,OAAO;AAAA,UAC3B;AACA,cAAI,SAAS,WAAW;AACpB,mBAAO,SAAS,OAAO;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,uBAAuB,CAAC,OAAO,UAAU,QAAQ;AAClD,YAAMA,aAAY,KAAK,WAAW,YAAY,SAAS,iBAAiB,QAAQ,GAAG,SAASA,WAAU,cAAc,cAAc,MAAM;AACxI,UAAI,CAAC,UAAU,CAAC,UAAU,QAAQ;AAC9B;AAAA,MACJ;AACA,gBAAU,QAAQ,CAAC,SAAS;AACxB,cAAM,OAAO,MAAM,UAAUA,WAAU,OAAO,YAAY,MAAM;AAAA,UAC5D,IAAI,KAAK,aAAa,KAAK,cAAc,KAAK;AAAA,UAC9C,IAAI,KAAK,YAAY,KAAK,eAAe,KAAK;AAAA,QAClD,GAAG,gBAAiB,KAAK,cAAc,IAAK,SAAS,OAAO,IAAI,SAAS,WACnE,IAAI,OAAO,IAAI,GAAG,IAAI,GAAG,aAAa,IACtC,IAAI,UAAU,KAAK,aAAa,SAAS,KAAK,YAAY,SAAS,KAAK,cAAc,SAAS,KAAK,eAAe,OAAO,GAAG,QAAQA,WAAU,UAAU,SAAS,MAAM,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;AAC5M,mBAAW,YAAY,OAAO;AAC1B,cAAI,CAAC,KAAK,SAAS,SAAS,YAAY,CAAC,GAAG;AACxC;AAAA,UACJ;AACA,mBAAS,OAAO,UAAU;AAC1B,gBAAM,OAAO,OAAO,MAAM,YAAY,QAAQ,MAAM,IAAI;AACxD,cAAI,CAAC,SAAS,OAAO,OAAO,SAAS,OAAO,QAAQ,MAAM;AACtD,iBAAK,MAAM,UAAU,OAAO,IAAI;AAChC,qBAAS,OAAO,MAAM;AAAA,UAC1B;AACA,eAAK,iBAAiB,UAAU,GAAG,SAAS;AAC5C,eAAK,oBAAoB,UAAU,GAAG,SAAS;AAC/C,eAAK,kBAAkB,UAAU,GAAG,SAAS;AAAA,QACjD;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,CAAC,UAAU,QAAQ;AACnB,gBAAU,SAAS,CAAC;AAAA,IACxB;AACA,SAAK,kBAAkB,CAAC,SAAS;AAC7B,UAAI,SAAS,UAAU;AACnB;AAAA,MACJ;AACA,UAAI,CAAC,UAAU,QAAQ;AACnB,kBAAU,SAAS,CAAC;AAAA,MACxB;AACA,gBAAU,OAAO,WAAW;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,MAAM,UAAU,OAAO,OAAO;AAC1B,QAAI,SAAS,OAAO,WAAW,CAAC,OAAO;AACnC;AAAA,IACJ;AACA,WAAO,SAAS,OAAO;AACvB,WAAO,SAAS,OAAO;AACvB,WAAO,SAAS,OAAO;AACvB,WAAO,SAAS,OAAO;AAAA,EAC3B;AAAA,EACA,OAAO;AACH,UAAM,YAAY,KAAK,WAAW,SAAS,UAAU,cAAc,cAAc,MAAM;AACvF,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,cAAU,OAAO,qBAAqB,OAAO,WAAW,UAAU,OAAO;AACzE,QAAI,OAAO,SAAS,QAAW;AAC3B,gBAAU,OAAO,iBAAiB,OAAO,OAAO,UAAU,OAAO;AAAA,IACrE;AAAA,EACJ;AAAA,EACA,MAAM,SAAS,OAAO;AAClB,UAAM,UAAU,KAAK,UAAU,eAAe,SAAS,QAAQ,cAAc,QAAQ,UAAU,OAAO,SAAS,UAAU,OAAO,SAAS,eAAe,QAAQ,QAAQ,YAAY,QAAQ,MAAM,eAAe,QAAQ,QAAQ,YAAY,QAAQ,MAAM,OAAO,OAAO;AACzQ,QAAI,gBAAgB,UAAU,UAAU,SAAS,GAAG;AAChD,WAAK,aAAa;AAAA,IACtB,WACS,gBAAgB,UAAU,UAAU,SAAS,GAAG;AACrD,WAAK,aAAa;AAAA,IACtB,OACK;AACD,qBAAe,UAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,qBAAqB,OAAO,UAAU,GAAG,CAAC;AAAA,IACrG;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,QAAQ,UAAU,cAAc,OAAO,WAAU,qCAAU,kBAAiB,QAAQ,eAAe,QAAQ,EAAE,SAAS,OAAO,QAAQ,IAAI,QAAQ,YAAY,iBAAiB,UAAU,KAAK;AAClQ,QAAI,EAAE,aAAc,QAAQ,UAAU,MAAM,YAAc,QAAQ,UAAU,MAAM,gBAAiB;AAC/F,aAAO;AAAA,IACX;AACA,WAAO,UAAU,UAAU,QAAQ,IAAI,KAAK,UAAU,UAAU,QAAQ,IAAI,KAAK;AAAA,EACrF;AAAA,EACA,gBAAgB,YAAY,SAAS;AACjC,QAAI,CAAC,QAAQ,QAAQ;AACjB,cAAQ,SAAS,IAAI,OAAO;AAAA,IAChC;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,OAAO,KAAK,iCAAQ,MAAM;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,MAAM,UAAU;AACZ,aAAS,OAAO,UAAU;AAAA,EAC9B;AACJ;;;AC7QA,eAAsB,8BAA8B,QAAQ,UAAU,MAAM;AACxE,QAAM,OAAO,cAAc,kBAAkB,CAAC,cAAc,IAAI,QAAQ,SAAS,GAAG,OAAO;AAC/F;;;ACHO,IAAM,eAAN,MAAmB;AAAA,EACtB,cAAc;AACV,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACXO,IAAM,UAAN,MAAc;AAAA,EACjB,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,OAAO;AAClB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,cAAc;AACd,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY,OAAO;AACnB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,SAAK,MAAM,KAAK,KAAK,SAAS,KAAK,cAAc,KAAK,WAAW;AACjE,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AACJ;;;AC9BO,SAAS,SAAS,SAAS,IAAI,IAAI,SAAS;AAC/C,QAAM,WAAW,KAAK,MAAM,GAAG,UAAU,IAAI,GAAG,UAAU,CAAC,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,aAAa;AACnH,MAAI,CAAC,UAAU,CAAC,QAAQ;AACpB;AAAA,EACJ;AACA,QAAM,YAAY,GAAG,YAAY,GAAG,UAAU,GAAG,YAAY,GAAG,SAAS,SAAS,QAAQ,QAAQ,GAAG,UAAU,GAAG,GAAG,UAAU,CAAC,GAAG,OAAO,QAAQ,qBAAqB,UAAU,GAAG,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACrN,OAAK,aAAa,GAAG,gBAAgB,QAAQ,OAAO,CAAC;AACrD,OAAK,aAAa,WAAW,IAAI,IAAI,UAAU,gBAAgB,QAAQ,OAAO,CAAC;AAC/E,OAAK,aAAa,GAAG,gBAAgB,QAAQ,OAAO,CAAC;AACrD,SAAO;AACX;AACO,SAAS,gBAAgB,SAAS,OAAOC,YAAW,OAAO,KAAK;AACnE,WAAS,SAAS,OAAO,GAAG;AAC5B,UAAQ,YAAY;AACpB,UAAQ,cAAcA;AACtB,UAAQ,OAAO;AACnB;AACO,SAAS,UAAU,WAAW,KAAK,IAAI,IAAI;AAC9C,QAAM,UAAU,UAAU,eAAe,iBAAiB,QAAQ,cAAc,MAAM;AACtF,MAAI,CAAC,gBAAgB;AACjB;AAAA,EACJ;AACA,SAAO,SAAS,KAAK,IAAI,IAAI,eAAe,MAAM,OAAO;AAC7D;AACO,SAAS,eAAe,WAAW,IAAI,IAAI;AAC9C,YAAU,OAAO,KAAK,CAAC,QAAQ;AAC3B,UAAM,KAAK,UAAU,WAAW,KAAK,IAAI,EAAE;AAC3C,QAAI,CAAC,IAAI;AACL;AAAA,IACJ;AACA,UAAM,OAAO,GAAG,YAAY,GAAG,OAAO,GAAG,YAAY;AACrD,oBAAgB,KAAK,GAAG,OAAO,cAAc,GAAG,IAAI,MAAM,IAAI;AAAA,EAClE,CAAC;AACL;;;AC/BO,IAAM,YAAN,cAAwB,uBAAuB;AAAA,EAClD,YAAY,WAAW;AACnB,UAAM,SAAS;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AACH,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,cAAc,cAAc,MAAM;AACxF,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,cAAU,OAAO,sBAAsB,QAAQ,WAAW,UAAU,OAAO;AAC3E,cAAU,OAAO,oBAAoB,QAAQ,SAAS,UAAU,OAAO;AAAA,EAC3E;AAAA,EACA,MAAM,WAAW;AACb,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU;AACtD,QAAI,QAAQ,cAAc,OAAO,QAAQ,UAAU,UAAU,cAAc,WAAW,eAAe;AACjG,YAAM,WAAW,UAAU,cAAc,MAAM;AAC/C,UAAI,CAAC,UAAU,OAAO,uBAClB,UAAU,OAAO,sBAAsB,KACvC,CAAC,UAAU,OAAO,qBAClB,UAAU,OAAO,oBAAoB,KACrC,CAAC,UAAU;AACX;AAAA,MACJ;AACA,YAAM,WAAW,KAAK,IAAI,UAAU,OAAO,iBAAiB,GAAG,QAAQ,UAAU,UAAU,SAAS,YAAY,UAAU,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;AAC5J,UAAI,IAAI;AACR,iBAAW,MAAM,OAAO;AACpB,cAAM,OAAO,GAAG,YAAY;AAC5B,mBAAW,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG;AACjC,gBAAM,OAAO,GAAG,YAAY,GAAG,UAAU,KAAK,IAAI,UAAU,OAAO,mBAAmB,GAAG,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAC5J,cAAI,QAAQ,WAAW,QAAQ,SAAS;AACpC,2BAAe,WAAW,IAAI,EAAE;AAAA,UACpC;AAAA,QACJ;AACA,UAAE;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,YAAY,KAAK,WAAW,QAAQ,UAAU,cAAc,OAAO,WAAU,qCAAU,kBAAiB,UAAU,cAAc,eAAe;AACrJ,QAAI,EAAE,OAAO,QAAQ,UAAU,MAAM,WAAW;AAC5C,aAAO;AAAA,IACX;AACA,WAAO,UAAU,WAAW,OAAO,QAAQ,IAAI;AAAA,EACnD;AAAA,EACA,gBAAgB,YAAY,SAAS;AACjC,QAAI,CAAC,QAAQ,SAAS;AAClB,cAAQ,UAAU,IAAI,QAAQ;AAAA,IAClC;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,QAAQ,KAAK,iCAAQ,OAAO;AAAA,IACxC;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;AC1DA,eAAsB,+BAA+B,QAAQ,UAAU,MAAM;AACzE,QAAM,OAAO,cAAc,mBAAmB,CAAC,cAAc,IAAI,UAAU,SAAS,GAAG,OAAO;AAClG;;;ACFO,IAAM,YAAN,MAAgB;AAAA,EACnB,cAAc;AACV,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,aAAa,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,IAC3D;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACvBO,IAAM,OAAN,MAAW;AAAA,EACd,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,QAAQ,IAAI,UAAU;AAAA,EAC/B;AAAA,EACA,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,OAAO;AAClB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,cAAc;AACd,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY,OAAO;AACnB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,SAAK,MAAM,KAAK,KAAK,SAAS,KAAK,cAAc,KAAK,WAAW;AAAA,EACrE;AACJ;;;AC1BO,SAAS,aAAa,SAAS,OAAO,OAAO,KAAK,WAAW,SAAS;AACzE,WAAS,SAAS,OAAO,GAAG;AAC5B,UAAQ,cAAc,gBAAgB,WAAW,OAAO;AACxD,UAAQ,YAAY;AACpB,UAAQ,OAAO;AACnB;AACO,SAAS,SAAS,WAAW,UAAU,WAAW,SAAS,UAAU;AACxE,YAAU,OAAO,KAAK,CAAC,QAAQ;AAC3B,UAAM,WAAW,SAAS,YAAY;AACtC,iBAAa,KAAK,SAAS,OAAO,cAAc,GAAG,UAAU,UAAU,WAAW,OAAO;AAAA,EAC7F,CAAC;AACL;;;ACTO,IAAM,UAAN,cAAsB,uBAAuB;AAAA,EAChD,YAAY,WAAW;AACnB,UAAM,SAAS;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AACH,UAAM,YAAY,KAAK,WAAW,OAAO,UAAU,cAAc,cAAc,MAAM;AACrF,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,cAAU,OAAO,mBAAmB,KAAK,WAAW,UAAU,OAAO;AAAA,EACzE;AAAA,EACA,MAAM,WAAW;AAhBrB;AAiBQ,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,gBAAgB,QAAQ;AAC7F,QAAI,CAAC,cAAc,MAAM,QACrB,CAAC,cAAc,OAAO,QAAQ,UAC9B,UAAU,cAAc,WAAW,gBAAgB;AACnD;AAAA,IACJ;AACA,UAAM,WAAW,UAAU,cAAc,MAAM;AAC/C,QAAI,CAAC,UAAU;AACX;AAAA,IACJ;AACA,UAAM,WAAW,UAAU,OAAO;AAClC,QAAI,CAAC,YAAY,WAAW,GAAG;AAC3B;AAAA,IACJ;AACA,UAAM,QAAQ,UAAU,UAAU,SAAS,YAAY,UAAU,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;AACnG,eAAW,YAAY,OAAO;AAC1B,YAAM,MAAM,SAAS,YAAY,GAAG,gBAAgB,YAAY,KAAK,QAAQ;AAC7E,UAAI,gBAAgB,UAAU;AAC1B;AAAA,MACJ;AACA,YAAM,kBAAkB,cAAc,MAAM,KAAK,OAAO,cAAc,gBAAgB,SAAS,cAAc,cAAe,gBAAgB,cAAe;AAC3J,UAAI,eAAe,GAAG;AAClB;AAAA,MACJ;AACA,YAAM,WAAW,gBAAgB,WAAS,cAAS,QAAQ,UAAjB,mBAAwB;AAClE,UAAI,CAAC,UAAU,UAAU,iBAAiB,UAAU;AAChD,cAAM,eAAe,cAAc,MAAM,KAAK;AAC9C,kBAAU,UAAU,gBAAgB,mBAAmB,UAAU,aAAa,OAAO,aAAa,OAAO;AAAA,MAC7G;AACA,YAAM,YAAY,aAAa,UAAU,QAAW,UAAU,UAAU,aAAa;AACrF,UAAI,CAAC,WAAW;AACZ;AAAA,MACJ;AACA,eAAS,WAAW,UAAU,WAAW,aAAa,QAAQ;AAAA,IAClE;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,YAAY,KAAK,WAAW,QAAQ,UAAU,cAAc,OAAO,WAAU,qCAAU,kBAAiB,UAAU,cAAc,eAAe;AACrJ,WAAO,OAAO,QAAQ,UAAU,CAAC,CAAC,MAAM,YAAY,UAAU,QAAQ,OAAO,QAAQ,IAAI;AAAA,EAC7F;AAAA,EACA,gBAAgB,YAAY,SAAS;AACjC,QAAI,CAAC,QAAQ,MAAM;AACf,cAAQ,OAAO,IAAI,KAAK;AAAA,IAC5B;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,KAAK,KAAK,iCAAQ,IAAI;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;AClEA,eAAsB,4BAA4B,QAAQ,UAAU,MAAM;AACtE,QAAM,OAAO,cAAc,gBAAgB,CAAC,cAAc,IAAI,QAAQ,SAAS,GAAG,OAAO;AAC7F;;;ACFO,IAAM,SAAN,cAAqB,uBAAuB;AAAA,EAC/C,YAAY,WAAW;AACnB,UAAM,SAAS;AACf,SAAK,kBAAkB,CAAC,SAAS;AAC7B,UAAI,SAAS,SAAS;AAClB;AAAA,MACJ;AACA,YAAMC,aAAY,KAAK;AACvB,UAAIA,WAAU,mBAAmB,GAAG;AAChC,QAAAA,WAAU,MAAM;AAAA,MACpB,OACK;AACD,QAAAA,WAAU,KAAK;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,MAAM,WAAW;AAAA,EACjB;AAAA,EACA,YAAY;AACR,WAAO;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;AC3BA,eAAsB,6BAA6B,QAAQ,UAAU,MAAM;AACvE,QAAM,OAAO,cAAc,iBAAiB,CAAC,cAAc,IAAI,OAAO,SAAS,GAAG,OAAO;AAC7F;;;ACFO,IAAM,OAAN,MAAW;AAAA,EACd,cAAc;AACV,SAAK,UAAU;AACf,SAAK,SAAS,CAAC;AACf,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,IAAI,eAAe;AACf,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,aAAa,OAAO;AACpB,SAAK,WAAW,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC;AAAA,IAC1C;AACA,QAAI,CAAC,KAAK,OAAO,QAAQ;AACrB,WAAK,UAAU;AAAA,IACnB;AACA,UAAM,WAAW,KAAK,YAAY,KAAK;AACvC,QAAI,aAAa,QAAW;AACxB,WAAK,WAAW,cAAc,QAAQ;AAAA,IAC1C;AAAA,EACJ;AACJ;;;AC7BO,IAAM,SAAN,cAAqB,uBAAuB;AAAA,EAC/C,YAAY,WAAW;AACnB,UAAM,SAAS;AACf,SAAK,kBAAkB,CAAC,SAAS;AAC7B,UAAI,SAAS,QAAQ;AACjB;AAAA,MACJ;AACA,YAAMC,aAAY,KAAK,WAAW,UAAUA,WAAU,eAAe,cAAc,QAAQ,cAAc,MAAM;AAC/G,UAAI,CAAC,aAAa;AACd;AAAA,MACJ;AACA,YAAM,WAAW,cAAc,YAAY,QAAQ;AACnD,UAAI,YAAY,GAAG;AACf;AAAA,MACJ;AACA,YAAM,QAAQ,cAAc,CAAC,QAAW,GAAG,YAAY,MAAM,CAAC,GAAG,eAAe,UAAU,SAAYA,WAAU,cAAc,UAAU,OAAO,KAAK,IAAI;AACxJ,MAAAA,WAAU,UAAU,KAAK,UAAUA,WAAU,cAAc,OAAO,cAAc,KAAK;AAAA,IACzF;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,MAAM,WAAW;AAAA,EACjB;AAAA,EACA,YAAY;AACR,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,YAAY,SAAS;AACjC,QAAI,CAAC,QAAQ,MAAM;AACf,cAAQ,OAAO,IAAI,KAAK;AAAA,IAC5B;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,KAAK,KAAK,iCAAQ,IAAI;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;ACvCA,eAAsB,4BAA4B,QAAQ,UAAU,MAAM;AACtE,QAAM,OAAO,cAAc,gBAAgB,CAAC,cAAc,IAAI,OAAO,SAAS,GAAG,OAAO;AAC5F;;;ACFO,IAAM,SAAN,MAAa;AAAA,EAChB,cAAc;AACV,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,IAAI,eAAe;AACf,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,aAAa,OAAO;AACpB,SAAK,WAAW,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,WAAW,KAAK,YAAY,KAAK;AACvC,QAAI,aAAa,QAAW;AACxB,WAAK,WAAW,cAAc,QAAQ;AAAA,IAC1C;AAAA,EACJ;AACJ;;;AClBO,IAAM,UAAN,cAAsB,uBAAuB;AAAA,EAChD,YAAY,WAAW;AACnB,UAAM,SAAS;AACf,SAAK,kBAAkB,CAAC,SAAS;AAC7B,YAAMC,aAAY,KAAK,WAAW,UAAUA,WAAU;AACtD,UAAI,CAAC,QAAQ,cAAc,MAAM,UAAU,SAAS,UAAU;AAC1D;AAAA,MACJ;AACA,YAAM,WAAW,cAAc,QAAQ,cAAc,MAAM,OAAO,QAAQ;AAC1E,MAAAA,WAAU,UAAU,eAAe,QAAQ;AAAA,IAC/C;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,MAAM,WAAW;AAAA,EACjB;AAAA,EACA,YAAY;AACR,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,YAAY,SAAS;AACjC,QAAI,CAAC,QAAQ,QAAQ;AACjB,cAAQ,SAAS,IAAI,OAAO;AAAA,IAChC;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,OAAO,KAAK,iCAAQ,MAAM;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;AChCA,eAAsB,8BAA8B,QAAQ,UAAU,MAAM;AACxE,QAAM,OAAO,cAAc,kBAAkB,CAAC,cAAc,IAAI,QAAQ,SAAS,GAAG,OAAO;AAC/F;;;ACHO,IAAM,cAAN,MAAkB;AAAA,EACrB,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AAAA,EACJ;AACJ;;;AC9BO,IAAM,aAAN,cAAyB,YAAY;AAAA,EACxC,cAAc;AACV,UAAM;AACN,SAAK,YAAY,CAAC;AAAA,EACtB;AAAA,EACA,IAAI,MAAM;AACN,WAAO,0BAA0B,KAAK,WAAW,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,EAC9E;AAAA,EACA,IAAI,IAAI,OAAO;AACX,SAAK,YAAY,0BAA0B,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;AAAA,EACpE;AAAA,EACA,KAAK,MAAM;AACP,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,QAAW;AACxB,WAAK,MAAM,KAAK;AAAA,IACpB;AACA,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,KAAK;AAAA,IAC1B;AAAA,EACJ;AACJ;;;ACtBO,IAAM,UAAN,cAAsB,YAAY;AAAA,EACrC,KAAK,MAAM;AACP,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,OAAO,0BAA0B,KAAK,MAAM,CAAC,QAAQ;AACtD,YAAM,MAAM,IAAI,WAAW;AAC3B,UAAI,KAAK,GAAG;AACZ,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACJ;;;ACbO,IAAM,WAAN,cAAuB,uBAAuB;AAAA,EACjD,YAAY,QAAQ,WAAW;AAC3B,UAAM,SAAS;AACf,SAAK,gBAAgB,MAAM;AACvB,YAAMC,aAAY,KAAK,WAAW,iBAAiBA,WAAU,cAAc,cAAc,MAAM;AAC/F,UAAI,CAAC,gBAAgB;AACjB;AAAA,MACJ;AACA,YAAM,UAAUA,WAAU,WAAW,EAAE,WAAW,CAAC,EAAE;AACrD,UAAI,CAAC,QAAQ,QAAQ;AACjB,YAAI,CAAC,QAAQ,OAAO;AAChB,kBAAQ,QAAQ;AAAA,QACpB;AACA,gBAAQ;AACR,YAAI,QAAQ,UAAUA,WAAU,UAAU,OAAO;AAC7C,kBAAQ,SAAS;AAAA,QACrB;AAAA,MACJ;AACA,UAAI,QAAQ,UAAU;AAClB,cAAM,kBAAkBA,WAAU,OAAO;AACzC,YAAI,CAAC,mBAAmB,kBAAkB,GAAG;AACzC;AAAA,QACJ;AACA,cAAM,gBAAgB,KAAK,IAAI,kBAAkB,GAAG,CAAC,GAAG,gBAAgBA,WAAU,cAAc,MAAM;AACtG,YAAI,kBAAkB,QAAW;AAC7B;AAAA,QACJ;AACA,cAAM,QAAQ,IAAI,OAAO,cAAc,GAAG,cAAc,GAAG,aAAa,GAAG,QAAQA,WAAU,UAAU,SAAS,MAAM,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;AACrJ,mBAAW,YAAY,OAAO;AAC1B,gBAAM,EAAE,IAAI,IAAI,SAAS,IAAI,aAAa,eAAe,SAAS,QAAQ,GAAG,IAAI,YAAY,GAAG,WAAW,eAAe,OAAO,QAAS,CAAC,gBAAgB,WAAY;AACvK,cAAI,KAAK,eAAe;AACpB,oBAAQ,UAAU,KAAK,QAAQ;AAC/B,kBAAM,OAAO,OAAO,OAAO,IAAI,EAAE;AACjC,iBAAK,SAAS;AACd,qBAAS,SAAS,MAAM,IAAI;AAAA,UAChC;AAAA,QACJ;AAAA,MACJ,WACS,QAAQ,aAAa,OAAO;AACjC,mBAAW,YAAY,QAAQ,WAAW;AACtC,mBAAS,SAAS,MAAM,SAAS,eAAe;AAAA,QACpD;AACA,gBAAQ,YAAY,CAAC;AAAA,MACzB;AAAA,IACJ;AACA,SAAK,gBAAgB,MAAM;AACvB,YAAMA,aAAY,KAAK,WAAW,WAAWA,WAAU,cAAc,MAAM,UAAU,gBAAgBA,WAAU,OAAO;AACtH,UAAI,CAAC,iBAAiB,gBAAgB,KAAK,CAAC,UAAU;AAClD;AAAA,MACJ;AACA,WAAK,gBAAgB,UAAU,eAAe,IAAI,OAAO,SAAS,GAAG,SAAS,GAAG,aAAa,CAAC;AAAA,IACnG;AACA,SAAK,kBAAkB,CAAC,UAAU,eAAe,MAAM,eAAe;AAClE,YAAMA,aAAY,KAAK,WAAW,QAAQA,WAAU,UAAU,SAAS,MAAM,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,GAAG,iBAAiBA,WAAU,cAAc,cAAc,MAAM;AAC3K,UAAI,CAAC,gBAAgB;AACjB;AAAA,MACJ;AACA,iBAAW,YAAY,OAAO;AAC1B,cAAM,EAAE,IAAI,IAAI,SAAS,IAAI,aAAa,SAAS,UAAU,QAAQ,GAAG,aAAY,yCAAY,UAAS,eAAe,SAAS,eAAe,QAAQ,gBAAgB,MAAM,UAAU,eAAe,MAAM,EAAE,IAAI,WAAW,aAAa,IAAI,UAAU,GAAG,eAAe,QAAQ,GAAG,UAAU,OAAO,OAAO,aAAa,IAAI,WAAY,KAAK,WAAY,eAAe,aAAa,IAAI,WAAY,KAAK,WAAY,aAAa;AACta,iBAAS,SAAS,MAAM,OAAO;AAAA,MACnC;AAAA,IACJ;AACA,SAAK,yBAAyB,CAAC,UAAU,QAAQ;AAC7C,YAAMA,aAAY,KAAK,WAAW,UAAUA,WAAU,cAAc,cAAc,MAAM;AACxF,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AACA,YAAM,QAAQ,SAAS,iBAAiB,QAAQ;AAChD,UAAI,CAAC,MAAM,QAAQ;AACf;AAAA,MACJ;AACA,YAAM,QAAQ,CAAC,SAAS;AACpB,cAAM,OAAO,MAAM,UAAUA,WAAU,OAAO,YAAY,MAAM;AAAA,UAC5D,IAAI,KAAK,aAAa,KAAK,cAAc,KAAK;AAAA,UAC9C,IAAI,KAAK,YAAY,KAAK,eAAe,KAAK;AAAA,QAClD,GAAG,gBAAiB,KAAK,cAAc,IAAK,SAAS,OAAO,IAAI,SAAS,WACnE,IAAI,OAAO,IAAI,GAAG,IAAI,GAAG,aAAa,IACtC,IAAI,UAAU,KAAK,aAAa,SAAS,KAAK,YAAY,SAAS,KAAK,cAAc,SAAS,KAAK,eAAe,OAAO,GAAG,OAAO,QAAQ,MAAM,aAAa,QAAQ,MAAM,IAAI;AACvL,aAAK,gBAAgB,KAAK,eAAe,MAAM,UAAU;AAAA,MAC7D,CAAC;AAAA,IACL;AACA,SAAK,UAAU;AACf,QAAI,CAAC,UAAU,SAAS;AACpB,gBAAU,UAAU,EAAE,WAAW,CAAC,EAAE;AAAA,IACxC;AACA,SAAK,kBAAkB,CAAC,SAAS;AAC7B,YAAM,UAAU,KAAK,UAAU,eAAe,cAAc,QAAQ,cAAc,MAAM;AACxF,UAAI,CAAC,eAAe,SAAS,WAAW;AACpC;AAAA,MACJ;AACA,UAAI,CAAC,UAAU,SAAS;AACpB,kBAAU,UAAU,EAAE,WAAW,CAAC,EAAE;AAAA,MACxC;AACA,YAAM,UAAU,UAAU;AAC1B,cAAQ,WAAW;AACnB,cAAQ,QAAQ;AAChB,iBAAW,YAAY,UAAU,QAAQ,WAAW;AAChD,YAAI,CAAC,KAAK,UAAU,QAAQ,GAAG;AAC3B;AAAA,QACJ;AACA,iBAAS,SAAS,MAAM,SAAS,eAAe;AAAA,MACpD;AACA,cAAQ,YAAY,CAAC;AACrB,cAAQ,SAAS;AACjB,iBAAW,MAAM;AACb,YAAI,UAAU,WAAW;AACrB;AAAA,QACJ;AACA,gBAAQ,WAAW;AAAA,MACvB,GAAG,YAAY,WAAW,GAAI;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AACH,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,cAAc,cAAc,MAAM;AACxF,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,cAAU,OAAO,sBAAsB,QAAQ,WAAW,UAAU,OAAO;AAAA,EAC/E;AAAA,EACA,MAAM,WAAW;AACb,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,kBAAkB,UAAU,cAAc,WAAW,gBAAgB,SAAS,QAAQ,cAAc,QAAQ,QAAQ,OAAO,SAAS,eAAe,MAAM,QAAQ,YAAY,MAAM,MAAM,QAAQ,OAAO,SAAS,eAAe,MAAM,QAAQ,YAAY,MAAM,MAAM,OAAO,OAAO;AACzV,QAAI,mBAAmB,gBAAgB,UAAU,WAAW,SAAS,GAAG;AACpE,WAAK,cAAc;AAAA,IACvB,WACS,gBAAgB,UAAU,WAAW,SAAS,GAAG;AACtD,WAAK,cAAc;AAAA,IACvB,OACK;AACD,qBAAe,WAAW,MAAM,CAAC,UAAU,QAAQ,KAAK,uBAAuB,UAAU,GAAG,CAAC;AAAA,IACjG;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,QAAQ,UAAU,cAAc,OAAO,WAAU,qCAAU,kBAAiB,QAAQ,eAAe,QAAQ,OAAO,OAAO,OAAO,QAAQ,OAAO,SAAS,QAAQ,OAAO,SAAS,aAAa,iBAAiB,WAAW,IAAI;AAClS,QAAI,EAAE,cAAe,MAAM,UAAU,MAAM,YAAc,MAAM,UAAU,MAAM,gBAAiB;AAC5F,aAAO;AAAA,IACX;AACA,UAAM,YAAY,MAAM,MAAM,YAAY,MAAM;AAChD,WAAO,UAAU,WAAW,SAAS,KAAK,UAAU,WAAW,SAAS,KAAK;AAAA,EACjF;AAAA,EACA,gBAAgB,YAAY,SAAS;AACjC,QAAI,CAAC,QAAQ,SAAS;AAClB,cAAQ,UAAU,IAAI,QAAQ;AAAA,IAClC;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,QAAQ,KAAK,iCAAQ,OAAO;AAAA,IACxC;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;ACxJA,eAAsB,+BAA+B,QAAQ,UAAU,MAAM;AACzE,QAAM,OAAO,cAAc,mBAAmB,CAAC,cAAc,IAAI,SAAS,QAAQ,SAAS,GAAG,OAAO;AACzG;;;ACHO,IAAM,OAAN,MAAW;AAAA,EACd,cAAc;AACV,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AACJ;;;ACdO,IAAM,SAAN,cAAqB,uBAAuB;AAAA,EAC/C,YAAY,WAAW;AACnB,UAAM,SAAS;AAAA,EACnB;AAAA,EACA,MAAM,UAAU,OAAO,OAAO;AAC1B,QAAI,SAAS,KAAK,WAAW,CAAC,OAAO;AACjC;AAAA,IACJ;AACA,aAAS,KAAK,SAAS;AAAA,EAC3B;AAAA,EACA,OAAO;AACH,UAAM,YAAY,KAAK,WAAW,OAAO,UAAU,cAAc,cAAc,MAAM;AACrF,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,cAAU,OAAO,iBAAiB,KAAK,SAAS,UAAU,OAAO;AAAA,EACrE;AAAA,EACA,MAAM,WAAW;AAAA,EACjB;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,YAAY,KAAK,WAAW,QAAQ,UAAU,cAAc,OAAO,WAAU,qCAAU,kBAAiB,UAAU,cAAc,eAAe;AACrJ,WAAO,OAAO,QAAQ,UAAU,CAAC,CAAC,MAAM,YAAY,UAAU,QAAQ,OAAO,QAAQ,IAAI;AAAA,EAC7F;AAAA,EACA,gBAAgB,YAAY,SAAS;AACjC,QAAI,CAAC,QAAQ,MAAM;AACf,cAAQ,OAAO,IAAI,KAAK;AAAA,IAC5B;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,KAAK,KAAK,iCAAQ,IAAI;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,MAAM,UAAU;AACZ,aAAS,KAAK,UAAU;AACxB,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,WAAW,UAAU,cAAc,MAAM,UAAU,SAAS,UAAU,OAAO,gBAAgB,cAAc,QAAQ,cAAc,MAAM;AAC5M,QAAI,CAAC,eAAe,CAAC,UAAU,SAAS,KAAK,CAAC,UAAU;AACpD;AAAA,IACJ;AACA,UAAM,cAAc,SAAS,YAAY,GAAG,OAAO,YAAY,UAAU,WAAW,GAAG,kBAAkB,OAAO,QAAQ,aAAa,YAAY,QAAQ,EAAE,KAAK,IAAI;AACpK,QAAI,OAAO,QAAQ;AACf;AAAA,IACJ;AACA,SAAK,UAAU;AACf,SAAK,SAAS,kBAAkB;AAAA,EACpC;AACJ;;;AC7CA,eAAsB,4BAA4B,QAAQ,UAAU,MAAM;AACtE,QAAM,OAAO,cAAc,gBAAgB,CAAC,cAAc,IAAI,OAAO,SAAS,GAAG,OAAO;AAC5F;;;ACHO,IAAM,mBAAmB,CAAC,GAAG,GAAG,GAAG,CAAC;AACpC,IAAM,iBAAiB,CAAC,GAAG,GAAG,GAAG,CAAC;;;ACDlC,IAAM,aAAN,MAAiB;AAAA,EACpB,YAAY,OAAO;AACf,SAAK,MAAM;AACX,SAAK,OAAO,IAAI,kBAAkB,KAAK;AAAA,EAC3C;AAAA,EACA,UAAU,OAAO;AACb,UAAM,QAAQ,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACxD,SAAK,OAAO,MAAM;AAClB,WAAO,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,OAAO,aAAa,IAAI,GAAG,EAAE;AAAA,EAC1E;AAAA,EACA,WAAW;AACP,WAAO,KAAK,KAAK,KAAK,KAAK;AAAA,EAC/B;AAAA,EACA,eAAe;AACX,SAAK,OAAO;AACZ,WAAO,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,CAAC,KAAK;AAAA,EACjE;AAAA,EACA,gBAAgB;AACZ,QAAI,cAAc,IAAI,OAAO;AAC7B,OAAG;AACC,aAAO,KAAK,KAAK,KAAK,KAAK;AAC3B,eAAS,QAAQ,MAAM,EAAE,SAAS,GAAG,eAAe,OAAO,aAAa,KAAK,KAAK,KAAK,KAAK,CAAC,GAAG;AAAA,MAChG;AAAA,IACJ,SAAS,SAAS;AAClB,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB;AACf,QAAI,OAAO,GAAG,MAAM;AACpB,aAAS,SAAS,IAAI,OAAO,KAAK,KAAK,KAAK,MAAM,MAAM,OAAO,GAAG,UAAU,OAAO,GAAG;AAClF,aAAO;AAAA,IACX;AACA,UAAM,YAAY,IAAI,WAAW,GAAG;AACpC,aAAS,IAAI,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,OAAO,KAAI;AACnD,eAAS,QAAQ,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,IAAI,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,MAC7E;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB;AACZ,WAAO,KAAK,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,GAAG;AAAA,IACvE;AACA,SAAK;AAAA,EACT;AACJ;;;ACzCA,SAAS,gBAAgB,YAAY,OAAO;AACxC,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,WAAO,KAAK;AAAA,MACR,GAAG,WAAW,KAAK,WAAW,GAAG;AAAA,MACjC,GAAG,WAAW,KAAK,WAAW,MAAM,CAAC;AAAA,MACrC,GAAG,WAAW,KAAK,WAAW,MAAM,CAAC;AAAA,IACzC,CAAC;AACD,eAAW,OAAO;AAAA,EACtB;AACA,SAAO;AACX;AACA,eAAe,oBAAoB,YAAY,KAAK,eAAe,sBAAsB;AACrF,UAAQ,WAAW,SAAS,GAAG;AAAA,IAC3B,KAAK,KAAK;AACN,YAAM,QAAQ,IAAI,OAAO,cAAc,KAAK,CAAC;AAC7C,iBAAW;AACX,YAAM,aAAa,WAAW,SAAS;AACvC,YAAM,cAAc,aAAa,SAAU;AAC3C,YAAM,kBAAkB,aAAa,QAAU;AAC/C,YAAM,sBAAsB,aAAa,OAAO;AAChD,YAAM,oBAAoB,aAAa,OAAO;AAC9C,YAAM,YAAY,WAAW,aAAa,IAAI;AAC9C,YAAM,oBAAoB,WAAW,SAAS;AAC9C,UAAI,kBAAkB;AAClB,6BAAqB,iBAAiB;AAAA,MAC1C;AACA,iBAAW;AACX;AAAA,IACJ;AAAA,IACA,KAAK,KAAK;AACN,iBAAW;AACX,YAAM,uBAAuB;AAAA,QACzB,YAAY,WAAW,UAAU,CAAC;AAAA,QAClC,oBAAoB,WAAW,UAAU,CAAC;AAAA,QAC1C,MAAM,WAAW,iBAAiB;AAAA,MACtC;AACA,UAAI,sBAAsB,KAAK,oBAAoB;AACnD;AAAA,IACJ;AAAA,IACA,KAAK,KAAK;AACN,UAAI,SAAS,KAAK,CAAC,cAAc,KAAK,GAAG,WAAW,cAAc,CAAC,CAAC;AACpE;AAAA,IACJ;AAAA,IACA,KAAK,GAAG;AACJ,UAAI,IAAI,iBAAiB,WAAW,GAAG;AACnC,cAAM,IAAI,UAAU,iDAAiD;AAAA,MACzE;AACA,iBAAW;AACX,UAAI,OAAO,cAAc,KAAK,CAAC,EAAE,gBAAgB;AAAA,QAC7C,MAAM,WAAW,aAAa;AAAA,QAC9B,KAAK,WAAW,aAAa;AAAA,QAC7B,OAAO,WAAW,aAAa;AAAA,QAC/B,QAAQ,WAAW,aAAa;AAAA,QAChC,UAAU;AAAA,UACN,OAAO,WAAW,aAAa;AAAA,UAC/B,QAAQ,WAAW,aAAa;AAAA,QACpC;AAAA,QACA,iBAAiB,WAAW,SAAS;AAAA,QACrC,iBAAiB,WAAW,SAAS;AAAA,QACrC,MAAM,WAAW,cAAc;AAAA,MACnC;AACA;AAAA,IACJ;AAAA,IACA;AACI,iBAAW,cAAc;AACzB;AAAA,EACR;AACJ;AACA,eAAe,gBAAgB,YAAY,KAAK,UAAU,eAAe,sBAAsB,kBAAkB;AAC7G,QAAM,QAAQ,IAAI,OAAO,cAAc,IAAI,CAAC;AAC5C,QAAM,OAAO,WAAW,aAAa;AACrC,QAAM,MAAM,WAAW,aAAa;AACpC,QAAM,QAAQ,WAAW,aAAa;AACtC,QAAM,SAAS,WAAW,aAAa;AACvC,QAAM,aAAa,WAAW,SAAS,GAAG,uBAAuB,aAAa,SAAU,KAAM,kBAAkB,aAAa,QAAU;AACvI,QAAM,YAAY,aAAa,QAAU;AACzC,QAAM,YAAY,aAAa,QAAU;AACzC,QAAM,kBAAkB,MAAO,aAAa,KAAK;AACjD,MAAI,qBAAqB;AACrB,UAAM,kBAAkB,gBAAgB,YAAY,eAAe;AAAA,EACvE;AACA,QAAM,WAAW,CAAC,UAAU;AACxB,UAAM,EAAE,GAAG,GAAG,EAAE,KAAK,sBAAsB,MAAM,kBAAkB,IAAI,kBAAkB,KAAK;AAC9F,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,UAAU,qBAAqB,IAAI,IAAK,WAAW,CAAC,GAAG,IAAI,IAAI,KAAK,KAAK,IAAK,IAAI;AAAA,EAC3G;AACA,QAAM,SAAS,MAAM;AACjB,QAAI;AACA,aAAO,IAAI,UAAU,MAAM,OAAO,MAAM,QAAQ,EAAE,YAAY,OAAO,CAAC;AAAA,IAC1E,SACO,OAAO;AACV,UAAI,iBAAiB,gBAAgB,MAAM,SAAS,kBAAkB;AAClE,eAAO;AAAA,MACX;AACA,YAAM;AAAA,IACV;AAAA,EACJ,GAAG;AACH,MAAI,SAAS,MAAM;AACf,UAAM,IAAI,UAAU,4BAA4B;AAAA,EACpD;AACA,QAAM,cAAc,WAAW,SAAS,GAAG,YAAY,WAAW,iBAAiB,GAAG,YAAY,KAAK;AACvG,QAAM,WAAW,CAAC,KAAK,QAAQ;AAC3B,UAAM,UAAU,QAAQ,GAAG,SAAS,MAAM;AAC1C,YAAU,UAAU,OAAO,KAAK,UAAU,UAAU,CAAC,KAAK,MAAM,UAAU,UAAU,CAAC,KAAK,OACnF,KAAK,OAAO,KAAM,YACrB;AAAA,EACR;AACA,MAAI,gBAAgB;AAChB,aAAS,OAAO,GAAG,OAAO,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,OAAO,GAAG,QAAQ;AACzF,UAAI,iBAAiB,IAAI,IAAI,MAAM,QAAQ;AACvC,iBAAS,WAAW,GAAG,YAAY,OAAK;AACpC,gBAAM,OAAO;AACb,iBAAO,SAAS,KAAK,IAAI;AACzB,iBAAO,OAAO;AACd,cAAI,SAAS,WAAW;AACpB,mBAAO,cAAc;AACrB,gBAAI,SAAS,YAAY;AACzB,qBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,kBAAI,CAAC,IAAI,IAAI,YAAY,CAAC,CAAC,IAAI,CAAC;AAAA,YACpC;AAAA,UACJ,OACK;AACD,gBAAI,QAAQ,IAAI,QAAQ;AACpB,kBAAI,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;AAAA,YAC3C,WACS,SAAS,WAAW;AACzB,kBAAI,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;AAAA,YAC3C;AACA,qBAAS,IAAI,GAAG,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK;AACvC,oBAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC,CAAC;AAC5C,oBAAM,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,iBAAiB,IAAI,IAAI,MAAM,QACxD,eAAe,IAAI,IAAI,YACtB,YAAY,MAAM,QAAQ,EAAG;AAClC,0BAAY;AAAA,YAChB;AACA,gBAAI,IAAI,WAAW,KAAK,QAAQ,OAAO,IAAK;AACxC;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,aAAa,MAAM,QAAQ,KAAK,YAAY,IAAI;AAChD;AACA,gBAAI,iBAAiB,IAAI,IAAI,eAAe,IAAI,IAAI,aAAa,MAAM,QAAQ;AAC3E;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,2DAAmB,WAAW,OAAO,WAAW,KAAK,SAAS,IAAI,cAAc,KAAK,IAAI,GAAG,OAAO,EAAE,GAAG,MAAM,MAAM,GAAG,MAAM,IAAI,GAAG,EAAE,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAO;AAAA,IAC/K;AACA,UAAM,QAAQ;AACd,UAAM,SAAS,MAAM,kBAAkB,KAAK;AAAA,EAChD,OACK;AACD,aAAS,OAAO,GAAG,OAAO,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,QAAM;AAC9E,YAAM,OAAO;AACb,aAAO,SAAS,KAAK,IAAI;AACzB,aAAO;AACP,UAAI,SAAS,WAAW;AACpB,eAAO,cAAc;AACrB,YAAI,SAAS,YAAY;AACzB,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,cAAI,CAAC,IAAI,IAAI,YAAY,CAAC,CAAC,IAAI,CAAC;AAAA,QACpC;AAAA,MACJ,OACK;AACD,YAAI,SAAS,YAAY,GAAG;AACxB;AAAA,QACJ;AACA,YAAI,QAAQ,IAAI,QAAQ;AACpB,cAAI,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;AAAA,QAC3C,WACS,SAAS,WAAW;AACzB,cAAI,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;AAAA,QAC3C;AACA,iBAAS,IAAI,GAAG,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK;AACvC,gBAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC,CAAC;AAC5C,gBAAM,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAI,YAAY,CAAE;AAAA,QAChD;AACA,YAAI,IAAI,UAAU,KAAK,QAAQ,OAAO,IAAK;AACvC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,QAAQ;AACd,UAAM,SAAS,MAAM,kBAAkB,KAAK;AAC5C,0DAAoB,WAAW,MAAM,KAAK,WAAW,KAAK,QAAQ,cAAc,KAAK,IAAI,GAAG,MAAM,OAAO,EAAE,GAAG,MAAM,MAAM,GAAG,MAAM,IAAI,GAAG,EAAE,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAO;AAAA,EACrL;AACJ;AACA,eAAe,WAAW,YAAY,KAAK,UAAU,eAAe,sBAAsB,kBAAkB;AACxG,UAAQ,WAAW,SAAS,GAAG;AAAA,IAC3B,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,YAAM,gBAAgB,YAAY,KAAK,UAAU,eAAe,sBAAsB,gBAAgB;AACtG;AAAA,IACJ,KAAK;AACD,YAAM,oBAAoB,YAAY,KAAK,eAAe,oBAAoB;AAC9E;AAAA,IACJ;AACI,YAAM,IAAI,UAAU,uBAAuB;AAAA,EACnD;AACA,SAAO;AACX;AACO,SAAS,iBAAiB,KAAK;AAClC,aAAW,aAAa,IAAI,uBAAuB;AAC/C,QAAI,UAAU,aAAa,UAAU,uBAAuB,eAAe;AACvE;AAAA,IACJ;AACA,WAAO,UAAU,KAAK,CAAC,KAAK,UAAU,KAAK,CAAC,KAAK;AAAA,EACrD;AACA,SAAO;AACX;AACA,eAAsB,UAAU,QAAQ,kBAAkB,UAAU;AAChE,MAAI,CAAC;AACD,eAAW;AACf,QAAM,MAAM,MAAM,MAAM,MAAM;AAC9B,MAAI,CAAC,IAAI,MAAM,IAAI,WAAW,KAAK;AAC/B,UAAM,IAAI,UAAU,gBAAgB;AAAA,EACxC;AACA,QAAM,SAAS,MAAM,IAAI,YAAY;AACrC,QAAM,MAAM;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,QAAQ,CAAC;AAAA,IACT,UAAU;AAAA,IACV,kBAAkB,CAAC;AAAA,IACnB,iBAAiB,IAAI,UAAU,GAAG,GAAG,EAAE,YAAY,OAAO,CAAC;AAAA,IAC3D,UAAU,CAAC;AAAA,IACX,uBAAuB,CAAC;AAAA,EAC5B,GAAG,aAAa,IAAI,WAAW,IAAI,kBAAkB,MAAM,CAAC;AAC5D,MAAI,WAAW,UAAU,CAAC,MAAM,UAAU;AACtC,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC9C;AACA,MAAI,QAAQ,WAAW,aAAa;AACpC,MAAI,SAAS,WAAW,aAAa;AACrC,QAAM,aAAa,WAAW,SAAS,GAAG,wBAAwB,aAAa,SAAU;AACzF,MAAI,YAAY,aAAa,SAAU;AACvC,MAAI,YAAY,aAAa,OAAO;AACpC,QAAM,mBAAmB,MAAO,aAAa,KAAK,GAAI,uBAAuB,WAAW,SAAS;AACjG,MAAI,mBAAmB,WAAW,SAAS;AAC3C,MAAI,IAAI,qBAAqB,GAAG;AAC5B,QAAI,oBAAoB,IAAI,mBAAmB,MAAO;AAAA,EAC1D;AACA,MAAI,sBAAsB;AACtB,QAAI,mBAAmB,gBAAgB,YAAY,gBAAgB;AAAA,EACvE;AACA,QAAM,mBAAmB,MAAM;AAC3B,QAAI;AACA,aAAO,IAAI,UAAU,IAAI,OAAO,IAAI,QAAQ,EAAE,YAAY,OAAO,CAAC;AAAA,IACtE,SACO,OAAO;AACV,UAAI,iBAAiB,gBAAgB,MAAM,SAAS,kBAAkB;AAClE,eAAO;AAAA,MACX;AACA,YAAM;AAAA,IACV;AAAA,EACJ,GAAG;AACH,MAAI,mBAAmB,MAAM;AACzB,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAChD;AACA,QAAM,EAAE,GAAG,GAAG,EAAE,IAAI,IAAI,iBAAiB,oBAAoB;AAC7D,kBAAgB,KAAK,IAAI,uBAAuB,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAC7E,WAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK,QAAQ,KAAK,GAAG;AACrD,oBAAgB,KAAK,WAAW,GAAG,GAAG,CAAC;AAAA,EAC3C;AACA,MAAI,kBAAkB;AACtB,MAAI,aAAa,IAAI,sBAAsB,MAAM,oBAAoB;AACrE,QAAM,gBAAgB,CAAC,cAAc;AACjC,QAAI,WAAW;AACX,4BAAsB;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,QAAM,uBAAuB,CAAC,aAAa;AACvC,QAAI,YAAY,MAAM;AAClB,0BAAoB;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AACA,MAAI;AACA,OAAG;AACC,UAAI,qBAAqB;AACrB,YAAI,OAAO,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,KAAK;AAAA,UACL,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,gBAAgB;AAAA,UAChB,OAAO,IAAI,UAAU,GAAG,GAAG,EAAE,YAAY,OAAO,CAAC;AAAA,UACjD,eAAe;AAAA,UACf,oBAAoB;AAAA,UACpB,WAAW;AAAA,UACX,UAAU;AAAA,UACV,iBAAiB,CAAC;AAAA,UAClB,UAAU;AAAA,UACV,YAAY;AAAA,QAChB,CAAC;AACD;AACA,4BAAoB;AACpB,8BAAsB;AAAA,MAC1B;AAAA,IACJ,SAAS,CAAE,MAAM,WAAW,YAAY,KAAK,UAAU,eAAe,sBAAsB,gBAAgB;AAC5G,QAAI,OAAO;AACX,eAAW,SAAS,IAAI,QAAQ;AAC5B,UAAI,MAAM,sBAAsB,MAAM,cAAc,GAAG;AACnD,YAAI,YAAY;AAChB;AAAA,MACJ;AACA,UAAI,aAAa,MAAM;AAAA,IAC3B;AACA,WAAO;AAAA,EACX,SACO,OAAO;AACV,QAAI,iBAAiB,WAAW;AAC5B,YAAM,IAAI,MAAM,6BAA6B,UAAU,KAAK,MAAM,OAAO,GAAG;AAAA,IAChF;AACA,UAAM;AAAA,EACV;AACJ;;;ACjUA,IAAM,oBAAoB;AAC1B,SAAS,gBAAgB,YAAY,OAAO,SAAS;AACjD,QAAM,EAAE,QAAQ,IAAI;AACpB,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,QAAM,aAAa,gBAAgB,OAAO,OAAO;AACjD,MAAI,QAAQ,SAAS,MAAM,GAAG;AAC1B,WAAO,QAAQ,QAAQ,mBAAmB,MAAM,UAAU;AAAA,EAC9D;AACA,QAAM,eAAe,QAAQ,QAAQ,GAAG;AACxC,SAAO,GAAG,QAAQ,UAAU,GAAG,YAAY,CAAC,UAAU,UAAU,IAAI,QAAQ,UAAU,YAAY,CAAC;AACvG;AACA,eAAsB,UAAU,OAAO;AACnC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,UAAM,UAAU;AAChB,UAAM,MAAM,IAAI,MAAM;AACtB,UAAM,UAAU;AAChB,QAAI,iBAAiB,QAAQ,MAAM;AAC/B,YAAM,UAAU;AAChB,cAAQ;AAAA,IACZ,CAAC;AACD,QAAI,iBAAiB,SAAS,MAAM;AAChC,YAAM,UAAU;AAChB,YAAM,QAAQ;AACd,YAAM,UAAU;AAChB,gBAAU,EAAE,MAAM,GAAG,WAAW,mBAAmB,MAAM,MAAM,EAAE;AACjE,cAAQ;AAAA,IACZ,CAAC;AACD,QAAI,MAAM,MAAM;AAAA,EACpB,CAAC;AACL;AACA,eAAsB,aAAa,OAAO;AACtC,MAAI,MAAM,SAAS,OAAO;AACtB,UAAM,UAAU,KAAK;AACrB;AAAA,EACJ;AACA,QAAM,UAAU;AAChB,MAAI;AACA,UAAM,UAAU,MAAM,UAAU,MAAM,MAAM;AAC5C,UAAM,eAAe,iBAAiB,MAAM,OAAO,KAAK;AACxD,QAAI,MAAM,iBAAiB,GAAG;AAC1B,YAAM,eAAe;AAAA,IACzB;AAAA,EACJ,QACM;AACF,UAAM,QAAQ;AAAA,EAClB;AACA,QAAM,UAAU;AACpB;AACA,eAAsB,iBAAiB,OAAO;AAC1C,MAAI,MAAM,SAAS,OAAO;AACtB,UAAM,UAAU,KAAK;AACrB;AAAA,EACJ;AACA,QAAM,UAAU;AAChB,QAAM,WAAW,MAAM,MAAM,MAAM,MAAM;AACzC,MAAI,CAAC,SAAS,IAAI;AACd,cAAU,EAAE,MAAM,GAAG,WAAW,kBAAkB;AAClD,UAAM,QAAQ;AAAA,EAClB,OACK;AACD,UAAM,UAAU,MAAM,SAAS,KAAK;AAAA,EACxC;AACA,QAAM,UAAU;AACpB;AACO,SAAS,kBAAkB,OAAO,WAAW,OAAO,UAAU;AApErE;AAqEI,QAAM,iBAAiB,gBAAgB,OAAO,SAAO,cAAS,YAAT,mBAAkB,UAAS,CAAC,GAAG,WAAW;AAAA,IAC3F;AAAA,IACA,KAAK,UAAU;AAAA,IACf,MAAM;AAAA,MACF,GAAG;AAAA,MACH,SAAS;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,UAAU,QAAQ,UAAU;AAAA,IACnC,cAAc,UAAU,gBAAgB,UAAU;AAAA,IAClD,QAAQ,UAAU;AAAA,EACtB;AACA,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,UAAM,MAAM,IAAI,KAAK,CAAC,cAAc,GAAG,EAAE,MAAM,gBAAgB,CAAC,GAAG,SAAS,OAAO,OAAO,OAAO,OAAO,aAAa,QAAQ,MAAM,OAAO,gBAAgB,GAAG,GAAG,MAAM,IAAI,MAAM;AAChL,QAAI,iBAAiB,QAAQ,MAAM;AAC/B,eAAS,SAAS;AAClB,eAAS,UAAU;AACnB,cAAQ,QAAQ;AAChB,aAAO,gBAAgB,GAAG;AAAA,IAC9B,CAAC;AACD,QAAI,iBAAiB,SAAS,YAAY;AACtC,aAAO,gBAAgB,GAAG;AAC1B,YAAM,OAAO;AAAA,QACT,GAAG;AAAA,QACH,OAAO;AAAA,QACP,SAAS;AAAA,MACb;AACA,YAAM,UAAU,IAAI;AACpB,eAAS,SAAS;AAClB,eAAS,UAAU,KAAK;AACxB,cAAQ,QAAQ;AAAA,IACpB,CAAC;AACD,QAAI,MAAM;AAAA,EACd,CAAC;AACL;;;ACrGO,IAAM,cAAN,MAAkB;AAAA,EACrB,YAAY,QAAQ;AAChB,SAAK,iBAAiB,OAAO,eAAe;AACxC,UAAI,CAAC,KAAK,QAAQ,WAAW;AACzB,cAAM,IAAI,MAAM,GAAG,WAAW,8BAA8B;AAAA,MAChE;AACA,YAAM,KAAK,QAAQ,UAAU;AAAA,QACzB,KAAK,WAAW;AAAA,QAChB,MAAM,WAAW;AAAA,QACjB,cAAc,WAAW,gBAAgB,WAAW,iBAAiB;AAAA,QACrE,KAAK,WAAW;AAAA,MACpB,CAAC;AAAA,IACL;AACA,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,SAAS,OAAO;AACZ,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACtB,WAAK,QAAQ,SAAS,CAAC;AAAA,IAC3B;AACA,SAAK,QAAQ,OAAO,KAAK,KAAK;AAAA,EAClC;AAAA,EACA,KAAK,SAAS,UAAU,QAAQ,SAAS,OAAO;AAC5C,UAAM,QAAQ,SAAS,OAAO,UAAU,+BAAO;AAC/C,QAAI,CAAC,OAAO;AACR;AAAA,IACJ;AACA,YAAQ,cAAc;AACtB,QAAI,MAAM,OAAO,MAAM,SAAS;AAC5B,YAAM,kBAAkB,IAAI,gBAAgB,MAAM,QAAQ,OAAO,MAAM,QAAQ,MAAM,GAAG,mBAAmB,gBAAgB,WAAW,IAAI;AAC1I,UAAI,CAAC,kBAAkB;AACnB,cAAM,IAAI,MAAM,2CAA2C;AAAA,MAC/D;AACA,uBAAiB,wBAAwB;AACzC,uBAAiB,wBAAwB;AACzC,uBAAiB,UAAU,GAAG,GAAG,gBAAgB,OAAO,gBAAgB,MAAM;AAC9E,UAAI,SAAS,iBAAiB,QAAW;AACrC,iBAAS,eAAe,MAAM,gBAAgB;AAAA,MAClD;AACA,UAAI,aAAa,SAAS,YAAY;AACtC,YAAM,MAAM,EAAE,GAAG,CAAC,MAAM,QAAQ,QAAQ,KAAK,GAAG,CAAC,MAAM,QAAQ,SAAS,IAAI,GAAG,QAAQ,MAAM,QAAQ,OAAO,UAAU;AACtH,UAAI,SAAS,YAAY,QAAW;AAChC,iBAAS,UAAU;AAAA,MACvB;AACA,UAAI,CAAC,MAAM,QAAQ;AACf;AAAA,MACJ;AACA,cAAQ,MAAM,SAAS,MAAM,QAAQ,OAAO,SAAS,MAAM,QAAQ,MAAM;AACzE,cAAQ,MAAM,gBAAgB;AAAA,QAC1B,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,2BAAiB,UAAU,MAAM,QAAQ,MAAM,MAAM,MAAM,GAAG;AAC9D,kBAAQ,UAAU,iBAAiB,IAAI,GAAG,IAAI,CAAC;AAC/C,2BAAiB,UAAU,GAAG,GAAG,gBAAgB,OAAO,gBAAgB,MAAM;AAC9E;AAAA,QACJ,KAAK;AACD,2BAAiB,UAAU,MAAM,QAAQ,MAAM,MAAM,MAAM,GAAG;AAC9D,kBAAQ,UAAU,iBAAiB,IAAI,GAAG,IAAI,CAAC;AAC/C;AAAA,QACJ,KAAK;AACD,2BAAiB,UAAU,MAAM,QAAQ,MAAM,MAAM,MAAM,GAAG;AAC9D,kBAAQ,UAAU,iBAAiB,IAAI,GAAG,IAAI,CAAC;AAC/C,2BAAiB,UAAU,GAAG,GAAG,gBAAgB,OAAO,gBAAgB,MAAM;AAC9E,cAAI,MAAM,QAAQ,iBAAiB,WAAW,GAAG;AAC7C,6BAAiB,aAAa,MAAM,QAAQ,OAAO,CAAC,EAAE,OAAO,IAAI,IAAI,MAAM,MAAM,IAAI,IAAI,MAAM,GAAG;AAAA,UACtG,OACK;AACD,6BAAiB,aAAa,MAAM,QAAQ,iBAAiB,IAAI,GAAG,IAAI,CAAC;AAAA,UAC7E;AACA;AAAA,QACJ,KAAK;AACD;AACI,kBAAM,oBAAoB,iBAAiB,aAAa,GAAG,GAAG,gBAAgB,OAAO,gBAAgB,MAAM;AAC3G,6BAAiB,UAAU,MAAM,QAAQ,MAAM,MAAM,MAAM,GAAG;AAC9D,oBAAQ,UAAU,iBAAiB,IAAI,GAAG,IAAI,CAAC;AAC/C,6BAAiB,UAAU,GAAG,GAAG,gBAAgB,OAAO,gBAAgB,MAAM;AAC9E,6BAAiB,aAAa,mBAAmB,GAAG,CAAC;AAAA,UACzD;AACA;AAAA,MACR;AACA,eAAS,WAAW,MAAM;AAC1B,UAAI,SAAS,UAAU,MAAM,WAAW;AACpC,iBAAS,WAAW,MAAM;AAC1B,YAAI,EAAE,cAAc,MAAM,QAAQ,OAAO,QAAQ;AAC7C,cAAI,EAAE,SAAS,gBAAgB,GAAG;AAC9B;AAAA,UACJ;AACA,uBAAa;AACb,2BAAiB,UAAU,GAAG,GAAG,gBAAgB,OAAO,gBAAgB,MAAM;AAAA,QAClF;AACA,iBAAS,WAAW;AAAA,MACxB;AACA,cAAQ,MAAM,MAAM,QAAQ,QAAQ,QAAQ,MAAM,QAAQ,SAAS,MAAM;AAAA,IAC7E,WACS,SAAS;AACd,YAAM,QAAQ,MAAM,OAAO,MAAM;AAAA,QAC7B,GAAG,CAAC;AAAA,QACJ,GAAG,CAAC;AAAA,MACR;AACA,cAAQ,UAAU,SAAS,IAAI,GAAG,IAAI,GAAG,SAAS,GAAI,SAAS,IAAK,KAAK;AAAA,IAC7E;AACA,YAAQ,cAAc;AAAA,EAC1B;AAAA,EACA,gBAAgB;AACZ,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAK,WAAW;AAClB,UAAM,UAAU,UAAU;AAC1B,QAAI,CAAC,QAAQ,WAAW,CAAC,KAAK,QAAQ,WAAW;AAC7C;AAAA,IACJ;AACA,eAAW,aAAa,QAAQ,SAAS;AACrC,YAAM,KAAK,QAAQ,UAAU,SAAS;AAAA,IAC1C;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,QAAI,SAAS,UAAU,WAAW,SAAS,UAAU,UAAU;AAC3D;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACtB,WAAK,QAAQ,SAAS,CAAC;AAAA,IAC3B;AACA,UAAM,YAAY,SAAS,WAAW,QAAQ,KAAK,QAAQ,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,UAAU,QAAQ,EAAE,WAAW,UAAU,GAAG;AACrI,QAAI,CAAC,OAAO;AACR,WAAK,eAAe,SAAS,EAAE,KAAK,MAAM;AACtC,aAAK,UAAU,QAAQ;AAAA,MAC3B,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,aAAa,WAAW,UAAU;AAC9B,QAAI,SAAS,UAAU,WAAW,SAAS,UAAU,UAAU;AAC3D;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACtB,WAAK,QAAQ,SAAS,CAAC;AAAA,IAC3B;AACA,UAAM,SAAS,KAAK,QAAQ,QAAQ,YAAY,SAAS,WAAW,QAAQ,SAAS,aAAa,GAAG,QAAQ,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,UAAU,QAAQ,EAAE,WAAW,UAAU,GAAG;AACvL,QAAI,CAAC,OAAO;AACR;AAAA,IACJ;AACA,UAAM,eAAe,UAAU,gBAAgB,UAAU,iBAAiB,MAAM;AAChF,QAAI,MAAM,SAAS;AACf,iBAAW,MAAM;AACb,aAAK,aAAa,WAAW,QAAQ;AAAA,MACzC,CAAC;AACD;AAAA,IACJ;AACA,KAAC,YAAY;AACT,UAAI;AACJ,UAAI,MAAM,WAAW,OAAO;AACxB,mBAAW,MAAM,kBAAkB,OAAO,WAAW,OAAO,QAAQ;AAAA,MACxE,OACK;AACD,mBAAW;AAAA,UACP;AAAA,UACA,MAAM;AAAA,UACN,SAAS,MAAM;AAAA,UACf,KAAK,MAAM;AAAA,UACX,SAAS,MAAM;AAAA,UACf,cAAc,MAAM;AAAA,UACpB,QAAQ;AAAA,UACR,OAAO,UAAU,SAAS,UAAU,SAAS,UAAU,QAAQ,UAAU,SAAS,MAAM,SAAS;AAAA,UACjG;AAAA,UACA,QAAQ,UAAU;AAAA,QACtB;AAAA,MACJ;AACA,UAAI,CAAC,SAAS,OAAO;AACjB,iBAAS,QAAQ;AAAA,MACrB;AACA,YAAM,OAAO,UAAU,QAAQ,SAAS,MAAM,QAAQ,UAAU,SAAS,SAAS,OAAO,aAAa;AAAA,QAClG,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACJ;AACA,eAAS,QAAQ,WAAW;AAC5B,eAAS,OAAO,WAAW;AAC3B,eAAS,QAAQ,WAAW;AAAA,IAChC,GAAG;AAAA,EACP;AACJ;;;ACvLO,IAAM,UAAN,MAAc;AAAA,EACjB,cAAc;AACV,SAAK,MAAM;AACX,SAAK,MAAM;AAAA,EACf;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,QAAW;AACxB,WAAK,MAAM,KAAK;AAAA,IACpB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,iBAAiB,QAAW;AACjC,WAAK,eAAe,KAAK;AAAA,IAC7B;AACA,QAAI,KAAK,QAAQ,QAAW;AACxB,WAAK,MAAM,KAAK;AAAA,IACpB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AAAA,EACJ;AACJ;;;AC3BO,IAAM,uBAAN,MAA2B;AAAA,EAC9B,YAAY,QAAQ;AAChB,SAAK,KAAK;AACV,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,YAAY;AACR,WAAO,CAAC;AAAA,EACZ;AAAA,EACA,YAAY,SAAS,QAAQ;AACzB,QAAI,CAAC,UAAU,CAAC,OAAO,SAAS;AAC5B;AAAA,IACJ;AACA,QAAI,CAAC,QAAQ,SAAS;AAClB,cAAQ,UAAU,CAAC;AAAA,IACvB;AACA,UAAM,iBAAiB,QAAQ;AAC/B,eAAW,QAAQ,OAAO,SAAS;AAC/B,YAAM,WAAW,eAAe,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,QAAQ,EAAE,QAAQ,KAAK,GAAG;AACtF,UAAI,UAAU;AACV,iBAAS,KAAK,IAAI;AAAA,MACtB,OACK;AACD,cAAM,UAAU,IAAI,QAAQ;AAC5B,gBAAQ,KAAK,IAAI;AACjB,uBAAe,KAAK,OAAO;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,cAAc;AACV,WAAO;AAAA,EACX;AACJ;;;AC5BA,SAAS,qBAAqB,QAAQ;AAClC,MAAI,OAAO,WAAW;AAClB;AAAA,EACJ;AACA,SAAO,YAAY,OAAO,SAAS;AAC/B,QAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK;AACzB,YAAM,IAAI,MAAM,GAAG,WAAW,2BAA2B;AAAA,IAC7D;AACA,QAAI,CAAC,OAAO,QAAQ;AAChB,aAAO,SAAS,CAAC;AAAA,IACrB;AACA,QAAI,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,QAAQ,EAAE,WAAW,KAAK,GAAG,GAAG;AAC1E;AAAA,IACJ;AACA,QAAI;AACA,YAAM,QAAQ;AAAA,QACV,KAAK,KAAK,OAAO;AAAA,QACjB,MAAM,KAAK,QAAQ,KAAK;AAAA,QACxB,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK,IAAI,UAAU,KAAK,IAAI,SAAS,CAAC;AAAA,QAC5C,OAAO;AAAA,QACP,SAAS;AAAA,QACT,cAAc,KAAK;AAAA,QACnB,OAAO,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS;AAAA,MAClE;AACA,aAAO,OAAO,KAAK,KAAK;AACxB,YAAM,YAAY,KAAK,MAAM,eAAe,KAAK,eAAe,mBAAmB;AACnF,YAAM,UAAU,KAAK;AAAA,IACzB,QACM;AACF,YAAM,IAAI,MAAM,GAAG,WAAW,IAAI,KAAK,QAAQ,KAAK,GAAG,YAAY;AAAA,IACvE;AAAA,EACJ;AACJ;AACA,eAAsB,eAAe,QAAQ,UAAU,MAAM;AACzD,uBAAqB,MAAM;AAC3B,QAAM,YAAY,IAAI,qBAAqB,MAAM;AACjD,QAAM,OAAO,UAAU,WAAW,OAAO;AACzC,QAAM,OAAO,SAAS,CAAC,SAAS,QAAQ,GAAG,IAAI,YAAY,MAAM,GAAG,OAAO;AAC/E;;;AC1CO,IAAM,YAAN,cAAwB,gBAAgB;AAAA,EAC3C,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,KAAK,IAAI;AACf,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;ACdO,IAAM,eAAN,cAA2B,gBAAgB;AAAA,EAC9C,cAAc;AACV,UAAM;AACN,SAAK,OAAO,eAAe;AAC3B,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,KAAK,IAAI;AACf,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;ACdO,IAAM,OAAN,MAAW;AAAA,EACd,cAAc;AACV,SAAK,QAAQ;AACb,SAAK,QAAQ,IAAI,UAAU;AAC3B,SAAK,WAAW,IAAI,aAAa;AAAA,EACrC;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,SAAK,MAAM,KAAK,KAAK,KAAK;AAC1B,SAAK,SAAS,KAAK,KAAK,QAAQ;AAAA,EACpC;AACJ;;;AChBO,IAAM,cAAN,MAAkB;AAAA,EACrB,YAAY,WAAW;AACnB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,KAAK,UAAU;AACX,UAAM,YAAY,KAAK,WAAW,mBAAmB,SAAS,SAAS,cAAc,iBAAiB;AACtG,QAAI,CAAC,aAAa;AACd;AAAA,IACJ;AACA,aAAS,OAAO;AAAA,MACZ,OAAO,UAAU,OAAO,eAChB,cAAc,YAAY,MAAM,KAAK,KAAK,YAAY,MAAM,OAAO,IAAI,UAAU,KACjF,UAAU,OAAO,eACjB,MACF;AAAA,MACN,WAAW;AAAA,MACX,UAAU,UAAU,OAAO,eACnB,cAAc,YAAY,SAAS,KAAK,KAAK,YAAY,SAAS,OAAO,IAAI,UAAU,KACvF,UAAU,OAAO,eACjB,MACF;AAAA,MACN,MAAM;AAAA,MACN,OAAO,YAAY;AAAA,IACvB;AACA,QAAI,SAAS,KAAK,YAAY,GAAG;AAC7B,eAAS,KAAK,WAAW;AAAA,IAC7B;AACA,QAAI,SAAS,KAAK,SAAS,GAAG;AAC1B,eAAS,KAAK,QAAQ;AAAA,IAC1B;AACA,QAAI,SAAS,MAAM;AACf,eAAS,WAAW,SAAS,KAAK,QAAQ;AAAA,IAC9C;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,WAAO,CAAC,SAAS;AAAA,EACrB;AAAA,EACA,YAAY,YAAY,SAAS;AAC7B,QAAI,CAAC,QAAQ,MAAM;AACf,cAAQ,OAAO,IAAI,KAAK;AAAA,IAC5B;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,KAAK,KAAK,iCAAQ,IAAI;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,OAAO,UAAU,OAAO;AACpB,QAAI,CAAC,KAAK,UAAU,QAAQ,KAAK,CAAC,SAAS,MAAM;AAC7C;AAAA,IACJ;AACA,UAAM,OAAO,SAAS;AACtB,QAAI,cAAc;AAClB,QAAI,SAAS,UAAU;AACnB,WAAK,aAAa,MAAM;AACxB,UAAI,KAAK,aAAa,SAAS,KAAK,OAAO;AACvC,sBAAc;AACd,iBAAS,WAAW;AACpB,aAAK,YAAY;AACjB,aAAK,OAAO;AAAA,MAChB,OACK;AACD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,IAAI;AACtB;AAAA,IACJ;AACA,QAAI,SAAS,UAAU;AACnB;AAAA,IACJ;AACA,QAAI,aAAa;AACb,WAAK,OAAO;AAAA,IAChB,OACK;AACD,WAAK,QAAQ,MAAM;AAAA,IACvB;AACA,QAAI,KAAK,OAAO,KAAK,UAAU;AAC3B;AAAA,IACJ;AACA,SAAK,OAAO;AACZ,QAAI,SAAS,KAAK,QAAQ,GAAG;AACzB,eAAS,KAAK;AAAA,IAClB;AACA,QAAI,SAAS,KAAK,UAAU,GAAG;AAC3B,eAAS,QAAQ;AACjB;AAAA,IACJ;AACA,UAAM,aAAa,KAAK,UAAU,OAAO,MAAM,aAAa,cAAc,GAAG,WAAW,KAAK,GAAG,cAAc,cAAc,GAAG,WAAW,KAAK;AAC/I,aAAS,SAAS,IAAI,cAAc,UAAU;AAC9C,aAAS,SAAS,IAAI,cAAc,WAAW;AAC/C,aAAS,WAAW;AACpB,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,aAAS,MAAM;AACf,UAAM,cAAc,SAAS,QAAQ;AACrC,QAAI,aAAa;AACb,WAAK,QAAQ,cAAc,YAAY,MAAM,KAAK,IAAI;AACtD,WAAK,WAAW,cAAc,YAAY,SAAS,KAAK,IAAI;AAAA,IAChE;AAAA,EACJ;AACJ;;;ACpGA,eAAsB,gBAAgB,QAAQ,UAAU,MAAM;AAC1D,QAAM,OAAO,mBAAmB,QAAQ,CAAC,cAAc,IAAI,YAAY,SAAS,GAAG,OAAO;AAC9F;;;ACHO,IAAM,aAAN,MAAiB;AAAA,EACpB,KAAK,SAAS,UAAU,QAAQ;AAC5B,UAAM,YAAY,SAAS;AAC3B,YAAQ,OAAO,CAAC,SAAS,GAAG,CAAC;AAC7B,YAAQ,OAAO,SAAS,GAAG,CAAC;AAC5B,YAAQ,WAAU,uCAAW,QAAO;AAAA,EACxC;AAAA,EACA,gBAAgB;AACZ,WAAO;AAAA,EACX;AACJ;;;ACTA,eAAsB,cAAc,QAAQ,UAAU,MAAM;AACxD,QAAM,OAAO,SAAS,QAAQ,IAAI,WAAW,GAAG,OAAO;AAC3D;;;ACFO,IAAM,gBAAN,MAAoB;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA,UAAU,UAAU;AAChB,WAAQ,CAAC,MAAM,KACX,CAAC,SAAS,aACV,SAAS,UAAU,cAAc,cAAc,OAAO,QAAQ,SAAS;AAAA,EAC/E;AAAA,EACA,KAAK,UAAU;AACX,UAAM,YAAY,SAAS,WAAW,UAAU,UAAU,eAAe,kBAAkB,QAAQ,cAAc,OAAO,QAAQ;AAChI,QAAI,MAAM,KAAK,CAAC,gBAAgB,QAAQ;AACpC;AAAA,IACJ;AACA,UAAM,gBAAgB,gBAAgB,OAAO,WAAW,UAAU,cAAc,MAAM;AACtF,QAAI,CAAC,UAAU;AACX;AAAA,IACJ;AACA,UAAM,aAAa,UAAU,OAAO,MAAM,eAAe;AAAA,MACrD,GAAG,WAAW,QAAQ;AAAA,MACtB,GAAG,WAAW,SAAS;AAAA,IAC3B,GAAG,iBAAiB,gBAAgB,QAAQ,SAAS,SAAS,UAAU,IAAI,eAAe,iBAAiB;AAAA,MACxG,IAAI,SAAS,IAAI,aAAa,KAAK;AAAA,MACnC,IAAI,SAAS,IAAI,aAAa,KAAK;AAAA,IACvC,GAAG,EAAE,OAAO,IAAI;AAChB,WAAO,MAAM,eAAe,IAAI,OAAO,KAAK;AAC5C,WAAO,MAAM,eAAe,IAAI,OAAO,KAAK;AAAA,EAChD;AACJ;;;AC3BA,eAAsB,kBAAkB,QAAQ,UAAU,MAAM;AAC5D,QAAM,OAAO,SAAS,YAAY,MAAM,IAAI,cAAc,GAAG,OAAO;AACxE;;;ACFO,IAAMC,aAAN,cAAwB,wBAAwB;AAAA,EACnD,YAAY,WAAW;AACnB,UAAM,SAAS;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,MAAM,SAAS,IAAI;AACf,UAAM,YAAY,KAAK,WAAW,WAAW,GAAG,OAAO,mBAAmB,UAAU,OAAO,iBAAiB,OAAO,GAAG,YAAY,GAAG,QAAQ,UAAU,UAAU,SAAS,YAAY,MAAM,QAAQ;AACpM,eAAW,MAAM,OAAO;AACpB,UAAI,OAAO,MAAM,CAAC,GAAG,QAAQ,KAAK,QAAQ,UAAU,GAAG,aAAa,GAAG,UAAU;AAC7E;AAAA,MACJ;AACA,YAAM,OAAO,GAAG,YAAY,GAAG,EAAE,IAAI,GAAG,IAAI,aAAa,MAAM,IAAI,GAAG,SAAS,GAAG,QAAQ,KAAK,QAAQ,QAAQ,KAAK,MAAM,OAAO,IAAI,MAAO,KAAK,MAAM,OAAO,IAAI,MAAO,WAAW,GAAG,KAAK,QAAQ,GAAG,KAAK,OAAO,WAAW,IAAI;AAClO,SAAG,SAAS,KAAK,KAAK;AACtB,SAAG,SAAS,KAAK,KAAK;AACtB,SAAG,SAAS,KAAK,KAAK;AACtB,SAAG,SAAS,KAAK,KAAK;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,WAAO,SAAS,QAAQ,KAAK,QAAQ;AAAA,EACzC;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;AC1BA,eAAsB,gCAAgC,QAAQ,UAAU,MAAM;AAC1E,QAAM,OAAO,cAAc,oBAAoB,CAAC,cAAc,IAAIC,WAAU,SAAS,GAAG,OAAO;AACnG;;;ACFA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,OAAO,YAAY;AACrD,QAAM,SAAS,MAAO,GAAG,QAAQ,WAAW,OAAO,QAAQ,MAAM,SAAU,IAAI,GAAG,EAAE;AACpF,KAAG,KAAK,SAAS,SAAS;AAC1B,KAAG,KAAK,SAAS;AACjB,MAAI,MAAM,YAAY;AAClB,OAAG,KAAK,QAAQ;AAChB,OAAG,QAAQ;AAAA,EACf;AACJ;AACO,SAAS,OAAO,IAAI,IAAI,OAAO,YAAY;AAC9C,QAAM,KAAK,GAAG,UAAU,GAAG,KAAK,GAAG,UAAU;AAC7C,MAAI,OAAO,UAAa,OAAO,QAAW;AACtC,OAAG,QAAQ;AAAA,EACf,WACS,OAAO,UAAa,OAAO,QAAW;AAC3C,OAAG,QAAQ;AAAA,EACf,WACS,OAAO,UAAa,OAAO,QAAW;AAC3C,QAAI,MAAM,IAAI;AACV,mBAAa,IAAI,IAAI,IAAI,IAAI,OAAO,UAAU;AAAA,IAClD,OACK;AACD,mBAAa,IAAI,IAAI,IAAI,IAAI,OAAO,UAAU;AAAA,IAClD;AAAA,EACJ;AACJ;;;ACzBA,IAAM,iBAAiB,CAAC,MAAM;AAC1B,MAAI,EAAE,sBAAsB,QAAW;AACnC,MAAE,oBAAoB,cAAc,EAAE,QAAQ,WAAW,QAAQ;AAAA,EACrE;AACA,MAAI,EAAE,SAAS,SAAS,EAAE,mBAAmB;AACzC,MAAE,SAAS,SAAS,EAAE;AAAA,EAC1B;AACJ;AACO,SAAS,OAAO,IAAI,IAAI;AAC3B,eAAa,6BAA6B,EAAE,GAAG,6BAA6B,EAAE,CAAC;AAC/E,iBAAe,EAAE;AACjB,iBAAe,EAAE;AACrB;;;ACZO,SAAS,QAAQ,IAAI,IAAI;AAC5B,MAAI,CAAC,GAAG,eAAe,CAAC,GAAG,aAAa;AACpC,WAAO,IAAI,EAAE;AAAA,EACjB;AACA,MAAI,GAAG,UAAU,MAAM,UAAa,GAAG,UAAU,MAAM,QAAW;AAC9D,OAAG,QAAQ;AAAA,EACf,WACS,GAAG,UAAU,MAAM,UAAa,GAAG,UAAU,MAAM,QAAW;AACnE,OAAG,QAAQ;AAAA,EACf,WACS,GAAG,UAAU,MAAM,UAAa,GAAG,UAAU,MAAM,QAAW;AACnE,UAAM,UAAU,GAAG,UAAU,KAAK,GAAG,UAAU,IAAI,KAAK;AACxD,YAAQ,QAAQ;AAAA,EACpB;AACJ;;;ACZO,SAAS,iBAAiB,IAAI,IAAI,OAAO,YAAY;AACxD,UAAQ,GAAG,QAAQ,WAAW,MAAM;AAAA,IAChC,KAAK,UAAU;AACX,aAAO,IAAI,IAAI,OAAO,UAAU;AAChC;AAAA,IACJ;AAAA,IACA,KAAK,UAAU;AACX,aAAO,IAAI,EAAE;AACb;AAAA,IACJ;AAAA,IACA,KAAK,WAAW;AACZ,cAAQ,IAAI,EAAE;AACd;AAAA,IACJ;AAAA,EACJ;AACJ;;;AChBO,IAAM,WAAN,cAAuB,wBAAwB;AAAA,EAClD,YAAY,WAAW;AACnB,UAAM,SAAS;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,MAAM,SAAS,IAAI,OAAO;AACtB,QAAI,GAAG,aAAa,GAAG,UAAU;AAC7B;AAAA,IACJ;AACA,UAAM,YAAY,KAAK,WAAW,OAAO,GAAG,YAAY,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ,UAAU,UAAU,SAAS,YAAY,MAAM,UAAU,CAAC;AACvJ,eAAW,MAAM,OAAO;AACpB,UAAI,OAAO,MACP,CAAC,GAAG,QAAQ,WAAW,UACvB,GAAG,QAAQ,WAAW,SAAS,GAAG,QAAQ,WAAW,QACrD,GAAG,aACH,GAAG,UAAU;AACb;AAAA,MACJ;AACA,YAAM,OAAO,GAAG,YAAY,GAAG,UAAU,GAAG,UAAU;AACtD,UAAI,KAAK,IAAI,KAAK,MAAM,KAAK,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,UAAU,SAAS;AACvE;AAAA,MACJ;AACA,YAAM,OAAO,YAAY,MAAM,IAAI,GAAG,QAAQ,UAAU;AACxD,UAAI,OAAO,OAAO;AACd;AAAA,MACJ;AACA,uBAAiB,IAAI,IAAI,OAAO,UAAU,OAAO,UAAU;AAAA,IAC/D;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,WAAO,SAAS,QAAQ,WAAW;AAAA,EACvC;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;ACtCA,eAAsB,mCAAmC,QAAQ,UAAU,MAAM;AAC7E,QAAM,OAAO,cAAc,uBAAuB,CAAC,cAAc,IAAI,SAAS,SAAS,GAAG,OAAO;AACrG;;;ACFO,IAAM,aAAN,cAAyB,OAAO;AAAA,EACnC,YAAY,GAAG,GAAG,QAAQ,YAAY;AAClC,UAAM,GAAG,GAAG,MAAM;AAClB,SAAK,aAAa;AAClB,SAAK,aAAa,EAAE,GAAG,WAAW;AAAA,EACtC;AAAA,EACA,SAAS,OAAO;AACZ,UAAM,EAAE,OAAO,OAAO,IAAI,KAAK;AAC/B,UAAM,EAAE,GAAG,EAAE,IAAI;AACjB,WAAQ,MAAM,SAAS,KAAK,KACxB,MAAM,SAAS,EAAE,GAAG,IAAI,OAAO,EAAE,CAAC,KAClC,MAAM,SAAS,EAAE,GAAG,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,KAC9C,MAAM,SAAS,EAAE,GAAG,GAAG,IAAI,OAAO,CAAC;AAAA,EAC3C;AAAA,EACA,WAAW,OAAO;AACd,QAAI,MAAM,WAAW,KAAK,GAAG;AACzB,aAAO;AAAA,IACX;AACA,UAAM,OAAO,OAAO,SAAS,OAAO,SAAS;AAAA,MACzC,GAAG,MAAM,SAAS,IAAI,KAAK,WAAW;AAAA,MACtC,GAAG,MAAM,SAAS,IAAI,KAAK,WAAW;AAAA,IAC1C;AACA,QAAI,OAAO,WAAW,QAAW;AAC7B,YAAM,eAAe,IAAI,OAAO,OAAO,GAAG,OAAO,GAAG,OAAO,SAAS,CAAC;AACrE,aAAO,MAAM,WAAW,YAAY;AAAA,IACxC,WACS,KAAK,SAAS,QAAW;AAC9B,YAAM,SAAS,IAAI,UAAU,OAAO,GAAG,OAAO,GAAG,KAAK,KAAK,QAAQ,GAAG,KAAK,KAAK,SAAS,CAAC;AAC1F,aAAO,MAAM,WAAW,MAAM;AAAA,IAClC;AACA,WAAO;AAAA,EACX;AACJ;;;AChCO,IAAM,cAAN,MAAkB;AAAA,EACrB,cAAc;AACV,SAAK,OAAO;AACZ,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,MAAM,QAAQ;AACnB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,SAAK,QAAQ,aAAa,OAAO,KAAK,OAAO,KAAK,KAAK;AACvD,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AACJ;;;ACnBO,IAAM,gBAAN,MAAoB;AAAA,EACvB,cAAc;AACV,SAAK,SAAS;AACd,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,aAAa,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,IAC3D;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,KAAK;AAAA,IAC1B;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACpBO,IAAM,QAAN,MAAY;AAAA,EACf,cAAc;AACV,SAAK,QAAQ;AACb,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,MAAM,QAAQ;AACnB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,SAAS,IAAI,YAAY;AAC9B,SAAK,YAAY,IAAI,cAAc;AACnC,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,OAAO,QAAW;AACvB,WAAK,KAAK,KAAK;AAAA,IACnB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,SAAK,QAAQ,aAAa,OAAO,KAAK,OAAO,KAAK,KAAK;AACvD,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,KAAK;AAAA,IAC1B;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AACA,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,SAAK,UAAU,KAAK,KAAK,SAAS;AAClC,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;AClDA,SAAS,gBAAgB,MAAM,MAAM,aAAa,YAAY,MAAM;AAChE,QAAM,EAAE,IAAI,IAAI,SAAS,IAAI,aAAa,MAAM,IAAI;AACpD,MAAI,CAAC,QAAQ,YAAY,aAAa;AAClC,WAAO;AAAA,EACX;AACA,QAAM,WAAW;AAAA,IACb,GAAG,KAAK,IAAI,EAAE;AAAA,IACd,GAAG,KAAK,IAAI,EAAE;AAAA,EAClB,GAAG,gBAAgB;AAAA,IACf,GAAG,KAAK,IAAI,SAAS,GAAG,WAAW,QAAQ,SAAS,CAAC;AAAA,IACrD,GAAG,KAAK,IAAI,SAAS,GAAG,WAAW,SAAS,SAAS,CAAC;AAAA,EAC1D;AACA,SAAO,KAAK,KAAK,cAAc,KAAK,IAAI,cAAc,KAAK,CAAC;AAChE;AACO,IAAM,SAAN,cAAqB,wBAAwB;AAAA,EAChD,YAAY,WAAW;AACnB,UAAM,SAAS;AACf,SAAK,YAAY,CAAC,OAAO;AACrB,UAAI,CAAC,GAAG,QAAQ,OAAO;AACnB;AAAA,MACJ;AACA,YAAMC,aAAY,KAAK,eAAe,eAAe,GAAG,QAAQ;AAChE,UAAI,YAAY,aAAa,OAAO,SAC9BA,WAAU,UAAU,aACpBA,WAAU,UAAU,YAAY,IAAI,aAAa,EAAE;AACzD,UAAI,WAAW;AACX;AAAA,MACJ;AACA,YAAM,WAAW,aAAa;AAC9B,kBAAY,mBAAmB,UAAU,aAAa,OAAO,aAAa,OAAO;AACjF,UAAI,aAAa,OAAO,QAAW;AAC/B,QAAAA,WAAU,UAAU,aAAa;AAAA,MACrC,OACK;AACD,QAAAA,WAAU,UAAU,YAAY,IAAI,aAAa,IAAI,SAAS;AAAA,MAClE;AAAA,IACJ;AACA,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,OAAO;AACH,SAAK,cAAc,UAAU,aAAa;AAC1C,SAAK,cAAc,UAAU,cAAc,oBAAI,IAAI;AAAA,EACvD;AAAA,EACA,MAAM,SAAS,IAAI;AACf,QAAI,CAAC,GAAG,QAAQ,OAAO;AACnB;AAAA,IACJ;AACA,OAAG,QAAQ,CAAC;AACZ,UAAM,OAAO,GAAG,YAAY,GAAG,YAAY,KAAK,WAAW,aAAa,UAAU,OAAO;AACzF,QAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,WAAW,SAAS,KAAK,IAAI,WAAW,QAAQ;AACrF;AAAA,IACJ;AACA,UAAM,WAAW,GAAG,QAAQ,OAAO,aAAa,SAAS,SAAS,cAAc,GAAG,OAAO,iBAAiB,GAAG,OAAO,SAAS,MAAM,QAAQ,OACtI,IAAI,WAAW,KAAK,GAAG,KAAK,GAAG,aAAa,UAAU,IACtD,IAAI,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,GAAG,QAAQ,UAAU,UAAU,SAAS,MAAM,KAAK;AAC/F,eAAW,MAAM,OAAO;AACpB,YAAM,WAAW,GAAG,QAAQ;AAC5B,UAAI,OAAO,MACP,EAAC,qCAAU,WACX,SAAS,OAAO,SAAS,MACzB,GAAG,YACH,GAAG,aACH,CAAC,GAAG,SACJ,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,gBAAgB,EAAE,KACzC,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,gBAAgB,EAAE,GAAG;AAC5C;AAAA,MACJ;AACA,YAAM,OAAO,GAAG,YAAY;AAC5B,UAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,WAAW,SAAS,KAAK,IAAI,WAAW,QAAQ;AACrF;AAAA,MACJ;AACA,YAAM,WAAW,gBAAgB,MAAM,MAAM,aAAa,YAAY,QAAQ,SAAS,IAAI;AAC3F,UAAI,WAAW,aAAa;AACxB;AAAA,MACJ;AACA,YAAM,eAAe,IAAI,WAAW,eAAe;AACnD,WAAK,UAAU,EAAE;AACjB,SAAG,MAAM,KAAK;AAAA,QACV,aAAa;AAAA,QACb,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAxFxB;AAyFQ,WAAO,CAAC,GAAC,cAAS,QAAQ,UAAjB,mBAAwB;AAAA,EACrC;AAAA,EACA,qBAAqB,YAAY,SAAS;AACtC,QAAI,CAAC,QAAQ,OAAO;AAChB,cAAQ,QAAQ,IAAI,MAAM;AAAA,IAC9B;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,MAAM,MAAK,iCAAQ,WAAS,iCAAQ,gBAAc,iCAAQ,YAAW;AAAA,IACjF;AAAA,EACJ;AAAA,EACA,QAAQ;AAAA,EACR;AACJ;;;ACpGA,eAAsB,qBAAqB,QAAQ,UAAU,MAAM;AAC/D,QAAM,OAAO,cAAc,kBAAkB,CAAC,cAAc,IAAI,OAAO,SAAS,GAAG,OAAO;AAC9F;;;ACFO,SAAS,aAAa,QAAQ;AACjC,MAAI,QAAQ;AACZ,QAAM,EAAE,OAAO,KAAK,aAAa,SAAS,YAAY,OAAO,gBAAgB,WAAW,SAAS,MAAM,IAAI;AAC3G,MAAI,YAAY,OAAO,GAAG,KAAK,aAAa;AACxC,aAAS,SAAS,OAAO,GAAG;AAC5B,YAAQ;AAAA,EACZ,WACS,MAAM,MAAM;AACjB,QAAI;AACJ,QAAI;AACJ,UAAM,QAAQ;AAAA,MACV,GAAG,IAAI,IAAI,WAAW;AAAA,MACtB,GAAG,IAAI;AAAA,IACX;AACA,UAAM,KAAK,aAAa,OAAO,KAAK;AACpC,QAAI,GAAG,YAAY,aAAa;AAC5B,YAAM,KAAK,MAAM,IAAK,GAAG,KAAK,GAAG,KAAM,MAAM;AAC7C,YAAM,EAAE,GAAG,GAAG,GAAG,GAAG;AACpB,YAAM,EAAE,GAAG,WAAW,OAAO,GAAG,GAAG;AAAA,IACvC,OACK;AACD,YAAM,QAAQ;AAAA,QACV,GAAG,IAAI;AAAA,QACP,GAAG,IAAI,IAAI,WAAW;AAAA,MAC1B;AACA,YAAM,KAAK,aAAa,OAAO,KAAK;AACpC,UAAI,GAAG,YAAY,aAAa;AAC5B,cAAM,KAAK,MAAM,IAAK,GAAG,KAAK,GAAG,KAAM,MAAM;AAC7C,cAAM,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG;AAC7B,cAAM,EAAE,GAAG,IAAI,GAAG,EAAE;AACpB,cAAM,EAAE,GAAG,IAAI,GAAG,WAAW,OAAO;AAAA,MACxC,OACK;AACD,cAAM,QAAQ;AAAA,UACV,GAAG,IAAI,IAAI,WAAW;AAAA,UACtB,GAAG,IAAI,IAAI,WAAW;AAAA,QAC1B;AACA,cAAM,KAAK,aAAa,OAAO,KAAK;AACpC,YAAI,GAAG,YAAY,aAAa;AAC5B,gBAAM,KAAK,MAAM,IAAK,GAAG,KAAK,GAAG,KAAM,MAAM;AAC7C,gBAAM,KAAK,CAAC,MAAM,GAAG,KAAK,GAAG;AAC7B,gBAAM,EAAE,GAAG,IAAI,GAAG,GAAG;AACrB,gBAAM,EAAE,GAAG,IAAI,IAAI,WAAW,OAAO,GAAG,IAAI,IAAI,WAAW,OAAO;AAAA,QACtE;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO,KAAK;AACZ,eAAS,SAAS,OAAO,GAAG;AAC5B,eAAS,SAAS,KAAK,GAAG;AAC1B,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,MAAI,CAAC,OAAO;AACR;AAAA,EACJ;AACA,UAAQ,YAAY;AACpB,MAAI,eAAe,QAAQ;AACvB,YAAQ,2BAA2B,eAAe;AAAA,EACtD;AACA,UAAQ,cAAc,gBAAgB,WAAW,OAAO;AACxD,QAAM,EAAE,OAAO,IAAI;AACnB,MAAI,OAAO,QAAQ;AACf,UAAM,cAAc,gBAAgB,OAAO,KAAK;AAChD,QAAI,aAAa;AACb,cAAQ,aAAa,OAAO;AAC5B,cAAQ,cAAc,gBAAgB,WAAW;AAAA,IACrD;AAAA,EACJ;AACA,UAAQ,OAAO;AACnB;AACO,SAAS,iBAAiB,QAAQ;AACrC,QAAM,EAAE,SAAS,MAAM,MAAM,MAAM,gBAAgB,eAAe,gBAAgB,IAAI;AACtF,eAAa,SAAS,MAAM,MAAM,IAAI;AACtC,MAAI,eAAe,QAAQ;AACvB,YAAQ,2BAA2B,eAAe;AAAA,EACtD;AACA,UAAQ,YAAY,gBAAgB,eAAe,eAAe;AAClE,UAAQ,KAAK;AACjB;AACO,SAAS,WAAW,KAAK;AAC5B,MAAI,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACxB,SAAO,IAAI,KAAK,GAAG;AACvB;AACO,SAAS,iBAAiB,WAAW,YAAY;AACpD,QAAM,MAAM,WAAW,UAAU,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;AACjD,MAAI,MAAM,WAAW,IAAI,GAAG;AAC5B,MAAI,QAAQ,QAAW;AACnB,UAAM,UAAU;AAChB,eAAW,IAAI,KAAK,GAAG;AAAA,EAC3B;AACA,SAAO;AACX;;;AC1FO,IAAM,eAAN,MAAmB;AAAA,EACtB,YAAY,WAAW;AACnB,SAAK,YAAY;AACjB,SAAK,gBAAgB,CAAC,IAAI,SAAS;AAC/B,YAAM,iBAAiB,GAAG,QAAQ;AAClC,UAAI,EAAC,iDAAgB,SAAQ;AACzB;AAAA,MACJ;AACA,YAAMC,aAAY,KAAK,WAAW,UAAUA,WAAU,eAAe,KAAK,KAAK,aAAa,OAAO,GAAG,YAAY,GAAG,OAAO,GAAG,YAAY;AAC3I,UAAI,UAAU,KAAK;AACnB,MAAAA,WAAU,OAAO,KAAK,CAAC,QAAQ;AAZ3C;AAagB,YAAI;AACJ,cAAM,WAAU,QAAG,QAAQ,YAAX,mBAAoB;AACpC,YAAI,mCAAS,QAAQ;AACjB,gBAAM,cAAc,QAAQ,WAAW,aAAa,gBAAgB,QAAQ,KAAK,GAAG,YAAY,UAAU,IAAI;AAC9G,cAAI,aAAa,YAAY;AACzB,wBAAY;AACZ,sBAAU,cAAc,QAAQ,OAAO;AAAA,UAC3C;AAAA,QACJ;AACA,YAAI,CAAC,WAAW;AACZ,gBAAM,YAAY,eAAe,OAAO,SAClCA,WAAU,UAAU,YAAY,IAAI,eAAe,EAAE,IACrDA,WAAU,UAAU;AAC1B,sBAAY,aAAa,IAAI,IAAI,SAAS;AAAA,QAC9C;AACA,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,cAAM,QAAQ,GAAG,OAAO,cAAc,GAAG,cAAc,GAAG,OAAO,iBAAiB,GAAG,EAAE,eAAe,IAAI;AAC1G,qBAAa;AAAA,UACT,SAAS;AAAA,UACT;AAAA,UACA,OAAO;AAAA,UACP,KAAK;AAAA,UACL;AAAA,UACA,YAAYA,WAAU,OAAO;AAAA,UAC7B,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,SAAK,oBAAoB,CAAC,IAAI,OAAO,UAAU;AAC3C,YAAM,eAAe,GAAG,QAAQ;AAChC,UAAI,EAAC,6CAAc,SAAQ;AACvB;AAAA,MACJ;AACA,YAAM,kBAAkB,aAAa;AACrC,UAAI,CAAC,gBAAgB,QAAQ;AACzB;AAAA,MACJ;AACA,YAAMA,aAAY,KAAK,WAAW,UAAUA,WAAU,eAAe,KAAK,MAAM,aAAa,KAAK,MAAM,aAAa,kBAAkB,gBAAgB,YAAY,MAAM,UAAU,MAAM,WAAW;AACpM,UAAI,mBAAmB,GAAG;AACtB;AAAA,MACJ;AACA,MAAAA,WAAU,OAAO,KAAK,CAAC,QAAQ;AAC3B,cAAM,OAAO,GAAG,YAAY,GAAG,OAAO,GAAG,YAAY,GAAG,OAAO,GAAG,YAAY,GAAG,gBAAgB,GAAG,OAAO,iBAAiB;AAC5H,YAAI,YAAY,MAAM,IAAI,IAAI,iBAC1B,YAAY,MAAM,IAAI,IAAI,iBAC1B,YAAY,MAAM,IAAI,IAAI,eAAe;AACzC;AAAA,QACJ;AACA,YAAI,gBAAgB,gBAAgB,gBAAgB,KAAK;AACzD,YAAI,CAAC,eAAe;AAChB,gBAAM,YAAY,aAAa,OAAO,SAChCA,WAAU,UAAU,YAAY,IAAI,aAAa,EAAE,IACnDA,WAAU,UAAU;AAC1B,0BAAgB,aAAa,IAAI,IAAI,SAAS;AAAA,QAClD;AACA,YAAI,CAAC,eAAe;AAChB;AAAA,QACJ;AACA,yBAAiB;AAAA,UACb,SAAS;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA,gBAAgB,QAAQ;AAAA,UACxB;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,SAAK,iBAAiB,CAAC,SAAS,IAAI,MAAM,YAAY;AAvF9D;AAwFY,YAAM,KAAK,KAAK;AAChB,UAAI,IAAE,aAAQ,UAAR,mBAAe,UAAU,aAAU,QAAG,QAAQ,UAAX,mBAAkB,UAAU,UAAS;AAC1E;AAAA,MACJ;AACA,YAAM,YAAW,QAAG,UAAH,mBAAU,OAAO,CAAC,MAAM;AACrC,cAAM,WAAW,KAAK,kBAAkB,IAAI,EAAE,WAAW;AACzD,eAAQ,GAAG,QAAQ,SACf,YAAY,GAAG,QAAQ,MAAM,aAC7B,QAAQ,UAAU,CAAC,MAAM,EAAE,gBAAgB,EAAE,WAAW,KAAK;AAAA,MACrE;AACA,UAAI,EAAC,qCAAU,SAAQ;AACnB;AAAA,MACJ;AACA,iBAAW,UAAU,UAAU;AAC3B,cAAM,KAAK,OAAO,aAAa,eAAe,KAAK,sBAAsB,IAAI,IAAI,EAAE;AACnF,YAAI,eAAe,QAAQ,MAAM,UAAU,WAAW;AAClD;AAAA,QACJ;AACA,aAAK,kBAAkB,IAAI,MAAM,MAAM;AAAA,MAC3C;AAAA,IACJ;AACA,SAAK,oBAAoB,CAAC,IAAI,OAAO;AACjC,aAAO,iBAAiB,CAAC,IAAI,EAAE,GAAG,KAAK,OAAO,KAAK;AAAA,IACvD;AACA,SAAK,wBAAwB,CAAC,IAAI,IAAI,OAAO;AACzC,aAAO,iBAAiB,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,OAAO,SAAS;AAAA,IAC/D;AACA,SAAK,SAAS;AAAA,MACV,OAAO,oBAAI,IAAI;AAAA,MACf,WAAW,oBAAI,IAAI;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,aAAa,SAAS,UAAU;AAC5B,UAAM,EAAE,OAAO,QAAQ,IAAI;AAC3B,QAAI,CAAC,SAAS,MAAM,UAAU,GAAG;AAC7B;AAAA,IACJ;AACA,UAAM,UAAU,MAAM,OAAO,CAAC,MAAM,QAAQ,SAAS,KAAK,kBAAkB,UAAU,EAAE,WAAW,KAAK,QAAQ,MAAM,SAAS;AAC/H,eAAW,QAAQ,SAAS;AACxB,WAAK,eAAe,SAAS,UAAU,MAAM,OAAO;AACpD,UAAI,KAAK,UAAU,MAAM,SAAS,OAAO,cAAc,KAAK,GAAG;AAC3D,aAAK,cAAc,UAAU,IAAI;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM,OAAO;AACT,SAAK,OAAO,QAAQ,oBAAI,IAAI;AAC5B,SAAK,OAAO,YAAY,oBAAI,IAAI;AAAA,EACpC;AAAA,EACA,gBAAgB,UAAU;AACtB,aAAS,QAAQ,CAAC;AAClB,QAAI,CAAC,SAAS,QAAQ,OAAO;AACzB;AAAA,IACJ;AACA,UAAM,QAAQ,KAAK,UAAU,OAAO,YAAY,EAAE,OAAO,IAAI,UAAU,EAAE,UAAU,MAAM,IAAI,SAAS,QAAQ;AAC9G,WAAO,gBAAgB,WAAW;AAClC,WAAO,aAAa,QAAQ;AAAA,EAChC;AAAA,EACA,kBAAkB,UAAU;AACxB,aAAS,QAAQ,CAAC;AAAA,EACtB;AACJ;;;ACpJA,IAAM,cAAN,MAAkB;AAAA,EACd,cAAc;AACV,SAAK,KAAK;AAAA,EACd;AAAA,EACA,UAAU,WAAW;AACjB,WAAO,IAAI,aAAa,SAAS;AAAA,EACrC;AAAA,EACA,cAAc;AAAA,EACd;AAAA,EACA,cAAc;AACV,WAAO;AAAA,EACX;AACJ;AACA,eAAsB,gBAAgB,QAAQ,UAAU,MAAM;AAC1D,QAAM,SAAS,IAAI,YAAY;AAC/B,QAAM,OAAO,UAAU,QAAQ,OAAO;AAC1C;;;ACfA,eAAsB,8BAA8B,QAAQ,UAAU,MAAM;AACxE,QAAM,qBAAqB,QAAQ,OAAO;AAC1C,QAAM,gBAAgB,QAAQ,OAAO;AACzC;;;ACJO,IAAM,oBAAN,MAAwB;AAAA,EAC3B,KAAK,SAAS,UAAU,QAAQ;AAC5B,UAAM,QAAQ,KAAK,UAAU,UAAU,MAAM,GAAG,OAAO,KAAK,aAAa,UAAU,MAAM,GAAG,YAAY,KAAK,MAAM,YAAY,KAAK,MAAM,aAAa,eAAe,KAAK,MAAM,YAAY,KAAK,MAAM,aAAa,uBAAwB,OAAO,eAAe,KAAM,cAAc,gBAAgB,KAAK,KAAM,KAAK,KAAK,uBAAwB;AACpV,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,YAAQ,UAAU;AAClB,YAAQ,UAAU,MAAM,GAAG,MAAM,CAAC;AAClC,YAAQ,OAAO,GAAG,CAAC;AACnB,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,cAAQ,OAAO,KAAK,QAAQ,CAAC;AAC7B,cAAQ,UAAU,KAAK,QAAQ,CAAC;AAChC,cAAQ,OAAO,aAAa;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,cAAc,UAAU;AACpB,UAAM,UAAU,SAAS;AACzB,WAAO,KAAK,MAAM,eAAc,mCAAS,WAAS,mCAAS,aAAY,CAAC,CAAC;AAAA,EAC7E;AACJ;;;ACnBO,IAAM,gBAAN,cAA4B,kBAAkB;AAAA,EACjD,UAAU,UAAU,QAAQ;AACxB,WAAO;AAAA,MACH,GAAG,CAAC,UAAU,SAAS,QAAQ;AAAA,MAC/B,GAAG,CAAC,UAAU,OAAO;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,aAAa,UAAU,QAAQ;AAC3B,UAAM,QAAQ,SAAS;AACvB,WAAO;AAAA,MACH,OAAO;AAAA,QACH,aAAa;AAAA,QACb,WAAW;AAAA,MACf;AAAA,MACA,QAAS,SAAS,QAAS,QAAQ;AAAA,IACvC;AAAA,EACJ;AACJ;;;ACjBO,IAAM,iBAAN,cAA6B,kBAAkB;AAAA,EAClD,UAAU,UAAU,QAAQ;AACxB,WAAO;AAAA,MACH,GAAG,CAAC;AAAA,MACJ,GAAG,SAAS;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,gBAAgB;AACZ,WAAO;AAAA,EACX;AAAA,EACA,aAAa,UAAU,QAAQ;AAC3B,WAAO;AAAA,MACH,OAAO;AAAA,QACH,aAAa;AAAA,QACb,WAAW;AAAA,MACf;AAAA,MACA,QAAQ,SAAS;AAAA,IACrB;AAAA,EACJ;AACJ;;;AClBA,eAAsB,wBAAwB,QAAQ,UAAU,MAAM;AAClE,QAAM,OAAO,SAAS,WAAW,IAAI,cAAc,GAAG,OAAO;AACjE;AACA,eAAsB,kBAAkB,QAAQ,UAAU,MAAM;AAC5D,QAAM,OAAO,SAAS,YAAY,IAAI,eAAe,GAAG,OAAO;AACnE;AACA,eAAsB,iBAAiB,QAAQ,UAAU,MAAM;AAC3D,QAAM,wBAAwB,QAAQ,OAAO;AAC7C,QAAM,kBAAkB,QAAQ,OAAO;AAC3C;;;ACVO,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc;AACV,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;ACvBO,IAAM,SAAN,cAAqB,gBAAgB;AAAA,EACxC,cAAc;AACV,UAAM;AACN,SAAK,YAAY,IAAI,gBAAgB;AACrC,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,KAAK,IAAI;AACf,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,KAAK;AAAA,IAC1B;AACA,SAAK,UAAU,KAAK,KAAK,SAAS;AAClC,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;ACrBA,SAAS,aAAa,UAAU,OAAO;AACnC,QAAM,SAAS,SAAS,QAAQ,gBAAgB,SAAS,QAAQ;AACjE,MAAI,CAAC,UAAU,CAAC,eAAe;AAC3B;AAAA,EACJ;AACA,QAAM,kBAAkB,cAAc,WAAW,SAAS,OAAO,YAAY,KAAK,MAAM,QAAQ,MAAM,IAAI,KAAK,IAAI,QAAQ,OAAO,SAAS;AAC3I,MAAI,CAAC,gBAAgB,QAAQ;AACzB;AAAA,EACJ;AACA,UAAQ,OAAO,QAAQ;AAAA,IACnB,KAAK;AACD,aAAO,SAAS;AAChB,UAAI,OAAO,QAAQ,KAAK;AACpB,eAAO,SAAS;AAAA,MACpB;AACA;AAAA,IACJ,KAAK;AAAA,IACL;AACI,aAAO,SAAS;AAChB,UAAI,OAAO,QAAQ,GAAG;AAClB,eAAO,SAAS;AAAA,MACpB;AACA;AAAA,EACR;AACA,MAAI,OAAO,YAAY,UAAU,GAAG;AAChC,WAAO,YAAY;AAAA,EACvB;AACJ;AACO,IAAM,gBAAN,MAAoB;AAAA,EACvB,YAAY,WAAW;AACnB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,KAAK,UAAU;AACX,UAAM,gBAAgB,SAAS,QAAQ;AACvC,QAAI,CAAC,eAAe;AAChB;AAAA,IACJ;AACA,aAAS,SAAS;AAAA,MACd,QAAQ,cAAc,UAAU;AAAA,MAChC,OAAQ,cAAc,cAAc,KAAK,IAAI,KAAK,KAAM;AAAA,IAC5D;AACA,aAAS,eAAe,cAAc;AACtC,QAAI,kBAAkB,cAAc;AACpC,QAAI,oBAAoB,UAAU;AAC9B,YAAM,QAAQ,KAAK,MAAM,UAAU,IAAI,CAAC;AACxC,wBAAkB,QAAQ,IAAI,sBAAsB;AAAA,IACxD;AACA,YAAQ,iBAAiB;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AACD,iBAAS,OAAO,SAAS;AACzB;AAAA,MACJ,KAAK;AACD,iBAAS,OAAO,SAAS;AACzB;AAAA,IACR;AACA,UAAM,kBAAkB,cAAc;AACtC,QAAI,gBAAgB,QAAQ;AACxB,eAAS,OAAO,QAAQ,IAAI,cAAc,gBAAgB,KAAK;AAC/D,eAAS,OAAO,WACX,cAAc,gBAAgB,KAAK,IAAI,MAAO,KAAK,UAAU,OAAO;AACzE,UAAI,CAAC,gBAAgB,MAAM;AACvB,iBAAS,OAAO,YAAY,UAAU;AAAA,MAC1C;AAAA,IACJ;AACA,aAAS,WAAW,SAAS,OAAO;AAAA,EACxC;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,SAAS,SAAS,QAAQ;AAChC,QAAI,CAAC,QAAQ;AACT,aAAO;AAAA,IACX;AACA,WAAO,CAAC,SAAS,aAAa,CAAC,SAAS,YAAY,OAAO,UAAU,UAAU,CAAC,OAAO;AAAA,EAC3F;AAAA,EACA,YAAY,YAAY,SAAS;AAC7B,QAAI,CAAC,QAAQ,QAAQ;AACjB,cAAQ,SAAS,IAAI,OAAO;AAAA,IAChC;AACA,eAAW,UAAU,SAAS;AAC1B,cAAQ,OAAO,KAAK,iCAAQ,MAAM;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,OAAO,UAAU,OAAO;AApF5B;AAqFQ,QAAI,CAAC,KAAK,UAAU,QAAQ,GAAG;AAC3B;AAAA,IACJ;AACA,iBAAa,UAAU,KAAK;AAC5B,aAAS,aAAW,cAAS,WAAT,mBAAiB,UAAS;AAAA,EAClD;AACJ;;;AC1FA,eAAsB,kBAAkB,QAAQ,UAAU,MAAM;AAC5D,QAAM,OAAO,mBAAmB,UAAU,CAAC,cAAc,IAAI,cAAc,SAAS,GAAG,OAAO;AAClG;;;ACHA,IAAM,YAAY,KAAK,KAAK,CAAC;AACtB,IAAM,eAAN,MAAmB;AAAA,EACtB,KAAK,SAAS,UAAU,QAAQ;AAC5B,UAAM,cAAc,SAAS,WAAW,gBAAgB,cAAc;AACtE,YAAQ,KAAK,CAAC,aAAa,CAAC,aAAa,eAAe,aAAa;AAAA,EACzE;AAAA,EACA,gBAAgB;AACZ,WAAO;AAAA,EACX;AACJ;;;ACRA,eAAsB,gBAAgB,QAAQ,UAAU,MAAM;AAC1D,QAAM,OAAO,SAAS,CAAC,QAAQ,QAAQ,GAAG,IAAI,aAAa,GAAG,OAAO;AACzE;;;ACFO,IAAM,aAAN,MAAiB;AAAA,EACpB,KAAK,SAAS,UAAU,QAAQ;AAC5B,UAAM,QAAQ,SAAS,OAAO,QAAQ,SAAS,aAAa;AAC5D,YAAQ,OAAO,GAAG,IAAI,MAAM;AAC5B,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,cAAQ,OAAO,KAAK,KAAK,KAAK;AAC9B,cAAQ,OAAO,GAAG,IAAI,SAAS,KAAK;AACpC,cAAQ,OAAO,KAAK,KAAK,KAAK;AAC9B,cAAQ,OAAO,GAAG,IAAI,MAAM;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,cAAc,UAAU;AACpB,UAAM,OAAO,SAAS;AACtB,WAAO,KAAK,MAAM,eAAc,6BAAM,WAAS,6BAAM,aAAY,CAAC,CAAC;AAAA,EACvE;AAAA,EACA,aAAa,WAAW,UAAU;AAC9B,UAAM,OAAO,SAAS,WAAW,QAAQ,eAAc,6BAAM,UAAS,CAAC;AACvE,aAAS,YAAY;AAAA,EACzB;AACJ;;;ACnBA,eAAsB,cAAc,QAAQ,UAAU,MAAM;AACxD,QAAM,OAAO,SAAS,QAAQ,IAAI,WAAW,GAAG,OAAO;AAC3D;;;ACFA,SAASC,kBAAiB,OAAO,YAAY,gBAAgB,KAAK,UAAU;AACxE,MAAI,CAAC,cACD,CAAC,eAAe,WACd,WAAW,YAAY,KAAK,MAAM,WAAW,SAAS,MAAM,WAAW,YAAY,IAAK;AAC1F;AAAA,EACJ;AACA,MAAI,CAAC,WAAW,MAAM;AAClB,eAAW,OAAO;AAAA,EACtB;AACA,OAAK,WAAW,aAAa,KAAK,KAAK,WAAW,QAAQ,WAAW,aAAa,IAAI;AAClF,eAAW,QAAQ,MAAM;AAAA,EAC7B;AACA,OAAK,WAAW,aAAa,KAAK,KAAK,WAAW,QAAQ,WAAW,aAAa,IAAI;AAClF;AAAA,EACJ;AACA,QAAM,SAAS,cAAc,eAAe,MAAM,GAAG,YAAY,WAAW,YAAY,KAAK,MAAM,SAAS,SAAS,KAAK,QAAQ,WAAW,SAAS;AACtJ,MAAI,CAAC,YAAY,WAAW,WAAW,cAAc;AACjD,eAAW,SAAS;AACpB,QAAI,WAAW,QAAQ,KAAK;AACxB,UAAI,CAAC,WAAW,OAAO;AACnB,mBAAW,QAAQ;AAAA,MACvB;AACA,iBAAW;AACX,UAAI,UAAU;AACV,mBAAW,SAAS;AACpB,mBAAW,SAAS,WAAW,QAAQ;AAAA,MAC3C;AAAA,IACJ;AAAA,EACJ,OACK;AACD,eAAW,SAAS;AACpB,QAAI,WAAW,QAAQ,GAAG;AACtB,UAAI,CAAC,WAAW,OAAO;AACnB,mBAAW,QAAQ;AAAA,MACvB;AACA,iBAAW;AACX,iBAAW,SAAS;AACpB,iBAAW,SAAS,WAAW;AAAA,IACnC;AAAA,EACJ;AACA,MAAI,WAAW,YAAY,UAAU,GAAG;AACpC,eAAW,YAAY;AAAA,EAC3B;AACA,MAAI,WAAW,QAAQ,KAAK;AACxB,eAAW,SAAS;AAAA,EACxB;AACJ;AACO,SAAS,kBAAkB,UAAU,OAAO;AAC/C,MAAI,CAAC,SAAS,eAAe,CAAC,SAAS,iBAAiB;AACpD;AAAA,EACJ;AACA,QAAM,EAAE,GAAG,GAAG,EAAE,IAAI,SAAS,aAAa,EAAE,GAAG,YAAY,GAAG,YAAY,GAAG,WAAW,IAAI,SAAS;AACrG,MAAI,GAAG;AACH,IAAAA,kBAAiB,OAAO,GAAG,YAAY,KAAK,KAAK;AAAA,EACrD;AACA,MAAI,GAAG;AACH,IAAAA,kBAAiB,OAAO,GAAG,YAAY,KAAK,IAAI;AAAA,EACpD;AACA,MAAI,GAAG;AACH,IAAAA,kBAAiB,OAAO,GAAG,YAAY,KAAK,IAAI;AAAA,EACpD;AACJ;;;AC5DO,IAAM,qBAAN,MAAyB;AAAA,EAC5B,YAAY,WAAW;AACnB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,KAAK,UAAU;AANnB;AAOQ,UAAM,YAAY,KAAK,WAAW,UAAU,SAAS;AACrD,UAAM,SAAS,yBAAyB,QAAQ,QAAQ,SAAS,IAAI,QAAQ,gBAAgB;AAC7F,aAAS,cAAc,cAAc,OAAO,KAAK,IAAI,UAAU,OAAO;AACtE,aAAS,gBAAgB,cAAc,OAAO,WAAW,CAAC;AAC1D,aAAS,mBAAkB,YAAO,UAAP,mBAAc;AACzC,UAAM,iBAAiB,gBAAgB,OAAO,KAAK,KAAK,SAAS,aAAa;AAC9E,QAAI,gBAAgB;AAChB,eAAS,cAAc,uBAAuB,gBAAgB,SAAS,iBAAiB,UAAU,OAAO,YAAY;AAAA,IACzH;AAAA,EACJ;AAAA,EACA,UAAU,UAAU;AAChB,UAAM,QAAQ,SAAS,iBAAiB,EAAE,YAAY,IAAI;AAC1D,WAAQ,CAAC,SAAS,aACd,CAAC,SAAS,YACV,CAAC,CAAC,WACA,2CAAa,EAAE,WAAU,UAAa,YAAY,EAAE,WACjD,2CAAa,EAAE,WAAU,UAAa,YAAY,EAAE,WACpD,2CAAa,EAAE,WAAU,UAAa,YAAY,EAAE;AAAA,EACjE;AAAA,EACA,OAAO,UAAU,OAAO;AACpB,QAAI,CAAC,KAAK,UAAU,QAAQ,GAAG;AAC3B;AAAA,IACJ;AACA,sBAAkB,UAAU,KAAK;AAAA,EACrC;AACJ;;;AC/BA,eAAsB,uBAAuB,QAAQ,UAAU,MAAM;AACjE,QAAM,OAAO,mBAAmB,eAAe,CAAC,cAAc,IAAI,mBAAmB,SAAS,GAAG,OAAO;AAC5G;;;ACFO,IAAM,aAAa,CAAC,QAAQ,aAAa,MAAM;AAC/C,IAAM,aAAN,MAAiB;AAAA,EACpB,KAAK,SAAS,UAAU,QAAQ,SAAS;AACrC,UAAM,YAAY,SAAS;AAC3B,QAAI,cAAc,QAAW;AACzB;AAAA,IACJ;AACA,UAAM,WAAW,UAAU;AAC3B,QAAI,aAAa,QAAW;AACxB;AAAA,IACJ;AACA,QAAI,SAAS,SAAS,QAAW;AAC7B,eAAS,OAAO,yBAAyB,UAAU,SAAS,eAAe;AAAA,IAC/E;AACA,UAAM,OAAO,SAAS,MAAM,QAAQ,UAAU,SAAS,IAAI,SAAS,UAAU,UAAU,OAAO,OAAO,KAAK,MAAM,MAAM,IAAI,GAAG,OAAO,UAAU,QAAQ,WAAW,OAAO,SAAS,MAAM,UAAW,KAAK,SAAS,SAAU;AAC3N,YAAQ,OAAO,GAAG,KAAK,IAAI,MAAM,IAAI,IAAI,OAAO,IAAI;AACpD,UAAM,MAAM;AAAA,MACR,GAAG,CAAC;AAAA,MACJ,GAAG,SAAS;AAAA,IAChB;AACA,YAAQ,cAAc;AACtB,QAAI,MAAM;AACN,cAAQ,SAAS,MAAM,IAAI,GAAG,IAAI,CAAC;AAAA,IACvC,OACK;AACD,cAAQ,WAAW,MAAM,IAAI,GAAG,IAAI,CAAC;AAAA,IACzC;AACA,YAAQ,cAAc;AAAA,EAC1B;AAAA,EACA,gBAAgB;AACZ,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAK,WAAW;AAClB,UAAM,UAAU,UAAU;AAC1B,QAAI,WAAW,KAAK,CAAC,MAAM,UAAU,GAAG,QAAQ,UAAU,MAAM,IAAI,CAAC,GAAG;AACpE,YAAM,eAAe,WAChB,IAAI,CAAC,MAAM,QAAQ,UAAU,MAAM,QAAQ,CAAC,CAAC,EAC7C,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;AACnC,gCAA0B,cAAc,CAAC,UAAU;AAC/C,iBAAS,KAAK,SAAS,MAAM,MAAM,MAAM,MAAM,CAAC;AAAA,MACpD,CAAC;AACD,YAAM,QAAQ,IAAI,QAAQ;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,aAAa,WAAW,UAAU;AAC9B,QAAI,CAAC,SAAS,SAAS,CAAC,WAAW,SAAS,SAAS,KAAK,GAAG;AACzD;AAAA,IACJ;AACA,UAAM,YAAY,SAAS;AAC3B,QAAI,cAAc,QAAW;AACzB;AAAA,IACJ;AACA,UAAM,WAAW,UAAU;AAC3B,QAAI,aAAa,QAAW;AACxB;AAAA,IACJ;AACA,aAAS,OAAO,yBAAyB,UAAU,SAAS,eAAe;AAAA,EAC/E;AACJ;;;AC1DA,eAAsB,cAAc,QAAQ,UAAU,MAAM;AACxD,QAAM,OAAO,SAAS,YAAY,IAAI,WAAW,GAAG,OAAO;AAC/D;;;ACuBA,eAAsB,SAAS,QAAQ,UAAU,MAAM;AACnD,UAAQ,MAAM;AACd,QAAM,kBAAkB,QAAQ,KAAK;AACrC,QAAM,+BAA+B,QAAQ,KAAK;AAClD,QAAM,8BAA8B,QAAQ,KAAK;AACjD,QAAM,8BAA8B,QAAQ,KAAK;AACjD,QAAM,+BAA+B,QAAQ,KAAK;AAClD,QAAM,4BAA4B,QAAQ,KAAK;AAC/C,QAAM,6BAA6B,QAAQ,KAAK;AAChD,QAAM,4BAA4B,QAAQ,KAAK;AAC/C,QAAM,8BAA8B,QAAQ,KAAK;AACjD,QAAM,+BAA+B,QAAQ,KAAK;AAClD,QAAM,4BAA4B,QAAQ,KAAK;AAC/C,QAAM,gCAAgC,QAAQ,KAAK;AACnD,QAAM,mCAAmC,QAAQ,KAAK;AACtD,QAAM,8BAA8B,QAAQ,KAAK;AACjD,QAAM,qBAAqB;AAC3B,QAAM,eAAe,QAAQ,KAAK;AAClC,QAAM,cAAc,QAAQ,KAAK;AACjC,QAAM,iBAAiB,QAAQ,KAAK;AACpC,QAAM,gBAAgB,QAAQ,KAAK;AACnC,QAAM,cAAc,QAAQ,KAAK;AACjC,QAAM,cAAc,QAAQ,KAAK;AACjC,QAAM,gBAAgB,QAAQ,KAAK;AACnC,QAAM,kBAAkB,QAAQ,KAAK;AACrC,QAAM,uBAAuB,QAAQ,KAAK;AAC1C,QAAM,UAAU,QAAQ,OAAO;AACnC;", "names": ["_a", "checkDestroy", "container", "container", "bounce", "container", "lineStyle", "container", "container", "container", "container", "Attractor", "Attractor", "container", "container", "updateColorValue"]}