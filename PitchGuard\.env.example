# PitchGuard Lite Environment Variables
# Copy this file to .env and fill in your values

# OpenRouter API Key (Required)
# Get a free key at: https://openrouter.ai
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional: Custom model (default: mistralai/mistral-7b-instruct:free)
# MODEL_NAME=mistralai/mistral-7b-instruct:free

# Optional: Backend host and port (default: localhost:8000)
# BACKEND_HOST=localhost
# BACKEND_PORT=8000