/* Custom styles for PitchGuard Lite */

/* Score bar animations and styling */
.score-bar-container {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.score-bar {
    height: 100%;
    width: 0%;
    border-radius: 4px;
    transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.score-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Score item hover effects */
.score-item {
    transition: transform 0.2s ease-in-out;
}

.score-item:hover {
    transform: translateY(-1px);
}

/* Loading animation for submit button */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom scrollbar for textarea */
textarea::-webkit-scrollbar {
    width: 8px;
}

textarea::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

textarea::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

textarea::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Fade in animation for results */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .grid-cols-1.lg\\:grid-cols-2 {
        grid-template-columns: 1fr;
    }
}

/* Focus states for accessibility */
textarea:focus,
button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Error state styling */
.error-state {
    border-color: #ef4444;
    background-color: #fef2f2;
}

/* Success state styling */
.success-state {
    border-color: #10b981;
    background-color: #f0fdf4;
}

/* Loading dots animation */
@keyframes loadingDots {
    0%, 20% {
        color: rgba(0, 0, 0, 0);
        text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                     0.5em 0 0 rgba(0, 0, 0, 0);
    }
    40% {
        color: black;
        text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                     0.5em 0 0 rgba(0, 0, 0, 0);
    }
    60% {
        text-shadow: 0.25em 0 0 black,
                     0.5em 0 0 rgba(0, 0, 0, 0);
    }
    80%, 100% {
        text-shadow: 0.25em 0 0 black,
                     0.5em 0 0 black;
    }
}

.loading-dots::after {
    content: "...";
    animation: loadingDots 1.5s infinite;
}

/* Card hover effects */
.bg-white {
    transition: box-shadow 0.3s ease-in-out;
}

.bg-white:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Score value animation */
.score-value {
    transition: all 0.3s ease-in-out;
}

.score-value.updated {
    transform: scale(1.1);
    font-weight: bold;
}

/* Receipt hash styling */
#receiptHash {
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5px;
    line-height: 1.4;
}

/* Gradient background animation */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

body {
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

/* Button press effect */
button:active {
    transform: translateY(1px);
}

/* Tooltip styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #374151;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Print styles */
@media print {
    body {
        background: white;
    }
    
    .no-print {
        display: none;
    }
}
