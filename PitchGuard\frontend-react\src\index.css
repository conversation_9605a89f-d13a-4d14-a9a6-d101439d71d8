@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl shadow-2xl;
  }
  
  .glass-button {
    @apply bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl hover:bg-white/30 transition-all duration-300;
  }
  
  .neon-glow {
    @apply shadow-[0_0_20px_rgba(59,130,246,0.5)] hover:shadow-[0_0_30px_rgba(59,130,246,0.8)] transition-shadow duration-300;
  }
  
  .cyber-grid {
    background-image: 
      linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .aurora-bg {
    background: linear-gradient(45deg, #a8edea 0%, #fed6e3 100%);
    background-size: 400% 400%;
    animation: gradient-xy 15s ease infinite;
  }
  
  .mesh-bg {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradient-xy 15s ease infinite;
  }
  
  .floating-orb {
    @apply absolute rounded-full blur-xl opacity-70 animate-float;
  }
  
  .floating-orb-1 {
    @apply w-32 h-32 bg-blue-400 top-10 left-10;
    animation-delay: 0s;
  }
  
  .floating-orb-2 {
    @apply w-24 h-24 bg-purple-400 top-1/3 right-20;
    animation-delay: 2s;
  }
  
  .floating-orb-3 {
    @apply w-40 h-40 bg-pink-400 bottom-20 left-1/4;
    animation-delay: 4s;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent;
  }
  
  .score-bar-glow {
    position: relative;
    overflow: hidden;
  }
  
  .score-bar-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.6),
      transparent
    );
    animation: shimmer 2s infinite;
  }
  
  .particle-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  .backdrop-blur-3xl {
    backdrop-filter: blur(64px);
  }
}
