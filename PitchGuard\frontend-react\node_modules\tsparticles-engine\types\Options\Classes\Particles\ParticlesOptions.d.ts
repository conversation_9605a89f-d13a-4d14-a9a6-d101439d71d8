import { AnimatableColor } from "../AnimatableColor";
import { Collisions } from "./Collisions/Collisions";
import type { Container } from "../../../Core/Container";
import type { Engine } from "../../../Core/Engine";
import type { IInteractivity } from "../../Interfaces/Interactivity/IInteractivity";
import type { IOptionLoader } from "../../Interfaces/IOptionLoader";
import type { IParticlesOptions } from "../../Interfaces/Particles/IParticlesOptions";
import { Move } from "./Move/Move";
import { Opacity } from "./Opacity/Opacity";
import { ParticlesBounce } from "./Bounce/ParticlesBounce";
import type { ParticlesGroups } from "../../../Types/ParticlesGroups";
import { ParticlesNumber } from "./Number/ParticlesNumber";
import type { RecursivePartial } from "../../../Types/RecursivePartial";
import { Shadow } from "./Shadow";
import { Shape } from "./Shape/Shape";
import type { SingleOrMultiple } from "../../../Types/SingleOrMultiple";
import { Size } from "./Size/Size";
import { Stroke } from "./Stroke";
import { ZIndex } from "./ZIndex/ZIndex";
export declare class ParticlesOptions implements IParticlesOptions, IOptionLoader<IParticlesOptions> {
    [name: string]: unknown;
    bounce: ParticlesBounce;
    collisions: Collisions;
    color: AnimatableColor;
    groups: ParticlesGroups;
    interactivity?: RecursivePartial<IInteractivity>;
    move: Move;
    number: ParticlesNumber;
    opacity: Opacity;
    reduceDuplicates: boolean;
    shadow: Shadow;
    shape: Shape;
    size: Size;
    stroke: SingleOrMultiple<Stroke>;
    zIndex: ZIndex;
    private readonly _container;
    private readonly _engine;
    constructor(engine: Engine, container?: Container);
    load(data?: RecursivePartial<IParticlesOptions>): void;
}
