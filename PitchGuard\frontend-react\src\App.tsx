import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Toaster } from 'react-hot-toast'
import AuthWrapper from './components/AuthWrapper'
import LandingPage from './components/LandingPage'
import PitchAnalyzer from './components/PitchAnalyzer'
import Navigation from './components/Navigation'
import Squares from './components/Squares'
import { useExportShare } from './hooks/useExportShare'

type ViewType = 'landing' | 'analyzer' | 'results'

function App() {
  const [currentView, setCurrentView] = useState<ViewType>('landing')
  const [analysisData, setAnalysisData] = useState<any>(null)
  const { generatePDF, shareResults, isExporting } = useExportShare()

  const handleGetStarted = () => {
    setCurrentView('analyzer')
  }

  const handleAnalysisComplete = (data: any) => {
    setAnalysisData(data)
    setCurrentView('results')
  }

  const handleExport = () => {
    if (analysisData) {
      const exportData = {
        scores: analysisData.scores,
        receipt: analysisData.receipt,
        timestamp: new Date().toLocaleString(),
        averageScore: Object.values(analysisData.scores).reduce((sum: number, score: any) => sum + score, 0) / 4
      }
      generatePDF(exportData)
    }
  }

  const handleShare = () => {
    if (analysisData) {
      const exportData = {
        scores: analysisData.scores,
        receipt: analysisData.receipt,
        timestamp: new Date().toLocaleString(),
        averageScore: Object.values(analysisData.scores).reduce((sum: number, score: any) => sum + score, 0) / 4
      }
      shareResults(exportData)
    }
  }

  return (
    <>
      {/* Global Squares Background */}
      <Squares
        direction="diagonal"
        speed={0.5}
        borderColor="rgba(59, 130, 246, 0.2)"
        squareSize={60}
        hoverFillColor="rgba(59, 130, 246, 0.1)"
      />

      <AuthWrapper>
        <div className="min-h-screen relative overflow-hidden z-10">
          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="relative z-20"
          >
          <AnimatePresence mode="wait">
            {currentView === 'landing' && (
              <motion.div
                key="landing"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.05 }}
                transition={{ duration: 0.5 }}
              >
                <LandingPage onGetStarted={handleGetStarted} />
              </motion.div>
            )}

            {currentView === 'analyzer' && (
              <motion.div
                key="analyzer"
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ duration: 0.5 }}
                className="min-h-screen flex items-center justify-center px-4 py-8"
              >
                <PitchAnalyzer onAnalysisComplete={handleAnalysisComplete} />
              </motion.div>
            )}

            {currentView === 'results' && analysisData && (
              <motion.div
                key="results"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -50 }}
                transition={{ duration: 0.5 }}
                className="min-h-screen flex items-center justify-center px-4 py-8"
              >
                <div className="max-w-6xl mx-auto">
                  {/* Results content will be rendered here */}
                  <div className="glass-card p-8 text-center">
                    <h2 className="text-3xl font-bold text-white mb-4">Analysis Complete!</h2>
                    <p className="text-white/70 mb-8">Your pitch has been analyzed successfully.</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                      {Object.entries(analysisData.scores).map(([key, score]: [string, any]) => (
                        <div key={key} className="glass-card p-4">
                          <div className="text-2xl font-bold text-blue-400">{score.toFixed(1)}</div>
                          <div className="text-sm text-white/70 capitalize">{key.replace('_', ' ')}</div>
                        </div>
                      ))}
                    </div>

                    <div className="flex gap-4 justify-center">
                      <button
                        onClick={handleExport}
                        disabled={isExporting}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg"
                      >
                        {isExporting ? 'Exporting...' : 'Export PDF'}
                      </button>
                      <button
                        onClick={handleShare}
                        className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg"
                      >
                        Share Results
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

          {/* Navigation Dock */}
          <Navigation
            currentView={currentView}
            onNavigate={setCurrentView}
            onExport={handleExport}
            onHelp={() => console.log('Help')}
            onSettings={() => console.log('Settings')}
          />

          {/* Toast Notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                color: 'white',
              },
            }}
          />
      </div>
    </AuthWrapper>
    </>
  )
}

export default App
