{"name": "react-particles", "version": "2.12.2", "description": "Official tsParticles React Component - Easily create highly customizable particle, confetti and fireworks animations and use them as animated backgrounds for your website. Ready to use components available also for Web Components, Vue.js (2.x and 3.x), Angular, Svelte, jQuery, Preact, Riot.js, Solid.js, Inferno.", "scripts": {"install": "node ./scripts/install.js"}, "repository": {"url": "https://github.com/tsparticles/react", "directory": "components/react", "type": "git"}, "keywords": ["front-end", "frontend", "tsparticles", "particles.js", "<PERSON><PERSON>s", "particles", "particle", "canvas", "jsparticles", "xparticles", "particles-js", "particles-bg", "particles-bg-vue", "particles-ts", "particles.ts", "react-particles-js", "react-particles.js", "react-particles", "react", "reactjs", "vue-particles", "ngx-particles", "angular-particles", "particleground", "vue", "v<PERSON><PERSON><PERSON>", "preact", "preactjs", "j<PERSON>y", "<PERSON><PERSON>s", "angular", "typescript", "javascript", "animation", "web", "html5", "web-design", "webdesign", "css", "html", "css3", "animated", "background", "confetti", "canvas", "fireworks", "fireworks-js", "confetti-js", "confettijs", "<PERSON>js", "canvas-confetti"], "funding": [{"type": "github", "url": "https://github.com/sponsors/matteob<PERSON>i"}, {"type": "github", "url": "https://github.com/sponsors/tsparticles"}, {"type": "buymeacoffee", "url": "https://www.buymeacoffee.com/matteobruni"}], "bugs": {"url": "https://github.com/tsparticles/react/issues"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "cjs/index.js", "module": "esm/index.js", "types": "types/index.d.ts", "unpkg": "particles.min.js", "jsdelivr": "particles.min.js", "peerDependencies": {"react": ">=16"}, "dependencies": {"tsparticles-engine": "^2.12.0"}}