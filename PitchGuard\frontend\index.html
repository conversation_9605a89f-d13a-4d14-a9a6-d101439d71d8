<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PitchGuard Lite - Secure Pitch Scoring</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Header -->
        <header class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">PitchGuard Lite</h1>
            <p class="text-gray-600 text-lg">Secure, confidential pitch scoring powered by AI</p>
            <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <p class="text-sm text-green-700">
                    🔒 Your pitch is encrypted client-side and never stored in plaintext
                </p>
            </div>
        </header>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Input Section -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Submit Your Pitch</h2>
                
                <form id="pitchForm" class="space-y-4">
                    <div>
                        <label for="pitchText" class="block text-sm font-medium text-gray-700 mb-2">
                            Pitch Content
                        </label>
                        <textarea 
                            id="pitchText" 
                            name="pitchText"
                            rows="12"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                            placeholder="Enter your startup pitch here... Include your problem statement, solution, market opportunity, business model, and team background."
                            required
                        ></textarea>
                        <p class="text-xs text-gray-500 mt-1">
                            Minimum 100 characters recommended for accurate scoring
                        </p>
                    </div>
                    
                    <button 
                        type="submit" 
                        id="submitBtn"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <span id="submitText">Analyze Pitch</span>
                        <span id="loadingSpinner" class="hidden">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                        </span>
                    </button>
                </form>
            </div>

            <!-- Results Section -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Pitch Scores</h2>
                
                <div id="resultsContainer" class="hidden space-y-6">
                    <!-- Score Bars -->
                    <div class="space-y-4">
                        <div class="score-item">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Narrative Clarity</span>
                                <span id="clarityScore" class="text-sm font-bold text-blue-600">0.0</span>
                            </div>
                            <div class="score-bar-container">
                                <div id="clarityBar" class="score-bar bg-blue-500"></div>
                            </div>
                        </div>

                        <div class="score-item">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Originality</span>
                                <span id="originalityScore" class="text-sm font-bold text-green-600">0.0</span>
                            </div>
                            <div class="score-bar-container">
                                <div id="originalityBar" class="score-bar bg-green-500"></div>
                            </div>
                        </div>

                        <div class="score-item">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Team Strength</span>
                                <span id="teamScore" class="text-sm font-bold text-purple-600">0.0</span>
                            </div>
                            <div class="score-bar-container">
                                <div id="teamBar" class="score-bar bg-purple-500"></div>
                            </div>
                        </div>

                        <div class="score-item">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Market Fit</span>
                                <span id="marketScore" class="text-sm font-bold text-orange-600">0.0</span>
                            </div>
                            <div class="score-bar-container">
                                <div id="marketBar" class="score-bar bg-orange-500"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Receipt -->
                    <div class="border-t pt-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Score Receipt</h3>
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <code id="receiptHash" class="text-xs text-gray-600 break-all"></code>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">
                            This hash serves as proof of your scoring session
                        </p>
                    </div>
                </div>

                <!-- Placeholder when no results -->
                <div id="noResults" class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <svg class="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-500">Submit your pitch to see AI-powered scores</p>
                </div>

                <!-- Error Display -->
                <div id="errorContainer" class="hidden">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Error</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p id="errorMessage"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="text-center mt-12 text-gray-500 text-sm">
            <p>PitchGuard Lite - Secure pitch analysis with client-side encryption</p>
        </footer>
    </div>

    <script src="main.js"></script>
</body>
</html>
