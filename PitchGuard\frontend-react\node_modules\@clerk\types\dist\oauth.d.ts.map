{"version": 3, "file": "oauth.d.ts", "sourceRoot": "", "sources": ["../src/oauth.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAElD,MAAM,MAAM,UAAU,GAAG,MAAM,CAAC;AAEhC,MAAM,WAAW,iBAAiB;IAChC,QAAQ,EAAE,aAAa,CAAC;IACxB,QAAQ,EAAE,aAAa,CAAC;IACxB,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,MAAM,qBAAqB,GAAG,UAAU,CAAC;AAC/C,MAAM,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAC3C,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAAC;AAC7C,MAAM,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAC3C,MAAM,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAC3C,MAAM,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAC3C,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAAC;AAC7C,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAAC;AAC7C,MAAM,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAC3C,MAAM,MAAM,qBAAqB,GAAG,UAAU,CAAC;AAC/C,MAAM,MAAM,yBAAyB,GAAG,eAAe,CAAC;AACxD,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAAC;AAC7C,MAAM,MAAM,sBAAsB,GAAG,WAAW,CAAC;AACjD,MAAM,MAAM,sBAAsB,GAAG,WAAW,CAAC;AACjD,MAAM,MAAM,sBAAsB,GAAG,WAAW,CAAC;AACjD,MAAM,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAC3C,MAAM,MAAM,kBAAkB,GAAG,OAAO,CAAC;AACzC,MAAM,MAAM,iBAAiB,GAAG,MAAM,CAAC;AACvC,MAAM,MAAM,sBAAsB,GAAG,WAAW,CAAC;AACjD,MAAM,MAAM,qBAAqB,GAAG,UAAU,CAAC;AAC/C,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAAC;AAC7C,MAAM,MAAM,iBAAiB,GAAG,MAAM,CAAC;AACvC,MAAM,MAAM,gBAAgB,GAAG,KAAK,CAAC;AACrC,MAAM,MAAM,kBAAkB,GAAG,OAAO,CAAC;AACzC,MAAM,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAC3C,MAAM,MAAM,cAAc,GAAG,GAAG,CAAC;AACjC,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAAC;AAE7C,MAAM,MAAM,aAAa,GACrB,qBAAqB,GACrB,mBAAmB,GACnB,oBAAoB,GACpB,mBAAmB,GACnB,mBAAmB,GACnB,mBAAmB,GACnB,oBAAoB,GACpB,oBAAoB,GACpB,mBAAmB,GACnB,qBAAqB,GACrB,yBAAyB,GACzB,oBAAoB,GACpB,sBAAsB,GACtB,sBAAsB,GACtB,sBAAsB,GACtB,mBAAmB,GACnB,kBAAkB,GAClB,iBAAiB,GACjB,sBAAsB,GACtB,qBAAqB,GACrB,oBAAoB,GACpB,iBAAiB,GACjB,gBAAgB,GAChB,kBAAkB,GAClB,mBAAmB,GACnB,cAAc,GACd,oBAAoB,CAAC;AAEzB,eAAO,MAAM,eAAe,EAAE,iBAAiB,EAmK9C,CAAC;AAEF,UAAU,yBAAyB;IACjC,QAAQ,CAAC,EAAE,aAAa,CAAC;IACzB,QAAQ,CAAC,EAAE,aAAa,CAAC;CAC1B;AAED,wBAAgB,oBAAoB,CAAC,EACnC,QAAQ,EACR,QAAQ,GACT,EAAE,yBAAyB,GAAG,iBAAiB,GAAG,SAAS,GAAG,IAAI,CAMlE;AAED,wBAAgB,oBAAoB,CAAC,YAAY,EAAE,aAAa,EAAE,uBAcjE"}