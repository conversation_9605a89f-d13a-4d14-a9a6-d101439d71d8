{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../use-sync-external-store/shim/index.js", "../../@clerk/clerk-react/dist/esm/chunk-XTU7I5IS.js", "../../@clerk/clerk-react/src/polyfills.ts", "../../@clerk/shared/src/isomorphicAtob.ts", "../../@clerk/shared/src/keys.ts", "../../@clerk/shared/dist/chunk-NDCDZYN6.mjs", "../../@clerk/clerk-react/src/contexts/ClerkProvider.tsx", "../../@clerk/shared/src/utils/runtimeEnvironment.ts", "../../@clerk/shared/src/deprecated.ts", "../../@clerk/shared/src/error.ts", "../../@clerk/clerk-react/src/errors.ts", "../../@clerk/clerk-react/src/utils/childrenUtils.tsx", "../../@clerk/clerk-react/src/utils/errorThrower.ts", "../../@clerk/clerk-react/src/utils/isConstructor.ts", "../../@clerk/shared/src/loadScript.ts", "../../@clerk/shared/src/proxy.ts", "../../@clerk/shared/src/utils/instance.ts", "../../@clerk/shared/src/url.ts", "../../@clerk/clerk-react/src/utils/isDevOrStageUrl.tsx", "../../@clerk/clerk-react/src/utils/versionSelector.ts", "../../@clerk/clerk-react/src/utils/loadClerkJsScript.ts", "../../@clerk/clerk-react/src/utils/useMaxAllowedInstancesGuard.tsx", "../../@clerk/clerk-react/src/utils/useCustomElementPortal.tsx", "../../@clerk/shared/src/underscore.ts", "../../@clerk/shared/src/file.ts", "../../@clerk/shared/src/handleValueOrFn.ts", "../../@clerk/shared/src/browser.ts", "../../@clerk/shared/src/utils/createDeferredPromise.ts", "../../@clerk/shared/src/utils/runWithExponentialBackOff.ts", "../../@clerk/shared/src/utils/logErrorInDevMode.ts", "../../@clerk/clerk-react/src/utils/useCustomPages.tsx", "../../@clerk/clerk-react/src/components/uiComponents.tsx", "../../@clerk/clerk-react/src/components/withClerk.tsx", "../../@clerk/shared/src/react/hooks/createContextAndHook.ts", "../../@clerk/shared/src/react/clerk-swr.ts", "../../@clerk/shared/src/react/contexts.tsx", "../../@clerk/shared/src/react/hooks/usePagesOrInfinite.ts", "../../@clerk/shared/src/react/hooks/useOrganization.tsx", "../../@clerk/shared/src/react/hooks/useOrganizationList.tsx", "../../@clerk/shared/src/react/hooks/useOrganizations.tsx", "../../@clerk/shared/src/react/hooks/useSafeLayoutEffect.tsx", "../../swr/core/dist/index.mjs", "../../swr/_internal/dist/index.mjs", "../../swr/infinite/dist/index.mjs", "../../@clerk/clerk-react/src/contexts/IsomorphicClerkContext.tsx", "../../@clerk/clerk-react/src/contexts/StructureContext.tsx", "../../@clerk/clerk-react/src/contexts/assertHelpers.ts", "../../@clerk/clerk-react/src/contexts/ClerkContextProvider.tsx", "../../@clerk/clerk-react/src/isomorphicClerk.ts", "../../@clerk/clerk-react/src/utils/deriveState.ts", "../../@clerk/clerk-react/src/contexts/AuthContext.ts", "../../@clerk/clerk-react/src/components/controlComponents.tsx", "../../@clerk/clerk-react/src/hooks/useUser.ts", "../../@clerk/clerk-react/src/hooks/useAuth.ts", "../../@clerk/clerk-react/src/hooks/utils.ts", "../../@clerk/clerk-react/src/hooks/useSession.ts", "../../@clerk/clerk-react/src/hooks/useClerk.ts", "../../@clerk/clerk-react/src/hooks/useSignIn.ts", "../../@clerk/clerk-react/src/hooks/useSignUp.ts", "../../@clerk/clerk-react/src/hooks/useSessionList.ts", "../../@clerk/clerk-react/src/hooks/useMagicLink.ts", "../../@clerk/clerk-react/src/hooks/useEmailLink.ts", "../../@clerk/clerk-react/src/components/withUser.tsx", "../../@clerk/clerk-react/src/components/withSession.tsx", "../../@clerk/clerk-react/src/components/SignInButton.tsx", "../../@clerk/clerk-react/src/components/SignUpButton.tsx", "../../@clerk/clerk-react/src/components/SignOutButton.tsx", "../../@clerk/clerk-react/src/components/SignInWithMetamaskButton.tsx"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "var __accessCheck = (obj, member, msg) => {\n  if (!member.has(obj))\n    throw TypeError(\"Cannot \" + msg);\n};\nvar __privateGet = (obj, member, getter) => {\n  __accessCheck(obj, member, \"read from private field\");\n  return getter ? getter.call(obj) : member.get(obj);\n};\nvar __privateAdd = (obj, member, value) => {\n  if (member.has(obj))\n    throw TypeError(\"Cannot add the same private member more than once\");\n  member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\n};\nvar __privateSet = (obj, member, value, setter) => {\n  __accessCheck(obj, member, \"write to private field\");\n  setter ? setter.call(obj, value) : member.set(obj, value);\n  return value;\n};\nvar __privateMethod = (obj, member, method) => {\n  __accessCheck(obj, member, \"access private method\");\n  return method;\n};\n\nexport {\n  __privateGet,\n  __privateAdd,\n  __privateSet,\n  __privateMethod\n};\n//# sourceMappingURL=chunk-XTU7I5IS.js.map", "/**\n * Vite does not define `global` by default\n * One workaround is to use the `define` config prop\n * https://vitejs.dev/config/#define\n * We are solving this in the SDK level to reduce setup steps.\n */\nif (typeof window !== 'undefined' && !window.global) {\n  window.global = typeof global === 'undefined' ? window : global;\n}\n\nexport {};\n", "/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n", "import type { PublishableKey } from '@clerk/types';\n\nimport { isomorphicAtob } from './isomorphicAtob';\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const keyPrefix = PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi)\n    ? PUBLISHABLE_KEY_TEST_PREFIX\n    : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${btoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(key: string | undefined): PublishableKey | null {\n  key = key || '';\n\n  if (!isPublishableKey(key)) {\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  if (!frontendApi.endsWith('$')) {\n    return null;\n  }\n\n  frontendApi = frontendApi.slice(0, -1);\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\nexport function isPublishableKey(key: string) {\n  key = key || '';\n\n  const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n  const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n  return hasValidPrefix && hasValidFrontendApiPostfix;\n}\n\nexport function isLegacyFrontendApiKey(key: string) {\n  key = key || '';\n\n  return key.startsWith('clerk.');\n}\n\nexport function createDevOrStagingUrlCache() {\n  // TODO: Check if we can merge it with `./instance.ts#isStaging()`\n  const DEV_OR_STAGING_SUFFIXES = [\n    '.lcl.dev',\n    '.stg.dev',\n    '.lclstage.dev',\n    '.stgstage.dev',\n    '.dev.lclclerk.com',\n    '.stg.lclclerk.com',\n    '.accounts.lclclerk.com',\n    'accountsstage.dev',\n    'accounts.dev',\n  ];\n\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromApiKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromApiKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\nexport {\n  __export,\n  __reExport\n};\n//# sourceMappingURL=chunk-NDCDZYN6.mjs.map", "import { isLegacyFrontendApiKey, isPublishableKey } from '@clerk/shared/keys';\nimport type { InitialState } from '@clerk/types';\nimport React from 'react';\n\nimport { multipleClerkProvidersError } from '../errors';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { __internal__setErrorThrowerOptions, errorThrower, withMaxAllowedInstancesGuard } from '../utils';\nimport { ClerkContextProvider } from './ClerkContextProvider';\nimport { StructureContext, StructureContextStates } from './StructureContext';\n\n__internal__setErrorThrowerOptions({\n  packageName: '@clerk/clerk-react',\n});\n\nexport type ClerkProviderProps = IsomorphicClerkOptions & {\n  children: React.ReactNode;\n  initialState?: InitialState;\n};\n\nfunction ClerkProviderBase(props: ClerkProviderProps): JSX.Element {\n  const { initialState, children, ...restIsomorphicClerkOptions } = props;\n  const { frontendApi = '', publishableKey = '', Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;\n\n  if (!userInitialisedClerk) {\n    if (!publishableKey && !frontendApi) {\n      errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !isPublishableKey(publishableKey)) {\n      errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });\n    } else if (!publishableKey && frontendApi && !isLegacyFrontendApiKey(frontendApi)) {\n      errorThrower.throwInvalidFrontendApiError({ key: frontendApi });\n    }\n  }\n\n  return (\n    <StructureContext.Provider value={StructureContextStates.noGuarantees}>\n      <ClerkContextProvider\n        initialState={initialState}\n        isomorphicClerkOptions={restIsomorphicClerkOptions}\n      >\n        {children}\n      </ClerkContextProvider>\n    </StructureContext.Provider>\n  );\n}\n\nconst ClerkProvider = withMaxAllowedInstancesGuard(ClerkProviderBase, 'ClerkProvider', multipleClerkProvidersError);\n\nClerkProvider.displayName = 'ClerkProvider';\n\nexport { ClerkProvider, __internal__setErrorThrowerOptions };\n", "export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch (err) {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch (err) {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch (err) {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n", "import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n", "import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/types';\n\nimport { deprecated } from './deprecated';\n\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\nexport function isKnownError(error: any) {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param {any} err - The error object to check.\n * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   * @memberof ClerkRuntimeError\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, used for localization\n   *\n   * @type {string}\n   * @memberof ClerkRuntimeError\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = message;\n    this.clerkRuntimeError = true;\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns {string} A formatted string with the error name and message.\n   * @memberof ClerkRuntimeError\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\n/**\n * @deprecated Use `EmailLinkError` instead.\n */\nexport class MagicLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    Object.setPrototypeOf(this, MagicLinkError.prototype);\n    deprecated('MagicLinkError', 'Use `EmailLinkError` instead.');\n  }\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\n/**\n * Check if the error is a MagicLinkError.\n * @deprecated Use `isEmailLinkError` instead.\n */\nexport function isMagicLinkError(err: Error): err is MagicLinkError {\n  deprecated('isMagicLinkError', 'Use `isEmailLinkError` instead.');\n  return err instanceof MagicLinkError;\n}\n\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err instanceof EmailLinkError;\n}\n\nconst _MagicLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n};\n\n/**\n * @deprecated Use `EmailLinkErrorCode` instead.\n */\nexport const MagicLinkErrorCode = new Proxy(_MagicLinkErrorCode, {\n  get(target, prop, receiver) {\n    deprecated('MagicLinkErrorCode', 'Use `EmailLinkErrorCode` instead.');\n    return Reflect.get(target, prop, receiver);\n  },\n});\n\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n};\n\nconst DefaultMessages = Object.freeze({\n  InvalidFrontendApiErrorMessage: `The frontendApi passed to Clerk is invalid. You can get your Frontend API key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidFrontendApiError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n}\n\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidFrontendApiError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidFrontendApiErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n  };\n}\n", "export {\n  MagicLinkErrorCode,\n  EmailLinkErrorCode,\n  isClerkAPIResponseError,\n  isKnownError,\n  isMetamaskError,\n  isMagicLinkError,\n  isEmailLinkError,\n} from '@clerk/shared/error';\n\nexport const noFrontendApiError = 'Clerk: You must add the frontendApi prop to your <ClerkProvider>';\n\nexport const noClerkProviderError = 'Clerk: You must wrap your application in a <ClerkProvider> component.';\n\nexport const noGuaranteedLoadedError = (hookName: string) =>\n  `Clerk: You're calling ${hookName} before there's a guarantee the client has been loaded. Call ${hookName} from a child of <SignedIn>, <SignedOut>, or <ClerkLoaded>, or use the withClerk() HOC.`;\n\nexport const noGuaranteedUserError = (hookName: string) =>\n  `Clerk: You're calling ${hookName} before there's a guarantee there's an active user. Call ${hookName} from a child of <SignedIn> or use the withUser() HOC.`;\n\nexport const multipleClerkProvidersError =\n  \"Clerk: You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.\";\n\nexport const hocChildrenNotAFunctionError = 'Clerk: Child of WithClerk must be a function.';\n\nexport const multipleChildrenInButtonComponent = (name: string) =>\n  `Clerk: You've passed multiple children components to <${name}/>. You can only pass a single child component or text.`;\n\nexport const invalidStateError =\n  'Clerk: Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support';\n\nexport const unsupportedNonBrowserDomainOrProxyUrlFunction =\n  'Clerk: Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.';\n\nexport const userProfilePageRenderedError =\n  'Clerk: <UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.';\nexport const userProfileLinkRenderedError =\n  'Clerk: <UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.';\n\nexport const organizationProfilePageRenderedError =\n  'Clerk: <OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.';\nexport const organizationProfileLinkRenderedError =\n  'Clerk: <OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.';\n\nexport const customPagesIgnoredComponent = (componentName: string) =>\n  `Clerk: <${componentName} /> can only accept <${componentName}.Page /> and <${componentName}.Link /> as its children. Any other provided component will be ignored.`;\n\nexport const customPageWrongProps = (componentName: string) =>\n  `Clerk: Missing props. <${componentName}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`;\n\nexport const customLinkWrongProps = (componentName: string) =>\n  `Clerk: Missing props. <${componentName}.Link /> component requires the following props: url, label and labelIcon.`;\n\nexport const useAuthHasRequiresRoleOrPermission =\n  'Clerk: Missing parameters. `has` from `useAuth` requires a permission or role key to be passed. Example usage: `has({permission: \"org:posts:edit\"`';\n", "import React from 'react';\n\nimport { multipleChildrenInButtonComponent } from '../errors';\n\nexport const assertSingleChild =\n  (children: React.ReactNode) =>\n  (name: 'SignInButton' | 'SignUpButton' | 'SignOutButton' | 'SignInWithMetamaskButton') => {\n    try {\n      return React.Children.only(children);\n    } catch (e) {\n      throw new Error(multipleChildrenInButtonComponent(name));\n    }\n  };\n\nexport const normalizeWithDefaultValue = (children: React.ReactNode | undefined, defaultText: string) => {\n  if (!children) {\n    children = defaultText;\n  }\n  if (typeof children === 'string') {\n    children = <button>{children}</button>;\n  }\n  return children;\n};\n\nexport const safeExecute =\n  (cb: unknown) =>\n  (...args: any) => {\n    if (cb && typeof cb === 'function') {\n      return cb(...args);\n    }\n  };\n", "import type { ErrorThrowerOptions } from '@clerk/shared/error';\nimport { buildErrorThrower } from '@clerk/shared/error';\n\nconst errorThrower = buildErrorThrower({ packageName: '@clerk/react' });\n\nfunction __internal__setErrorThrowerOptions(options: ErrorThrowerOptions) {\n  errorThrower.setMessages(options).setPackageName(options);\n}\n\nexport { errorThrower, __internal__setErrorThrowerOptions };\n", "export function isConstructor<T>(f: any): f is T {\n  return typeof f === 'function';\n}\n", "const NO_DOCUMENT_ERROR = 'loadScript cannot be called when document does not exist';\nconst NO_SRC_ERROR = 'loadScript cannot be called without a src';\n\ntype LoadScriptOptions = {\n  async?: boolean;\n  defer?: boolean;\n  crossOrigin?: 'anonymous' | 'use-credentials';\n  beforeLoad?: (script: HTMLScriptElement) => void;\n};\n\nexport async function loadScript(src = '', opts: LoadScriptOptions): Promise<HTMLScriptElement> {\n  const { async, defer, beforeLoad, crossOrigin } = opts || {};\n  return new Promise((resolve, reject) => {\n    if (!src) {\n      reject(NO_SRC_ERROR);\n    }\n\n    if (!document || !document.body) {\n      reject(NO_DOCUMENT_ERROR);\n    }\n\n    const script = document.createElement('script');\n\n    crossOrigin && script.setAttribute('crossorigin', crossOrigin);\n    script.async = async || false;\n    script.defer = defer || false;\n\n    script.addEventListener('load', () => {\n      script.remove();\n      resolve(script);\n    });\n\n    script.addEventListener('error', () => {\n      script.remove();\n      reject();\n    });\n\n    script.src = src;\n    beforeLoad?.(script);\n    document.body.appendChild(script);\n  });\n}\n", "import { deprecated } from './deprecated';\n\nexport function isValidProxyUrl(key: string | undefined) {\n  if (!key) {\n    return true;\n  }\n\n  return isHttpOrHttps(key) || isProxyUrlRelative(key);\n}\n\nexport function isHttpOrHttps(key: string | undefined) {\n  return /^http(s)?:\\/\\//.test(key || '');\n}\n\nexport function isProxyUrlRelative(key: string) {\n  return key.startsWith('/');\n}\n\nexport function proxyUrlToAbsoluteURL(url: string | undefined): string {\n  if (!url) {\n    return '';\n  }\n  return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;\n}\n\n/**\n * @deprecated Use `buildRequestUrl` from @clerk/backend\n */\nexport function getRequestUrl({ request, relativePath }: { request: Request; relativePath?: string }): URL {\n  deprecated('getRequestUrl', 'Use `buildRequestUrl` from @clerk/backend instead.');\n  const { headers, url: initialUrl } = request;\n  const url = new URL(initialUrl);\n  const host = headers.get('X-Forwarded-Host') ?? headers.get('host') ?? (headers as any)['host'] ?? url.host;\n\n  // X-Forwarded-Proto could be 'https, http'\n  let protocol =\n    (headers.get('X-Forwarded-Proto') ?? (headers as any)['X-Forwarded-Proto'])?.split(',')[0] ?? url.protocol;\n  protocol = protocol.replace(/[:/]/, '');\n\n  return new URL(relativePath || url.pathname, `${protocol}://${host}`);\n}\n", "/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n", "import { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, pkgVersion?: string) => {\n  if (!pkgVersion && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!pkgVersion) {\n    return 'latest';\n  }\n\n  return pkgVersion.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (\n  frontendApi: string,\n  { pkgVersion = CLERK_JS_PACKAGE_VERSION, clerkJSVersion }: { pkgVersion?: string; clerkJSVersion?: string },\n) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, pkgVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n", "import { createDevOrStagingUrlCache } from '@clerk/shared/keys';\nconst { isDevOrStagingUrl } = createDevOrStagingUrlCache();\nexport { isDevOrStagingUrl };\n", "/**\n * This version selector is a bit complicated, so here is the flow:\n * 1. Use the clerkJSVersion prop on the provider\n * 2. Use the exact `@clerk/clerk-js` version if it is a `@snapshot` prerelease for `@clerk/clerk-react`\n * 3. Use the prerelease tag of `@clerk/clerk-react`\n * 4. Fallback to the major version of `@clerk/clerk-react`\n * @param clerkJSVersion - The optional clerkJSVersion prop on the provider\n * @returns The npm tag, version or major version to use\n */\nexport const versionSelector = (clerkJSVersion: string | undefined) => {\n  if (clerkJSVersion) {\n    return clerkJSVersion;\n  }\n\n  const prereleaseTag = getPrereleaseTag(PACKAGE_VERSION);\n  if (prereleaseTag) {\n    if (prereleaseTag === 'snapshot') {\n      return JS_PACKAGE_VERSION;\n    }\n\n    return prereleaseTag;\n  }\n\n  return getMajorVersion(PACKAGE_VERSION);\n};\n\n// TODO: Replace these with \"semver\" package\nconst getPrereleaseTag = (packageVersion: string) => packageVersion.match(/-(.*)\\./)?.[1];\nconst getMajorVersion = (packageVersion: string) => packageVersion.split('.')[0];\n", "import { parsePublishable<PERSON>ey } from '@clerk/shared/keys';\nimport { loadScript } from '@clerk/shared/loadScript';\nimport { isValidProxyUrl, proxyUrlToAbsoluteURL } from '@clerk/shared/proxy';\nimport { addClerkPrefix } from '@clerk/shared/url';\n\nimport type { IsomorphicClerkOptions } from '../types';\nimport { errorThrower } from './errorThrower';\nimport { isDevOrStagingUrl } from './isDevOrStageUrl';\nimport { versionSelector } from './versionSelector';\n\nconst FAILED_TO_LOAD_ERROR = 'Clerk: Failed to load Clerk';\n\ntype LoadClerkJsScriptOptions = Omit<IsomorphicClerkOptions, 'proxyUrl' | 'domain'> & {\n  proxyUrl: string;\n  domain: string;\n};\n\nexport const loadClerkJsScript = (opts: LoadClerkJsScriptOptions) => {\n  const { frontendApi, publishableKey } = opts;\n\n  if (!publishableKey && !frontendApi) {\n    errorThrower.throwMissingPublishableKeyError();\n  }\n\n  return loadScript(clerkJsScriptUrl(opts), {\n    async: true,\n    crossOrigin: 'anonymous',\n    beforeLoad: applyClerkJsScriptAttributes(opts),\n  }).catch(() => {\n    throw new Error(FAILED_TO_LOAD_ERROR);\n  });\n};\n\nconst clerkJsScriptUrl = (opts: LoadClerkJsScriptOptions) => {\n  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey, frontendApi } = opts;\n\n  if (clerkJSUrl) {\n    return clerkJSUrl;\n  }\n\n  let scriptHost = '';\n  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {\n    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\\/\\//, '');\n  } else if (domain && !isDevOrStagingUrl(parsePublishableKey(publishableKey)?.frontendApi || frontendApi || '')) {\n    scriptHost = addClerkPrefix(domain);\n  } else {\n    scriptHost = parsePublishableKey(publishableKey)?.frontendApi || frontendApi || '';\n  }\n\n  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\\.+$/, '')}.` : '';\n  const version = versionSelector(clerkJSVersion);\n  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;\n};\n\nconst applyClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => (script: HTMLScriptElement) => {\n  const { publishableKey, frontendApi, proxyUrl, domain } = options;\n  if (publishableKey) {\n    script.setAttribute('data-clerk-publishable-key', publishableKey);\n  } else if (frontendApi) {\n    script.setAttribute('data-clerk-frontend-api', frontendApi);\n  }\n\n  if (proxyUrl) {\n    script.setAttribute('data-clerk-proxy-url', proxyUrl);\n  }\n\n  if (domain) {\n    script.setAttribute('data-clerk-domain', domain);\n  }\n};\n", "import React from 'react';\n\nconst counts = new Map<string, number>();\n\nexport function useMaxAllowedInstancesGuard(name: string, error: string, maxCount = 1): void {\n  React.useEffect(() => {\n    const count = counts.get(name) || 0;\n    if (count == maxCount) {\n      throw new Error(error);\n    }\n    counts.set(name, count + 1);\n\n    return () => {\n      counts.set(name, (counts.get(name) || 1) - 1);\n    };\n  }, []);\n}\n\nexport function withMaxAllowedInstancesGuard<P>(\n  WrappedComponent: React.ComponentType<P>,\n  name: string,\n  error: string,\n): React.ComponentType<P> {\n  const displayName = WrappedComponent.displayName || WrappedComponent.name || name || 'Component';\n  const Hoc = (props: P) => {\n    useMaxAllowedInstancesGuard(name, error);\n    return <WrappedComponent {...(props as any)} />;\n  };\n  Hoc.displayName = `withMaxAllowedInstancesGuard(${displayName})`;\n  return Hoc;\n}\n", "import React, { useState } from 'react';\nimport { createPortal } from 'react-dom';\n\nexport type UseCustomElementPortalParams = {\n  component: React.ReactNode;\n  id: number;\n};\n\nexport type UseCustomElementPortalReturn = {\n  portal: () => JSX.Element;\n  mount: (node: Element) => void;\n  unmount: () => void;\n  id: number;\n};\n\n// This function takes a component as prop, and returns functions that mount and unmount\n// the given component into a given node\nexport const useCustomElementPortal = (elements: UseCustomElementPortalParams[]) => {\n  const initialState = Array(elements.length).fill(null);\n  const [nodes, setNodes] = useState<(Element | null)[]>(initialState);\n\n  return elements.map((el, index) => ({\n    id: el.id,\n    mount: (node: Element) => setNodes(prevState => prevState.map((n, i) => (i === index ? node : n))),\n    unmount: () => setNodes(prevState => prevState.map((n, i) => (i === index ? null : n))),\n    portal: () => <>{nodes[index] ? createPortal(el.component, nodes[index] as Element) : null}</>,\n  }));\n};\n", "/**\n * Converts an array of strings to a comma-separated sentence\n * @param items {Array<string>}\n * @returns {string} Returns a string with the items joined by a comma and the last item joined by \", or\"\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once <PERSON>fari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n", "/**\n * Read an expected JSON type File.\n *\n * Probably paired with:\n *  <input type='file' accept='application/JSON' ... />\n */\nexport function readJSONFile(file: File): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.addEventListener('load', function () {\n      const result = JSON.parse(reader.result as string);\n      resolve(result);\n    });\n\n    reader.addEventListener('error', reject);\n    reader.readAsText(file);\n  });\n}\n\nconst MimeTypeToExtensionMap = Object.freeze({\n  'image/png': 'png',\n  'image/jpeg': 'jpg',\n  'image/gif': 'gif',\n  'image/webp': 'webp',\n  'image/x-icon': 'ico',\n  'image/vnd.microsoft.icon': 'ico',\n} as const);\n\nexport type SupportedMimeType = keyof typeof MimeTypeToExtensionMap;\n\nexport const extension = (mimeType: SupportedMimeType): string => {\n  return MimeTypeToExtensionMap[mimeType];\n};\n", "type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n", "/**\n * Checks if the window object is defined. You can also use this to check if something is happening on the client side.\n * @returns {boolean}\n */\nexport function inBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nconst botAgents = [\n  'bot',\n  'spider',\n  'crawl',\n  'APIs-Google',\n  'AdsBot',\n  'Googlebot',\n  'mediapartners',\n  'Google Favicon',\n  'FeedFetcher',\n  'Google-Read-Aloud',\n  'DuplexWeb-Google',\n  'googleweblight',\n  'bing',\n  'yandex',\n  'baidu',\n  'duckduck',\n  'yahoo',\n  'ecosia',\n  'ia_archiver',\n  'facebook',\n  'instagram',\n  'pinterest',\n  'reddit',\n  'slack',\n  'twitter',\n  'whatsapp',\n  'youtube',\n  'semrush',\n];\nconst botAgentRegex = new RegExp(botAgents.join('|'), 'i');\n\n/**\n * Checks if the user agent is a bot.\n * @param userAgent - Any user agent string\n * @returns {boolean}\n */\nexport function userAgentIsRobot(userAgent: string): boolean {\n  return !userAgent ? false : botAgentRegex.test(userAgent);\n}\n\n/**\n * Checks if the current environment is a browser and the user agent is not a bot.\n * @returns {boolean}\n */\nexport function isValidBrowser(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n  return !userAgentIsRobot(navigator?.userAgent) && !navigator?.webdriver;\n}\n\n/**\n * Checks if the current environment is a browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isBrowserOnline(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n\n  const isNavigatorOnline = navigator?.onLine;\n\n  // Being extra safe with the experimental `connection` property, as it is not defined in all browsers\n  // https://developer.mozilla.org/en-US/docs/Web/API/Navigator/connection#browser_compatibility\n  // @ts-ignore\n  const isExperimentalConnectionOnline = navigator?.connection?.rtt !== 0 && navigator?.connection?.downlink !== 0;\n  return isExperimentalConnectionOnline && isNavigatorOnline;\n}\n\n/**\n * Runs `isBrowserOnline` and `isValidBrowser` to check if the current environment is a valid browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isValidBrowserOnline(): boolean {\n  return isBrowserOnline() && isValidBrowser();\n}\n", "import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n", "type Milliseconds = number;\n\ntype BackoffOptions = Partial<{\n  firstDelay: Milliseconds;\n  maxDelay: Milliseconds;\n  timeMultiple: number;\n  shouldRetry: (error: unknown, iterationsCount: number) => boolean;\n}>;\n\nconst defaultOptions: Required<BackoffOptions> = {\n  firstDelay: 125,\n  maxDelay: 0,\n  timeMultiple: 2,\n  shouldRetry: () => true,\n};\n\nconst sleep = async (ms: Milliseconds) => new Promise(s => setTimeout(s, ms));\n\nconst createExponentialDelayAsyncFn = (opts: {\n  firstDelay: Milliseconds;\n  maxDelay: Milliseconds;\n  timeMultiple: number;\n}) => {\n  let timesCalled = 0;\n\n  const calculateDelayInMs = () => {\n    const constant = opts.firstDelay;\n    const base = opts.timeMultiple;\n    const delay = constant * Math.pow(base, timesCalled);\n    return Math.min(opts.maxDelay || delay, delay);\n  };\n\n  return async (): Promise<void> => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\n\nexport const runWithExponentialBackOff = async <T>(\n  callback: () => T | Promise<T>,\n  options: BackoffOptions = {},\n): Promise<T> => {\n  let iterationsCount = 0;\n  const { shouldRetry, firstDelay, maxDelay, timeMultiple } = {\n    ...defaultOptions,\n    ...options,\n  };\n  const delay = createExponentialDelayAsyncFn({ firstDelay, maxDelay, timeMultiple });\n\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterationsCount++;\n      if (!shouldRetry(e, iterationsCount)) {\n        throw e;\n      }\n      await delay();\n    }\n  }\n};\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(message);\n  }\n};\n", "import { logErrorInDevMode } from '@clerk/shared';\nimport type { CustomPage } from '@clerk/types';\nimport type { ReactElement } from 'react';\nimport React from 'react';\n\nimport {\n  OrganizationProfileLink,\n  OrganizationProfilePage,\n  UserProfileLink,\n  UserProfilePage,\n} from '../components/uiComponents';\nimport { customLinkWrongProps, customPagesIgnoredComponent, customPageWrongProps } from '../errors';\nimport type { UserProfilePageProps } from '../types';\nimport type { UseCustomElementPortalParams, UseCustomElementPortalReturn } from './useCustomElementPortal';\nimport { useCustomElementPortal } from './useCustomElementPortal';\n\nconst isThatComponent = (v: any, component: React.ReactNode): v is React.ReactNode => {\n  return !!v && React.isValidElement(v) && (v as React.ReactElement)?.type === component;\n};\n\nexport const useUserProfileCustomPages = (children: React.ReactNode | React.ReactNode[]) => {\n  const reorderItemsLabels = ['account', 'security'];\n  return useCustomPages({\n    children,\n    reorderItemsLabels,\n    LinkComponent: UserProfileLink,\n    PageComponent: UserProfilePage,\n    componentName: 'UserProfile',\n  });\n};\n\nexport const useOrganizationProfileCustomPages = (children: React.ReactNode | React.ReactNode[]) => {\n  const reorderItemsLabels = ['members', 'settings'];\n  return useCustomPages({\n    children,\n    reorderItemsLabels,\n    LinkComponent: OrganizationProfileLink,\n    PageComponent: OrganizationProfilePage,\n    componentName: 'OrganizationProfile',\n  });\n};\n\ntype UseCustomPagesParams = {\n  children: React.ReactNode | React.ReactNode[];\n  LinkComponent: any;\n  PageComponent: any;\n  reorderItemsLabels: string[];\n  componentName: string;\n};\n\ntype CustomPageWithIdType = UserProfilePageProps & { children?: React.ReactNode };\n\nconst useCustomPages = ({\n  children,\n  LinkComponent,\n  PageComponent,\n  reorderItemsLabels,\n  componentName,\n}: UseCustomPagesParams) => {\n  const validChildren: CustomPageWithIdType[] = [];\n\n  React.Children.forEach(children, child => {\n    if (!isThatComponent(child, PageComponent) && !isThatComponent(child, LinkComponent)) {\n      if (child) {\n        logErrorInDevMode(customPagesIgnoredComponent(componentName));\n      }\n      return;\n    }\n\n    const { props } = child as ReactElement;\n\n    const { children, label, url, labelIcon } = props;\n\n    if (isThatComponent(child, PageComponent)) {\n      if (isReorderItem(props, reorderItemsLabels)) {\n        // This is a reordering item\n        validChildren.push({ label });\n      } else if (isCustomPage(props)) {\n        // this is a custom page\n        validChildren.push({ label, labelIcon, children, url });\n      } else {\n        logErrorInDevMode(customPageWrongProps(componentName));\n        return;\n      }\n    }\n\n    if (isThatComponent(child, LinkComponent)) {\n      if (isExternalLink(props)) {\n        // This is an external link\n        validChildren.push({ label, labelIcon, url });\n      } else {\n        logErrorInDevMode(customLinkWrongProps(componentName));\n        return;\n      }\n    }\n  });\n\n  const customPageContents: UseCustomElementPortalParams[] = [];\n  const customPageLabelIcons: UseCustomElementPortalParams[] = [];\n  const customLinkLabelIcons: UseCustomElementPortalParams[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isCustomPage(cp)) {\n      customPageContents.push({ component: cp.children, id: index });\n      customPageLabelIcons.push({ component: cp.labelIcon, id: index });\n      return;\n    }\n    if (isExternalLink(cp)) {\n      customLinkLabelIcons.push({ component: cp.labelIcon, id: index });\n    }\n  });\n\n  const customPageContentsPortals = useCustomElementPortal(customPageContents);\n  const customPageLabelIconsPortals = useCustomElementPortal(customPageLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n\n  const customPages: CustomPage[] = [];\n  const customPagesPortals: React.ComponentType[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isReorderItem(cp, reorderItemsLabels)) {\n      customPages.push({ label: cp.label });\n      return;\n    }\n    if (isCustomPage(cp)) {\n      const {\n        portal: contentPortal,\n        mount,\n        unmount,\n      } = customPageContentsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customPageLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mount, unmount, mountIcon, unmountIcon });\n      customPagesPortals.push(contentPortal);\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n    if (isExternalLink(cp)) {\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customLinkLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mountIcon, unmountIcon });\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n  });\n\n  return { customPages, customPagesPortals };\n};\n\nconst isReorderItem = (childProps: any, validItems: string[]): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !url && !labelIcon && validItems.some(v => v === label);\n};\n\nconst isCustomPage = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !!children && !!url && !!labelIcon && !!label;\n};\n\nconst isExternalLink = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !!url && !!labelIcon && !!label;\n};\n", "import { logErrorInDevMode } from '@clerk/shared';\nimport type {\n  CreateOrganizationProps,\n  GoogleOneTapProps,\n  OrganizationListProps,\n  OrganizationProfileProps,\n  OrganizationSwitcherProps,\n  SignInProps,\n  SignUpProps,\n  UserButtonProps,\n  UserProfileProps,\n} from '@clerk/types';\nimport type { PropsWithChildren } from 'react';\nimport React, { createElement } from 'react';\n\nimport {\n  organizationProfileLinkRenderedError,\n  organizationProfilePageRenderedError,\n  userProfileLinkRenderedError,\n  userProfilePageRenderedError,\n} from '../errors';\nimport type {\n  MountProps,\n  OpenProps,\n  OrganizationProfileLinkProps,\n  OrganizationProfilePageProps,\n  UserProfileLinkProps,\n  UserProfilePageProps,\n  WithClerkProp,\n} from '../types';\nimport { useOrganizationProfileCustomPages, useUserProfileCustomPages } from '../utils';\nimport { withClerk } from './withClerk';\n\ntype UserProfileExportType = typeof _UserProfile & {\n  Page: typeof UserProfilePage;\n  Link: typeof UserProfileLink;\n};\n\ntype UserButtonExportType = typeof _UserButton & {\n  UserProfilePage: typeof UserProfilePage;\n  UserProfileLink: typeof UserProfileLink;\n};\n\ntype UserButtonPropsWithoutCustomPages = Omit<UserButtonProps, 'userProfileProps'> & {\n  userProfileProps?: Pick<UserProfileProps, 'additionalOAuthScopes' | 'appearance'>;\n};\n\ntype OrganizationProfileExportType = typeof _OrganizationProfile & {\n  Page: typeof OrganizationProfilePage;\n  Link: typeof OrganizationProfileLink;\n};\n\ntype OrganizationSwitcherExportType = typeof _OrganizationSwitcher & {\n  OrganizationProfilePage: typeof OrganizationProfilePage;\n  OrganizationProfileLink: typeof OrganizationProfileLink;\n};\n\ntype OrganizationSwitcherPropsWithoutCustomPages = Omit<OrganizationSwitcherProps, 'organizationProfileProps'> & {\n  organizationProfileProps?: Pick<OrganizationProfileProps, 'appearance'>;\n};\n\n// README: <Portal/> should be a class pure component in order for mount and unmount\n// lifecycle props to be invoked correctly. Replacing the class component with a\n// functional component wrapped with a React.memo is not identical to the original\n// class implementation due to React intricacies such as the useEffect’s cleanup\n// seems to run AFTER unmount, while componentWillUnmount runs BEFORE.\n\n// More information can be found at https://clerkinc.slack.com/archives/C015S0BGH8R/p1624891993016300\n\n// The function Portal implementation is commented out for future reference.\n\n// const Portal = React.memo(({ props, mount, unmount }: MountProps) => {\n//   const portalRef = React.createRef<HTMLDivElement>();\n\n//   useEffect(() => {\n//     if (portalRef.current) {\n//       mount(portalRef.current, props);\n//     }\n//     return () => {\n//       if (portalRef.current) {\n//         unmount(portalRef.current);\n//       }\n//     };\n//   }, []);\n\n//   return <div ref={portalRef} />;\n// });\n\n// Portal.displayName = 'ClerkPortal';\n\nconst isMountProps = (props: any): props is MountProps => {\n  return 'mount' in props;\n};\n\nconst isOpenProps = (props: any): props is OpenProps => {\n  return 'open' in props;\n};\n\nclass Portal extends React.PureComponent<MountProps | OpenProps> {\n  private portalRef = React.createRef<HTMLDivElement>();\n\n  componentDidUpdate(prevProps: Readonly<MountProps | OpenProps>) {\n    if (!isMountProps(prevProps) || !isMountProps(this.props)) {\n      return;\n    }\n\n    if (\n      prevProps.props.appearance !== this.props.props.appearance ||\n      prevProps.props?.customPages?.length !== this.props.props?.customPages?.length\n    ) {\n      this.props.updateProps({ node: this.portalRef.current, props: this.props.props });\n    }\n  }\n\n  componentDidMount() {\n    if (this.portalRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.mount(this.portalRef.current, this.props.props);\n      }\n\n      if (isOpenProps(this.props)) {\n        this.props.open(this.props.props);\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.portalRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.unmount(this.portalRef.current);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.close();\n      }\n    }\n  }\n\n  render() {\n    return (\n      <>\n        <div ref={this.portalRef} />\n        {isMountProps(this.props) &&\n          this.props?.customPagesPortals?.map((portal, index) => createElement(portal, { key: index }))}\n      </>\n    );\n  }\n}\n\nexport const SignIn = withClerk(({ clerk, ...props }: WithClerkProp<SignInProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountSignIn}\n      unmount={clerk.unmountSignIn}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'SignIn');\n\nexport const SignUp = withClerk(({ clerk, ...props }: WithClerkProp<SignUpProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountSignUp}\n      unmount={clerk.unmountSignUp}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'SignUp');\n\nexport function UserProfilePage({ children }: PropsWithChildren<UserProfilePageProps>) {\n  logErrorInDevMode(userProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function UserProfileLink({ children }: PropsWithChildren<UserProfileLinkProps>) {\n  logErrorInDevMode(userProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _UserProfile = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<Omit<UserProfileProps, 'customPages'>>>) => {\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    return (\n      <Portal\n        mount={clerk.mountUserProfile}\n        unmount={clerk.unmountUserProfile}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, customPages }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'UserProfile',\n);\n\nexport const UserProfile: UserProfileExportType = Object.assign(_UserProfile, {\n  Page: UserProfilePage,\n  Link: UserProfileLink,\n});\n\nconst _UserButton = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<UserButtonPropsWithoutCustomPages>>) => {\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });\n    return (\n      <Portal\n        mount={clerk.mountUserButton}\n        unmount={clerk.unmountUserButton}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, userProfileProps }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'UserButton',\n);\n\nexport const UserButton: UserButtonExportType = Object.assign(_UserButton, {\n  UserProfilePage,\n  UserProfileLink,\n});\n\nexport function OrganizationProfilePage({ children }: PropsWithChildren<OrganizationProfilePageProps>) {\n  logErrorInDevMode(organizationProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function OrganizationProfileLink({ children }: PropsWithChildren<OrganizationProfileLinkProps>) {\n  logErrorInDevMode(organizationProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _OrganizationProfile = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<Omit<OrganizationProfileProps, 'customPages'>>>) => {\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    return (\n      <Portal\n        mount={clerk.mountOrganizationProfile}\n        unmount={clerk.unmountOrganizationProfile}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, customPages }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'OrganizationProfile',\n);\n\nexport const OrganizationProfile: OrganizationProfileExportType = Object.assign(_OrganizationProfile, {\n  Page: OrganizationProfilePage,\n  Link: OrganizationProfileLink,\n});\n\nexport const CreateOrganization = withClerk(({ clerk, ...props }: WithClerkProp<CreateOrganizationProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountCreateOrganization}\n      unmount={clerk.unmountCreateOrganization}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'CreateOrganization');\n\nconst _OrganizationSwitcher = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<OrganizationSwitcherPropsWithoutCustomPages>>) => {\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });\n    return (\n      <Portal\n        mount={clerk.mountOrganizationSwitcher}\n        unmount={clerk.unmountOrganizationSwitcher}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, organizationProfileProps }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'OrganizationSwitcher',\n);\n\nexport const OrganizationSwitcher: OrganizationSwitcherExportType = Object.assign(_OrganizationSwitcher, {\n  OrganizationProfilePage,\n  OrganizationProfileLink,\n});\n\nexport const OrganizationList = withClerk(({ clerk, ...props }: WithClerkProp<OrganizationListProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountOrganizationList}\n      unmount={clerk.unmountOrganizationList}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'OrganizationList');\n\nexport const GoogleOneTap = withClerk(({ clerk, ...props }: WithClerkProp<GoogleOneTapProps>) => {\n  return (\n    <Portal\n      open={clerk.openGoogleOneTap}\n      close={clerk.closeGoogleOneTap}\n      props={props}\n    />\n  );\n}, 'GoogleOneTap');\n", "import type { LoadedClerk } from '@clerk/types';\nimport React from 'react';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { LoadedGuarantee } from '../contexts/StructureContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withClerk = <P extends { clerk: LoadedClerk }>(\n  Component: React.ComponentType<P>,\n  displayName?: string,\n) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC = (props: Omit<P, 'clerk'>) => {\n    const clerk = useIsomorphicClerkContext();\n\n    if (!clerk.loaded) {\n      return null;\n    }\n\n    return (\n      <LoadedGuarantee>\n        <Component\n          {...(props as P)}\n          clerk={clerk}\n        />\n      </LoadedGuarantee>\n    );\n  };\n  HOC.displayName = `withClerk(${displayName})`;\n  return HOC;\n};\n\nexport const WithClerk: React.FC<{\n  children: (clerk: LoadedClerk) => React.ReactNode;\n}> = ({ children }) => {\n  const clerk = useIsomorphicClerkContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!clerk.loaded) {\n    return null;\n  }\n\n  return <LoadedGuarantee>{children(clerk as unknown as LoadedClerk)}</LoadedGuarantee>;\n};\n", "'use client';\nimport React from 'react';\n\nexport function assertContextExists(contextVal: unknown, msgOrCtx: string | React.Context<any>): asserts contextVal {\n  if (!contextVal) {\n    throw typeof msgOrCtx === 'string' ? new Error(msgOrCtx) : new Error(`${msgOrCtx.displayName} not found`);\n  }\n}\n\ntype Options = { assertCtxFn?: (v: unknown, msg: string) => void };\ntype ContextOf<T> = React.Context<{ value: T } | undefined>;\ntype UseCtxFn<T> = () => T;\n\n/**\n * Creates and returns a Context and two hooks that return the context value.\n * The Context type is derived from the type passed in by the user.\n * The first hook returned guarantees that the context exists so the returned value is always CtxValue\n * The second hook makes no guarantees, so the returned value can be CtxValue | undefined\n */\nexport const createContextAndHook = <CtxVal>(\n  displayName: string,\n  options?: Options,\n): [ContextOf<CtxVal>, UseCtxFn<CtxVal>, UseCtxFn<CtxVal | Partial<CtxVal>>] => {\n  const { assertCtxFn = assertContextExists } = options || {};\n  const Ctx = React.createContext<{ value: CtxVal } | undefined>(undefined);\n  Ctx.displayName = displayName;\n\n  const useCtx = () => {\n    const ctx = React.useContext(Ctx);\n    assertCtxFn(ctx, `${displayName} not found`);\n    return (ctx as any).value as CtxVal;\n  };\n\n  const useCtxWithoutGuarantee = () => {\n    const ctx = React.useContext(Ctx);\n    return ctx ? ctx.value : {};\n  };\n\n  return [Ctx, useCtx, useCtxWithoutGuarantee];\n};\n", "'use client';\nexport * from 'swr';\nexport { default as useSWR, SWRConfig } from 'swr';\nexport { default as useSWRInfinite } from 'swr/infinite';\n", "'use client';\n\nimport type {\n  ActiveSessionResource,\n  ClientResource,\n  LoadedClerk,\n  OrganizationInvitationResource,\n  OrganizationMembershipResource,\n  OrganizationResource,\n  UserResource,\n} from '@clerk/types';\nimport type { PropsWithChildren } from 'react';\nimport React from 'react';\n\nimport { deprecated } from '../deprecated';\nimport { SWRConfig } from './clerk-swr';\nimport { createContextAndHook } from './hooks/createContextAndHook';\n\nconst [ClerkInstanceContext, useClerkInstanceContext] = createContextAndHook<LoadedClerk>('ClerkInstanceContext');\nconst [UserContext, useUserContext] = createContextAndHook<UserResource | null | undefined>('UserContext');\nconst [ClientContext, useClientContext] = createContextAndHook<ClientResource | null | undefined>('ClientContext');\nconst [SessionContext, useSessionContext] = createContextAndHook<ActiveSessionResource | null | undefined>(\n  'SessionContext',\n);\n\ntype OrganizationContextProps = {\n  organization: OrganizationResource | null | undefined;\n\n  /**\n   * @deprecated This property will be dropped in the next major release.\n   * This property is only used in another deprecated part: `invitationList` from useOrganization\n   */\n  lastOrganizationInvitation: OrganizationInvitationResource | null | undefined;\n  /**\n   * @deprecated This property will be dropped in the next major release.\n   * This property is only used in another deprecated part: `membershipList` from useOrganization\n   */\n  lastOrganizationMember: OrganizationMembershipResource | null | undefined;\n};\nconst [OrganizationContextInternal, useOrganizationContext] = createContextAndHook<{\n  organization: OrganizationResource | null | undefined;\n  lastOrganizationInvitation: OrganizationInvitationResource | null | undefined;\n  lastOrganizationMember: OrganizationMembershipResource | null | undefined;\n}>('OrganizationContext');\n\nconst OrganizationProvider = ({\n  children,\n  organization,\n  lastOrganizationMember,\n  lastOrganizationInvitation,\n  swrConfig,\n}: PropsWithChildren<\n  OrganizationContextProps & {\n    // Exporting inferred types  directly from SWR will result in error while building declarations\n    swrConfig?: any;\n  }\n>) => {\n  return (\n    <SWRConfig value={swrConfig}>\n      <OrganizationContextInternal.Provider\n        value={{\n          value: {\n            organization,\n            lastOrganizationMember,\n            lastOrganizationInvitation,\n          },\n        }}\n      >\n        {children}\n      </OrganizationContextInternal.Provider>\n    </SWRConfig>\n  );\n};\n\n/**\n * @deprecated use OrganizationProvider instead\n */\nexport const OrganizationContext = (...args: Parameters<typeof OrganizationProvider>) => {\n  deprecated('OrganizationContext', 'Use `OrganizationProvider` instead');\n  return OrganizationProvider(...args);\n};\n\nexport {\n  ClientContext,\n  useClientContext,\n  OrganizationProvider,\n  useOrganizationContext,\n  UserContext,\n  useUserContext,\n  SessionContext,\n  useSessionContext,\n  ClerkInstanceContext,\n  useClerkInstanceContext,\n};\n", "'use client';\n\nimport { useCallback, useMemo, useRef, useState } from 'react';\n\nimport { useSWR, useSWRInfinite } from '../clerk-swr';\nimport type { CacheSetter, PaginatedResources, ValueOrSetter } from '../types';\n\nfunction getDifferentKeys(obj1: Record<string, unknown>, obj2: Record<string, unknown>): Record<string, unknown> {\n  const keysSet = new Set(Object.keys(obj2));\n  const differentKeysObject: Record<string, unknown> = {};\n\n  for (const key1 of Object.keys(obj1)) {\n    if (!keysSet.has(key1)) {\n      differentKeysObject[key1] = obj1[key1];\n    }\n  }\n\n  return differentKeysObject;\n}\n\ntype PagesOrInfiniteOptions = {\n  /**\n   * This the starting point for your fetched results. The initial value persists between re-renders\n   */\n  initialPage?: number;\n  /**\n   * Maximum number of items returned per request. The initial value persists between re-renders\n   */\n  pageSize?: number;\n};\n\nexport const useWithSafeValues = <T extends PagesOrInfiniteOptions>(params: T | true | undefined, defaultValues: T) => {\n  const shouldUseDefaults = typeof params === 'boolean' && params;\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(\n    shouldUseDefaults ? defaultValues.initialPage : params?.initialPage ?? defaultValues.initialPage,\n  );\n  const pageSizeRef = useRef(shouldUseDefaults ? defaultValues.pageSize : params?.pageSize ?? defaultValues.pageSize);\n\n  const newObj: Record<string, unknown> = {};\n  for (const key of Object.keys(defaultValues)) {\n    // @ts-ignore\n    newObj[key] = shouldUseDefaults ? defaultValues[key] : params?.[key] ?? defaultValues[key];\n  }\n\n  return {\n    ...newObj,\n    initialPage: initialPageRef.current,\n    pageSize: pageSizeRef.current,\n  } as T;\n};\n\ntype ArrayType<DataArray> = DataArray extends Array<infer ElementType> ? ElementType : never;\ntype ExtractData<Type> = Type extends { data: infer Data } ? ArrayType<Data> : Type;\n\ntype DefaultOptions = {\n  /**\n   * Persists the previous pages with new ones in the same array\n   */\n  infinite?: boolean;\n  /**\n   * Return the previous key's data until the new data has been loaded\n   */\n  keepPreviousData?: boolean;\n  /**\n   * Should a request be triggered\n   */\n  enabled?: boolean;\n};\n\ntype UsePagesOrInfinite = <\n  Params extends PagesOrInfiniteOptions,\n  FetcherReturnData extends Record<string, any>,\n  CacheKeys = Record<string, unknown>,\n  TOptions extends DefaultOptions = DefaultOptions,\n>(\n  /**\n   * The parameters will be passed to the fetcher\n   */\n  params: Params,\n  /**\n   * A Promise returning function to fetch your data\n   */\n  fetcher: ((p: Params) => FetcherReturnData | Promise<FetcherReturnData>) | undefined,\n  /**\n   * Internal configuration of the hook\n   */\n  options: TOptions,\n  cacheKeys: CacheKeys,\n) => PaginatedResources<ExtractData<FetcherReturnData>, TOptions['infinite']>;\n\nexport const usePagesOrInfinite: UsePagesOrInfinite = (params, fetcher, options, cacheKeys) => {\n  const [paginatedPage, setPaginatedPage] = useState(params.initialPage ?? 1);\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(params.initialPage ?? 1);\n  const pageSizeRef = useRef(params.pageSize ?? 10);\n\n  const enabled = options.enabled ?? true;\n  const triggerInfinite = options.infinite ?? false;\n  const keepPreviousData = options.keepPreviousData ?? false;\n\n  const pagesCacheKey = {\n    ...cacheKeys,\n    ...params,\n    initialPage: paginatedPage,\n    pageSize: pageSizeRef.current,\n  };\n\n  const {\n    data: swrData,\n    isValidating: swrIsValidating,\n    isLoading: swrIsLoading,\n    error: swrError,\n    mutate: swrMutate,\n  } = useSWR(\n    !triggerInfinite && !!fetcher && enabled ? pagesCacheKey : null,\n    cacheKeyParams => {\n      // @ts-ignore\n      const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n      // @ts-ignore\n      return fetcher?.(requestParams);\n    },\n    { keepPreviousData },\n  );\n\n  const {\n    data: swrInfiniteData,\n    isLoading: swrInfiniteIsLoading,\n    isValidating: swrInfiniteIsValidating,\n    error: swrInfiniteError,\n    size,\n    setSize,\n    mutate: swrInfiniteMutate,\n  } = useSWRInfinite(\n    pageIndex => {\n      if (!triggerInfinite || !enabled) {\n        return null;\n      }\n\n      return {\n        ...params,\n        ...cacheKeys,\n        initialPage: initialPageRef.current + pageIndex,\n        pageSize: pageSizeRef.current,\n      };\n    },\n    cacheKeyParams => {\n      // @ts-ignore\n      const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n      // @ts-ignore\n      return fetcher?.(requestParams);\n    },\n  );\n\n  const page = useMemo(() => {\n    if (triggerInfinite) {\n      return size;\n    }\n    return paginatedPage;\n  }, [triggerInfinite, size, paginatedPage]);\n\n  const fetchPage: ValueOrSetter<number> = useCallback(\n    numberOrgFn => {\n      if (triggerInfinite) {\n        void setSize(numberOrgFn);\n        return;\n      }\n      return setPaginatedPage(numberOrgFn);\n    },\n    [setSize],\n  );\n\n  const data = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.map(a => a?.data).flat() ?? [];\n    }\n    return swrData?.data ?? [];\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const count = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.[swrInfiniteData?.length - 1]?.total_count || 0;\n    }\n    return swrData?.total_count ?? 0;\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const isLoading = triggerInfinite ? swrInfiniteIsLoading : swrIsLoading;\n  const isFetching = triggerInfinite ? swrInfiniteIsValidating : swrIsValidating;\n  const isError = !!(triggerInfinite ? swrInfiniteError : swrError);\n  /**\n   * Helpers\n   */\n  const fetchNext = useCallback(() => {\n    fetchPage(n => Math.max(0, n + 1));\n  }, [fetchPage]);\n\n  const fetchPrevious = useCallback(() => {\n    fetchPage(n => Math.max(0, n - 1));\n  }, [fetchPage]);\n\n  const offsetCount = (initialPageRef.current - 1) * pageSizeRef.current;\n\n  const pageCount = Math.ceil((count - offsetCount) / pageSizeRef.current);\n  const hasNextPage = count - offsetCount * pageSizeRef.current > page * pageSizeRef.current;\n  const hasPreviousPage = (page - 1) * pageSizeRef.current > offsetCount * pageSizeRef.current;\n\n  const setData: CacheSetter = triggerInfinite\n    ? value =>\n        swrInfiniteMutate(value, {\n          revalidate: false,\n        })\n    : value =>\n        swrMutate(value, {\n          revalidate: false,\n        });\n\n  const revalidate = triggerInfinite ? () => swrInfiniteMutate() : () => swrMutate();\n\n  return {\n    data,\n    count,\n    isLoading,\n    isFetching,\n    isError,\n    page,\n    pageCount,\n    fetchPage,\n    fetchNext,\n    fetchPrevious,\n    hasNextPage,\n    hasPreviousPage,\n    // Let the hook return type define this type\n    revalidate: revalidate as any,\n    // Let the hook return type define this type\n    setData: setData as any,\n  };\n};\n", "import type {\n  ClerkPaginatedResponse,\n  ClerkPaginationParams,\n  GetDomainsParams,\n  GetInvitationsParams,\n  GetMembershipRequestParams,\n  GetMembershipsParams,\n  GetMembersParams,\n  GetPendingInvitationsParams,\n  OrganizationDomainResource,\n  OrganizationInvitationResource,\n  OrganizationMembershipRequestResource,\n  OrganizationMembershipResource,\n  OrganizationResource,\n} from '@clerk/types';\n\nimport { deprecated } from '../../deprecated';\nimport { useSWR } from '../clerk-swr';\nimport { useClerkInstanceContext, useOrganizationContext, useSessionContext } from '../contexts';\nimport type { PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\ntype UseOrganizationParams = {\n  /**\n   * @deprecated Use `invitations` instead\n   */\n  invitationList?: GetPendingInvitationsParams;\n  /**\n   * @deprecated Use `memberships` instead\n   */\n  membershipList?: GetMembershipsParams;\n  domains?:\n    | true\n    | (GetDomainsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  membershipRequests?:\n    | true\n    | (GetMembershipRequestParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  memberships?:\n    | true\n    | (GetMembersParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n\n  invitations?:\n    | true\n    | (GetInvitationsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n};\n\ntype UseOrganization = <T extends UseOrganizationParams>(\n  params?: T,\n) =>\n  | {\n      isLoaded: false;\n      organization: undefined;\n      /**\n       * @deprecated Use `invitations` instead\n       */\n      invitationList: undefined;\n      /**\n       * @deprecated Use `memberships` instead\n       */\n      membershipList: undefined;\n      membership: undefined;\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n    }\n  | {\n      isLoaded: true;\n      organization: OrganizationResource;\n      /**\n       * @deprecated Use `invitations` instead\n       */\n      invitationList: undefined;\n      /**\n       * @deprecated Use `memberships` instead\n       */\n      membershipList: undefined;\n      membership: undefined;\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n    }\n  | {\n      isLoaded: boolean;\n      organization: OrganizationResource | null;\n      /**\n       * @deprecated Use `invitations` instead\n       */\n      invitationList: OrganizationInvitationResource[] | null | undefined;\n      /**\n       * @deprecated Use `memberships` instead\n       */\n      membershipList: OrganizationMembershipResource[] | null | undefined;\n      membership: OrganizationMembershipResource | null | undefined;\n      domains: PaginatedResources<\n        OrganizationDomainResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      membershipRequests: PaginatedResources<\n        OrganizationMembershipRequestResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      memberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['memberships'] extends { infinite: true } ? true : false\n      > | null;\n      invitations: PaginatedResources<\n        OrganizationInvitationResource,\n        T['invitations'] extends { infinite: true } ? true : false\n      > | null;\n    };\n\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\nexport const useOrganization: UseOrganization = params => {\n  const {\n    invitationList: invitationListParams,\n    membershipList: membershipListParams,\n    domains: domainListParams,\n    membershipRequests: membershipRequestsListParams,\n    memberships: membersListParams,\n    invitations: invitationsListParams,\n  } = params || {};\n  const { organization, lastOrganizationMember, lastOrganizationInvitation } = useOrganizationContext();\n  const session = useSessionContext();\n\n  const domainSafeValues = useWithSafeValues(domainListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n    enrollmentMode: undefined,\n  });\n\n  const membershipRequestSafeValues = useWithSafeValues(membershipRequestsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const membersSafeValues = useWithSafeValues(membersListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    role: undefined,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const invitationsSafeValues = useWithSafeValues(invitationsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: ['pending'],\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n\n  const shouldFetch = !!(clerk.loaded && session && organization);\n\n  const domainParams =\n    typeof domainListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: domainSafeValues.initialPage,\n          pageSize: domainSafeValues.pageSize,\n          enrollmentMode: domainSafeValues.enrollmentMode,\n        };\n\n  const membershipRequestParams =\n    typeof membershipRequestsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membershipRequestSafeValues.initialPage,\n          pageSize: membershipRequestSafeValues.pageSize,\n          status: membershipRequestSafeValues.status,\n        };\n\n  const membersParams =\n    typeof membersListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membersSafeValues.initialPage,\n          pageSize: membersSafeValues.pageSize,\n          role: membersSafeValues.role,\n        };\n\n  const invitationsParams =\n    typeof invitationsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: invitationsSafeValues.initialPage,\n          pageSize: invitationsSafeValues.pageSize,\n          status: invitationsSafeValues.status,\n        };\n\n  const domains = usePagesOrInfinite<GetDomainsParams, ClerkPaginatedResponse<OrganizationDomainResource>>(\n    {\n      ...domainParams,\n    },\n    organization?.getDomains,\n    {\n      keepPreviousData: domainSafeValues.keepPreviousData,\n      infinite: domainSafeValues.infinite,\n      enabled: !!domainParams,\n    },\n    {\n      type: 'domains',\n      organizationId: organization?.id,\n    },\n  );\n\n  const membershipRequests = usePagesOrInfinite<\n    GetMembershipRequestParams,\n    ClerkPaginatedResponse<OrganizationMembershipRequestResource>\n  >(\n    {\n      ...membershipRequestParams,\n    },\n    organization?.getMembershipRequests,\n    {\n      keepPreviousData: membershipRequestSafeValues.keepPreviousData,\n      infinite: membershipRequestSafeValues.infinite,\n      enabled: !!membershipRequestParams,\n    },\n    {\n      type: 'membershipRequests',\n      organizationId: organization?.id,\n    },\n  );\n\n  const memberships = usePagesOrInfinite<GetMembersParams, ClerkPaginatedResponse<OrganizationMembershipResource>>(\n    {\n      ...membersParams,\n      paginated: true,\n    } as any,\n    organization?.getMemberships as unknown as any,\n    {\n      keepPreviousData: membersSafeValues.keepPreviousData,\n      infinite: membersSafeValues.infinite,\n      enabled: !!membersParams,\n    },\n    {\n      type: 'members',\n      organizationId: organization?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<GetInvitationsParams, ClerkPaginatedResponse<OrganizationInvitationResource>>(\n    {\n      ...invitationsParams,\n    },\n    organization?.getInvitations,\n    {\n      keepPreviousData: invitationsSafeValues.keepPreviousData,\n      infinite: invitationsSafeValues.infinite,\n      enabled: !!invitationsParams,\n    },\n    {\n      type: 'invitations',\n      organizationId: organization?.id,\n    },\n  );\n\n  // Some gymnastics to adhere to the rules of hooks\n  // We need to make sure useSWR is called on every render\n  const pendingInvitations = !clerk.loaded\n    ? () => [] as OrganizationInvitationResource[]\n    : () => clerk.organization?.getPendingInvitations(invitationListParams);\n\n  const currentOrganizationMemberships = !clerk.loaded\n    ? () => [] as OrganizationMembershipResource[]\n    : () => clerk.organization?.getMemberships(membershipListParams);\n\n  if (invitationListParams) {\n    deprecated('invitationList in useOrganization', 'Use the `invitations` property and return value instead.');\n  }\n\n  const {\n    data: invitationList,\n    isValidating: isInvitationsLoading,\n    mutate: mutateInvitationList,\n  } = useSWR(\n    shouldFetch && invitationListParams\n      ? cacheKey('invites', organization, lastOrganizationInvitation, invitationListParams)\n      : null,\n    pendingInvitations,\n  );\n\n  if (membershipListParams) {\n    deprecated('membershipList in useOrganization', 'Use the `memberships` property and return value instead.');\n  }\n\n  const {\n    data: membershipList,\n    isValidating: isMembershipsLoading,\n    mutate: mutateMembershipList,\n  } = useSWR(\n    shouldFetch && membershipListParams\n      ? cacheKey('memberships', organization, lastOrganizationMember, membershipListParams)\n      : null,\n    currentOrganizationMemberships,\n  );\n\n  if (organization === undefined) {\n    return {\n      isLoaded: false,\n      organization: undefined,\n      invitationList: undefined,\n      membershipList: undefined,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n    };\n  }\n\n  if (organization === null) {\n    return {\n      isLoaded: true,\n      organization: null,\n      invitationList: null,\n      membershipList: null,\n      membership: null,\n      domains: null,\n      membershipRequests: null,\n      memberships: null,\n      invitations: null,\n    };\n  }\n\n  /** In SSR context we include only the organization object when loadOrg is set to true. */\n  if (!clerk.loaded && organization) {\n    return {\n      isLoaded: true,\n      organization,\n      invitationList: undefined,\n      membershipList: undefined,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n    };\n  }\n\n  return {\n    isLoaded: !isMembershipsLoading && !isInvitationsLoading,\n    organization,\n    membershipList,\n    membership: getCurrentOrganizationMembership(session!.user.organizationMemberships, organization.id), // your membership in the current org\n    invitationList,\n    unstable__mutate: () => {\n      void mutateMembershipList();\n      void mutateInvitationList();\n    },\n    domains,\n    membershipRequests,\n    memberships,\n    invitations,\n  };\n};\n\nfunction getCurrentOrganizationMembership(\n  organizationMemberships: OrganizationMembershipResource[],\n  activeOrganizationId: string,\n) {\n  return organizationMemberships.find(\n    organizationMembership => organizationMembership.organization.id === activeOrganizationId,\n  );\n}\n\nfunction cacheKey(\n  type: 'memberships' | 'invites',\n  organization: OrganizationResource,\n  resource: OrganizationInvitationResource | OrganizationMembershipResource | null | undefined,\n  pagination: ClerkPaginationParams,\n) {\n  return [type, organization.id, resource?.id, resource?.updatedAt, pagination.offset, pagination.limit]\n    .filter(Boolean)\n    .join('-');\n}\n", "import type {\n  ClerkPaginatedResponse,\n  CreateOrganizationParams,\n  GetUserOrganizationInvitationsParams,\n  GetUserOrganizationMembershipParams,\n  GetUserOrganizationSuggestionsParams,\n  OrganizationMembershipResource,\n  OrganizationResource,\n  OrganizationSuggestionResource,\n  SetActive,\n  UserOrganizationInvitationResource,\n} from '@clerk/types';\n\nimport { deprecatedObjectProperty } from '../../deprecated';\nimport { useClerkInstanceContext, useUserContext } from '../contexts';\nimport type { PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\ntype UseOrganizationListParams = {\n  userMemberships?:\n    | true\n    | (GetUserOrganizationMembershipParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  userInvitations?:\n    | true\n    | (GetUserOrganizationInvitationsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  userSuggestions?:\n    | true\n    | (GetUserOrganizationSuggestionsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n};\n\ntype OrganizationList = ReturnType<typeof createOrganizationList>;\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\ntype UseOrganizationList = <T extends UseOrganizationListParams>(\n  params?: T,\n) =>\n  | {\n      isLoaded: false;\n      /**\n       * @deprecated Use userMemberships instead\n       */\n      organizationList: undefined;\n      createOrganization: undefined;\n      setActive: undefined;\n      userMemberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      userInvitations: PaginatedResourcesWithDefault<UserOrganizationInvitationResource>;\n      userSuggestions: PaginatedResourcesWithDefault<OrganizationSuggestionResource>;\n    }\n  | {\n      isLoaded: boolean;\n      /**\n       * @deprecated Use userMemberships instead\n       */\n      organizationList: OrganizationList;\n      createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource>;\n      setActive: SetActive;\n      userMemberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['userMemberships'] extends { infinite: true } ? true : false\n      >;\n      userInvitations: PaginatedResources<\n        UserOrganizationInvitationResource,\n        T['userInvitations'] extends { infinite: true } ? true : false\n      >;\n      userSuggestions: PaginatedResources<\n        OrganizationSuggestionResource,\n        T['userSuggestions'] extends { infinite: true } ? true : false\n      >;\n    };\n\nexport const useOrganizationList: UseOrganizationList = params => {\n  const { userMemberships, userInvitations, userSuggestions } = params || {};\n\n  const userMembershipsSafeValues = useWithSafeValues(userMemberships, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userInvitationsSafeValues = useWithSafeValues(userInvitations, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userSuggestionsSafeValues = useWithSafeValues(userSuggestions, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n  const user = useUserContext();\n\n  const userMembershipsParams =\n    typeof userMemberships === 'undefined'\n      ? undefined\n      : {\n          initialPage: userMembershipsSafeValues.initialPage,\n          pageSize: userMembershipsSafeValues.pageSize,\n        };\n\n  const userInvitationsParams =\n    typeof userInvitations === 'undefined'\n      ? undefined\n      : {\n          initialPage: userInvitationsSafeValues.initialPage,\n          pageSize: userInvitationsSafeValues.pageSize,\n          status: userInvitationsSafeValues.status,\n        };\n\n  const userSuggestionsParams =\n    typeof userSuggestions === 'undefined'\n      ? undefined\n      : {\n          initialPage: userSuggestionsSafeValues.initialPage,\n          pageSize: userSuggestionsSafeValues.pageSize,\n          status: userSuggestionsSafeValues.status,\n        };\n\n  const isClerkLoaded = !!(clerk.loaded && user);\n\n  const memberships = usePagesOrInfinite<\n    GetUserOrganizationMembershipParams,\n    ClerkPaginatedResponse<OrganizationMembershipResource>\n  >(\n    {\n      ...userMembershipsParams,\n      paginated: true,\n    } as any,\n    user?.getOrganizationMemberships as unknown as any,\n    {\n      keepPreviousData: userMembershipsSafeValues.keepPreviousData,\n      infinite: userMembershipsSafeValues.infinite,\n      enabled: !!userMembershipsParams,\n    },\n    {\n      type: 'userMemberships',\n      userId: user?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<\n    GetUserOrganizationInvitationsParams,\n    ClerkPaginatedResponse<UserOrganizationInvitationResource>\n  >(\n    {\n      ...userInvitationsParams,\n    },\n    user?.getOrganizationInvitations,\n    {\n      keepPreviousData: userInvitationsSafeValues.keepPreviousData,\n      infinite: userInvitationsSafeValues.infinite,\n      enabled: !!userInvitationsParams,\n    },\n    {\n      type: 'userInvitations',\n      userId: user?.id,\n    },\n  );\n\n  const suggestions = usePagesOrInfinite<\n    GetUserOrganizationSuggestionsParams,\n    ClerkPaginatedResponse<OrganizationSuggestionResource>\n  >(\n    {\n      ...userSuggestionsParams,\n    },\n    user?.getOrganizationSuggestions,\n    {\n      keepPreviousData: userSuggestionsSafeValues.keepPreviousData,\n      infinite: userSuggestionsSafeValues.infinite,\n      enabled: !!userSuggestionsParams,\n    },\n    {\n      type: 'userSuggestions',\n      userId: user?.id,\n    },\n  );\n\n  // TODO: Properly check for SSR user values\n  if (!isClerkLoaded) {\n    return {\n      isLoaded: false,\n      organizationList: undefined,\n      createOrganization: undefined,\n      setActive: undefined,\n      userMemberships: undefinedPaginatedResource,\n      userInvitations: undefinedPaginatedResource,\n      userSuggestions: undefinedPaginatedResource,\n    };\n  }\n\n  const result = {\n    isLoaded: isClerkLoaded,\n    organizationList: createOrganizationList(user.organizationMemberships),\n    setActive: clerk.setActive,\n    createOrganization: clerk.createOrganization,\n    userMemberships: memberships,\n    userInvitations: invitations,\n    userSuggestions: suggestions,\n  };\n  deprecatedObjectProperty(result, 'organizationList', 'Use `userMemberships` instead.');\n\n  return result;\n};\n\nfunction createOrganizationList(organizationMemberships: OrganizationMembershipResource[]) {\n  return organizationMemberships.map(organizationMembership => ({\n    membership: organizationMembership,\n    organization: organizationMembership.organization,\n  }));\n}\n", "import type { CreateOrganizationParams, OrganizationMembershipResource, OrganizationResource } from '@clerk/types';\n\nimport { deprecated } from '../../deprecated';\nimport { useClerkInstanceContext } from '../contexts';\n\ntype UseOrganizationsReturn =\n  | {\n      isLoaded: false;\n\n      /**\n       * @deprecated Use `createOrganization` from `useOrganizationList`\n       * Example: `const {createOrganization} = useOrganizationList()`\n       */\n      createOrganization: undefined;\n\n      /**\n       * @deprecated Use `memberships` from `useOrganization`\n       * Example: `const {memberships} = useOrganization()`\n       */\n      getOrganizationMemberships: undefined;\n\n      /**\n       * @deprecated Use `getOrganization` from `useClerk`\n       * Example: `const {getOrganization} = useClerk()`\n       */\n      getOrganization: undefined;\n    }\n  | {\n      isLoaded: true;\n      /**\n       * @deprecated Use `createOrganization` from `useOrganizationList`\n       * Example: `const {createOrganization} = useOrganizationList()`\n       */\n      createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource>;\n\n      /**\n       * @deprecated Use `memberships` from `useOrganization`\n       * Example: `const {memberships} = useOrganization()`\n       */\n      getOrganizationMemberships: () => Promise<OrganizationMembershipResource[]>;\n\n      /**\n       * @deprecated Use `getOrganization` from `useClerk`\n       * Example: `const {getOrganization} = useClerk()`\n       */\n      getOrganization: (organizationId: string) => Promise<OrganizationResource | undefined>;\n    };\n\ntype UseOrganizations = () => UseOrganizationsReturn;\n\n/**\n * @deprecated Use useOrganizationList, useOrganization, or useClerk instead\n */\nexport const useOrganizations: UseOrganizations = () => {\n  deprecated('useOrganizations', 'Use useOrganizationList, useOrganization, or useClerk instead.');\n  const clerk = useClerkInstanceContext();\n  if (!clerk.loaded) {\n    return {\n      isLoaded: false,\n      createOrganization: undefined,\n      getOrganizationMemberships: undefined,\n      getOrganization: undefined,\n    };\n  }\n\n  return {\n    isLoaded: true,\n    createOrganization: clerk.createOrganization,\n    getOrganizationMemberships: clerk.getOrganizationMemberships,\n    getOrganization: clerk.getOrganization,\n  };\n};\n", "import React from 'react';\n\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import ReactExports, { useRef, useMemo, useCallback, useDebugValue } from 'react';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { serialize, OBJECT, SWRConfig as SWRConfig$1, defaultConfig, withArgs, SWRGlobalState, createCacheHelper, isUndefined, getTimestamp, UNDEFINED, isFunction, revalidateEvents, internalMutate, useIsomorphicLayoutEffect, subscribeCallback, IS_SERVER, rAF, IS_REACT_LEGACY, mergeObjects } from 'swr/_internal';\nexport { mutate, preload, useSWRConfig } from 'swr/_internal';\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = ReactExports.use || ((promise)=>{\n    if (promise.status === 'pending') {\n        throw promise;\n    } else if (promise.status === 'fulfilled') {\n        return promise.value;\n    } else if (promise.status === 'rejected') {\n        throw promise.reason;\n    } else {\n        promise.status = 'pending';\n        promise.then((v)=>{\n            promise.status = 'fulfilled';\n            promise.value = v;\n        }, (e)=>{\n            promise.status = 'rejected';\n            promise.reason = e;\n        });\n        throw promise;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache , compare , suspense , fallbackData , revalidateOnMount , revalidateIfStale , refreshInterval , refreshWhenHidden , refreshWhenOffline , keepPreviousData  } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = serialize(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = useRef(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = useRef(false);\n    // Refs to keep the key and config.\n    const keyRef = useRef(key);\n    const fetcherRef = useRef(fetcher);\n    const configRef = useRef(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = createCacheHelper(cache, key);\n    const stateDependencies = useRef({}).current;\n    const fallback = isUndefined(fallbackData) ? config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!isUndefined(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = useMemo(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!isUndefined(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            if (!isUndefined(revalidateIfStale)) return revalidateIfStale;\n            return true;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = mergeObjects(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = useSyncExternalStore(useCallback((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = isUndefined(cachedData) ? fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = useRef(data);\n    const returnedData = keepPreviousData ? isUndefined(cachedData) ? laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !isUndefined(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !isUndefined(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return isUndefined(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return isUndefined(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = isUndefined(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = isUndefined(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = useCallback(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if (isUndefined(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && isUndefined(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    getTimestamp()\n                ];\n            }\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = UNDEFINED;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!isUndefined(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError  } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || isFunction(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const boundMutate = useCallback(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return internalMutate(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    useIsomorphicLayoutEffect(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!isUndefined(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    useIsomorphicLayoutEffect(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(UNDEFINED, WITH_DEDUPE);\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        let nextFocusRevalidatedAt = 0;\n        const onRevalidate = (type, opts = {})=>{\n            if (type == revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = subscribeCallback(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if (isUndefined(data) || IS_SERVER) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                rAF(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    useIsomorphicLayoutEffect(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = isFunction(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    useDebugValue(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && isUndefined(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any initial data. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!IS_REACT_LEGACY && IS_SERVER) {\n            throw new Error('Fallback data is required when using suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!isUndefined(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if (isUndefined(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!isUndefined(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    return {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n};\nconst SWRConfig = OBJECT.defineProperty(SWRConfig$1, 'defaultValue', {\n    value: defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (!data) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = withArgs(useSWRHandler);\n\n// useSWR\n\nexport { SWRConfig, useSWR as default, unstable_serialize };\n", "import React, { useEffect, useLayoutEffect, createContext, useContext, useMemo, useRef, createElement, useState, useCallback } from 'react';\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const constructor = arg && arg.constructor;\n    const isDate = constructor == Date;\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && constructor != RegExp) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (constructor == Array) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (constructor == OBJECT) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = typeof window != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\n\nconst IS_REACT_LEGACY = !React.useId;\nconst IS_SERVER = !isWindowDefined || 'Deno' in window;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? useEffect : useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\n\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\nvar constants = {\n  __proto__: null,\n  ERROR_REVALIDATE_EVENT: ERROR_REVALIDATE_EVENT,\n  FOCUS_EVENT: FOCUS_EVENT,\n  MUTATE_EVENT: MUTATE_EVENT,\n  RECONNECT_EVENT: RECONNECT_EVENT\n};\n\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const revalidate = options.revalidate !== false;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (// Skip the special useSWRInfinite and useSWRSubscription keys.\n            !/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const revalidators = EVENT_REVALIDATORS[key];\n        const startRevalidate = ()=>{\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                data = committedData;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    data = populateCache(data, committedData);\n                }\n                // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                set({\n                    data,\n                    error: UNDEFINED,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        const res = await startRevalidate();\n        // The mutation and revalidation are ended, we can clear it since the data is\n        // not an optimistic value anymore.\n        set({\n            _c: UNDEFINED\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return populateCache ? res : data;\n    }\n}\n\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = {};\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = {};\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    {},\n                    {},\n                    {},\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = (currentData, newData)=>stableHash(currentData) == stableHash(newData);\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, // use web preset by default\npreset);\n\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1 , fallback: f1  } = a;\n        const { use: u2 , fallback: f2  } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\n\nconst SWRConfigContext = createContext({});\nconst SWRConfig = (props)=>{\n    const { value  } = props;\n    const parentConfig = useContext(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = useMemo(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = useMemo(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = useRef(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return createElement(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = React;\n    }\n};\n\nconst normalize = (args)=>{\n    return isFunction(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return mergeObjects(defaultConfig, useContext(SWRConfigContext));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = serialize(key_);\n    const [, , , PRELOAD] = SWRGlobalState.get(cache);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = serialize(key_);\n            const [, , , PRELOAD] = SWRGlobalState.get(cache);\n            const req = PRELOAD[key];\n            if (isUndefined(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = mergeConfigs(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use  } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n/**\n * An implementation of state with dependency-tracking.\n */ const useStateWithDeps = (state)=>{\n    const rerender = useState({})[1];\n    const unmountedRef = useRef(false);\n    const stateRef = useRef(state);\n    // If a state property (data, error, or isValidating) is accessed by the render\n    // function, we mark the property as a dependency so if it is updated again\n    // in the future, we trigger a rerender.\n    // This is also known as dependency-tracking.\n    const stateDependenciesRef = useRef({\n        data: false,\n        error: false,\n        isValidating: false\n    });\n    /**\n   * @param payload To change stateRef, pass the values explicitly to setState:\n   * @example\n   * ```js\n   * setState({\n   *   isValidating: false\n   *   data: newData // set data to newData\n   *   error: undefined // set error to undefined\n   * })\n   *\n   * setState({\n   *   isValidating: false\n   *   data: undefined // set data to undefined\n   *   error: err // set error to err\n   * })\n   * ```\n   */ const setState = useCallback((payload)=>{\n        let shouldRerender = false;\n        const currentState = stateRef.current;\n        for(const _ in payload){\n            const k = _;\n            // If the property has changed, update the state and mark rerender as\n            // needed.\n            if (currentState[k] !== payload[k]) {\n                currentState[k] = payload[k];\n                // If the property is accessed by the component, a rerender should be\n                // triggered.\n                if (stateDependenciesRef.current[k]) {\n                    shouldRerender = true;\n                }\n            }\n        }\n        if (shouldRerender && !unmountedRef.current) {\n            if (IS_REACT_LEGACY) {\n                rerender({});\n            } else {\n                React.startTransition(()=>rerender({}));\n            }\n        }\n    }, [\n        rerender\n    ]);\n    useIsomorphicLayoutEffect(()=>{\n        unmountedRef.current = false;\n        return ()=>{\n            unmountedRef.current = true;\n        };\n    });\n    return [\n        stateRef,\n        stateDependenciesRef.current,\n        setState\n    ];\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\nexport { IS_REACT_LEGACY, IS_SERVER, OBJECT, SWRConfig, SWRGlobalState, UNDEFINED, cache, compare, createCacheHelper, defaultConfig, defaultConfigOptions, getTimestamp, hasRequestAnimationFrame, initCache, internalMutate, isDocumentDefined, isFunction, isPromiseLike, isUndefined, isWindowDefined, mergeConfigs, mergeObjects, mutate, noop, normalize, preload, preset, rAF, constants as revalidateEvents, serialize, slowConnection, stableHash, subscribeCallback, useIsomorphicLayoutEffect, useSWRConfig, useStateWithDeps, withArgs, withMiddleware };\n", "import { useRef, useCallback } from 'react';\nimport useS<PERSON> from 'swr';\nimport { serialize, withMiddleware, createCacheHelper, isUndefined, useIsomorphicLayoutEffect, UNDEFINED, isFunction } from 'swr/_internal';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\n\nconst INFINITE_PREFIX = '$inf$';\nconst getFirstPageKey = (getKey)=>{\n    return serialize(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = (getKey)=>{\n    return INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n\n// We have to several type castings here because `useSWRInfinite` is a special\n// const INFINITE_PREFIX = '$inf$'\nconst EMPTY_PROMISE = Promise.resolve();\n// export const unstable_serialize = (getKey: SWRInfiniteKeyLoader) => {\n//   return INFINITE_PREFIX + getFirstPageKey(getKey)\n// }\nconst infinite = (useSWRNext)=>(getKey, fn, config)=>{\n        const didMountRef = useRef(false);\n        const { cache , initialSize =1 , revalidateAll =false , persistSize =false , revalidateFirstPage =true , revalidateOnMount =false , parallel =false  } = config;\n        // The serialized key of the first page. This key will be used to store\n        // metadata of this SWR infinite hook.\n        let infiniteKey;\n        try {\n            infiniteKey = getFirstPageKey(getKey);\n            if (infiniteKey) infiniteKey = INFINITE_PREFIX + infiniteKey;\n        } catch (err) {\n        // Not ready yet.\n        }\n        const [get, set, subscribeCache] = createCacheHelper(cache, infiniteKey);\n        const getSnapshot = useCallback(()=>{\n            const size = isUndefined(get()._l) ? initialSize : get()._l;\n            return size;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            cache,\n            infiniteKey,\n            initialSize\n        ]);\n        useSyncExternalStore(useCallback((callback)=>{\n            if (infiniteKey) return subscribeCache(infiniteKey, ()=>{\n                callback();\n            });\n            return ()=>{};\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            cache,\n            infiniteKey\n        ]), getSnapshot, getSnapshot);\n        const resolvePageSize = useCallback(()=>{\n            const cachedPageSize = get()._l;\n            return isUndefined(cachedPageSize) ? initialSize : cachedPageSize;\n        // `cache` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            initialSize\n        ]);\n        // keep the last page size to restore it with the persistSize option\n        const lastPageSizeRef = useRef(resolvePageSize());\n        // When the page key changes, we reset the page size if it's not persisted\n        useIsomorphicLayoutEffect(()=>{\n            if (!didMountRef.current) {\n                didMountRef.current = true;\n                return;\n            }\n            if (infiniteKey) {\n                // If the key has been changed, we keep the current page size if persistSize is enabled\n                // Otherwise, we reset the page size to cached pageSize\n                set({\n                    _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n                });\n            }\n        // `initialSize` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            cache\n        ]);\n        // Needs to check didMountRef during mounting, not in the fetcher\n        const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n        // Actual SWR hook to load all pages in one fetcher.\n        const swr = useSWRNext(infiniteKey, async (key)=>{\n            // get the revalidate context\n            const forceRevalidateAll = get()._i;\n            // return an array of page data\n            const data = [];\n            const pageSize = resolvePageSize();\n            const [getCache] = createCacheHelper(cache, key);\n            const cacheData = getCache().data;\n            const revalidators = [];\n            let previousPageData = null;\n            for(let i = 0; i < pageSize; ++i){\n                const [pageKey, pageArg] = serialize(getKey(i, parallel ? null : previousPageData));\n                if (!pageKey) {\n                    break;\n                }\n                const [getSWRCache, setSWRCache] = createCacheHelper(cache, pageKey);\n                // Get the cached page data.\n                let pageData = getSWRCache().data;\n                // should fetch (or revalidate) if:\n                // - `revalidateAll` is enabled\n                // - `mutate()` called\n                // - the cache is missing\n                // - it's the first page and it's not the initial render\n                // - `revalidateOnMount` is enabled and it's on mount\n                // - cache for that page has changed\n                const shouldFetchPage = revalidateAll || forceRevalidateAll || isUndefined(pageData) || revalidateFirstPage && !i && !isUndefined(cacheData) || shouldRevalidateOnMount || cacheData && !isUndefined(cacheData[i]) && !config.compare(cacheData[i], pageData);\n                if (fn && shouldFetchPage) {\n                    const revalidate = async ()=>{\n                        pageData = await fn(pageArg);\n                        setSWRCache({\n                            data: pageData,\n                            _k: pageArg\n                        });\n                        data[i] = pageData;\n                    };\n                    if (parallel) {\n                        revalidators.push(revalidate);\n                    } else {\n                        await revalidate();\n                    }\n                } else {\n                    data[i] = pageData;\n                }\n                if (!parallel) {\n                    previousPageData = pageData;\n                }\n            }\n            // flush all revalidateions in parallel\n            if (parallel) {\n                await Promise.all(revalidators.map((r)=>r()));\n            }\n            // once we executed the data fetching based on the context, clear the context\n            set({\n                _i: UNDEFINED\n            });\n            // return the data\n            return data;\n        }, config);\n        const mutate = useCallback(// eslint-disable-next-line func-names\n        function(data, opts) {\n            // When passing as a boolean, it's explicitly used to disable/enable\n            // revalidation.\n            const options = typeof opts === 'boolean' ? {\n                revalidate: opts\n            } : opts || {};\n            // Default to true.\n            const shouldRevalidate = options.revalidate !== false;\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            if (shouldRevalidate) {\n                if (!isUndefined(data)) {\n                    // We only revalidate the pages that are changed\n                    set({\n                        _i: false\n                    });\n                } else {\n                    // Calling `mutate()`, we revalidate all pages\n                    set({\n                        _i: true\n                    });\n                }\n            }\n            return arguments.length ? swr.mutate(data, {\n                ...options,\n                revalidate: shouldRevalidate\n            }) : swr.mutate();\n        }, // swr.mutate is always the same reference\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache\n        ]);\n        // Extend the SWR API\n        const setSize = useCallback((arg)=>{\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            const [, changeSize] = createCacheHelper(cache, infiniteKey);\n            let size;\n            if (isFunction(arg)) {\n                size = arg(resolvePageSize());\n            } else if (typeof arg == 'number') {\n                size = arg;\n            }\n            if (typeof size != 'number') return EMPTY_PROMISE;\n            changeSize({\n                _l: size\n            });\n            lastPageSizeRef.current = size;\n            // Calculate the page data after the size change.\n            const data = [];\n            const [getInfiniteCache] = createCacheHelper(cache, infiniteKey);\n            let previousPageData = null;\n            for(let i = 0; i < size; ++i){\n                const [pageKey] = serialize(getKey(i, previousPageData));\n                const [getCache] = createCacheHelper(cache, pageKey);\n                // Get the cached page data.\n                const pageData = pageKey ? getCache().data : UNDEFINED;\n                // Call `mutate` with infinte cache data if we can't get it from the page cache.\n                if (isUndefined(pageData)) {\n                    return mutate(getInfiniteCache().data);\n                }\n                data.push(pageData);\n                previousPageData = pageData;\n            }\n            return mutate(data);\n        }, // exclude getKey from the dependencies, which isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache,\n            mutate,\n            resolvePageSize\n        ]);\n        // Use getter functions to avoid unnecessary re-renders caused by triggering\n        // all the getters of the returned swr object.\n        return {\n            size: resolvePageSize(),\n            setSize,\n            mutate,\n            get data () {\n                return swr.data;\n            },\n            get error () {\n                return swr.error;\n            },\n            get isValidating () {\n                return swr.isValidating;\n            },\n            get isLoading () {\n                return swr.isLoading;\n            }\n        };\n    };\nconst useSWRInfinite = withMiddleware(useSWR, infinite);\n\nexport { useSWRInfinite as default, infinite, unstable_serialize };\n", "import { ClerkInstanceContext, useClerkInstanceContext } from '@clerk/shared/react';\n\nexport const [IsomorphicClerkContext, useIsomorphicClerkContext] = [ClerkInstanceContext, useClerkInstanceContext];\n", "import React from 'react';\n\nimport { assertWrappedByClerkProvider } from './assertHelpers';\n\nexport interface StructureContextValue {\n  guaranteedLoaded: boolean;\n}\n\nexport const StructureContextStates = Object.freeze({\n  noGuarantees: Object.freeze({\n    guaranteedLoaded: false,\n  }),\n  guaranteedLoaded: Object.freeze({\n    guaranteedLoaded: true,\n  }),\n});\n\nexport const StructureContext = React.createContext<StructureContextValue | undefined>(undefined);\n\nStructureContext.displayName = 'StructureContext';\n\nconst useStructureContext = (): StructureContextValue => {\n  const structureCtx = React.useContext(StructureContext);\n  assertWrappedByClerkProvider(structureCtx);\n  return structureCtx;\n};\n\nexport const LoadedGuarantee: React.FC<React.PropsWithChildren<unknown>> = ({ children }) => {\n  const structure = useStructureContext();\n  if (structure.guaranteedLoaded) {\n    return <>{children}</>;\n  }\n  return (\n    <StructureContext.Provider value={StructureContextStates.guaranteedLoaded}>{children}</StructureContext.Provider>\n  );\n};\n", "import { noClerkProviderError, noGuaranteedLoadedError } from '../errors';\n\nexport function assertWrappedByClerkProvider(contextVal: unknown): asserts contextVal {\n  if (!contextVal) {\n    throw new Error(noClerkProviderError);\n  }\n}\n\nexport function assertClerkLoadedGuarantee(guarantee: unknown, hookName: string): asserts guarantee {\n  if (!guarantee) {\n    throw new Error(noGuaranteedLoadedError(hookName));\n  }\n}\n", "import { deprecated } from '@clerk/shared/deprecated';\nimport type { ClientResource, InitialState, Resources } from '@clerk/types';\nimport React from 'react';\n\nimport IsomorphicClerk from '../isomorphicClerk';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { deriveState } from '../utils/deriveState';\nimport { AuthContext } from './AuthContext';\nimport { ClientContext } from './ClientContext';\nimport { IsomorphicClerkContext } from './IsomorphicClerkContext';\nimport { OrganizationProvider } from './OrganizationContext';\nimport { SessionContext } from './SessionContext';\nimport { UserContext } from './UserContext';\n\ntype ClerkContextProvider = {\n  isomorphicClerkOptions: IsomorphicClerkOptions;\n  initialState: InitialState | undefined;\n  children: React.ReactNode;\n};\n\nexport type ClerkContextProviderState = Resources;\n\nexport function ClerkContextProvider(props: ClerkContextProvider): JSX.Element | null {\n  const { isomorphicClerkOptions, initialState, children } = props;\n  const { isomorphicClerk: clerk, loaded: clerkLoaded } = useLoadedIsomorphicClerk(isomorphicClerkOptions);\n\n  if (isomorphicClerkOptions.frontendApi) {\n    deprecated('frontendApi', 'Use `publishableKey` instead.');\n  }\n\n  const [state, setState] = React.useState<ClerkContextProviderState>({\n    client: clerk.client as ClientResource,\n    session: clerk.session,\n    user: clerk.user,\n    organization: clerk.organization,\n    lastOrganizationInvitation: null,\n    lastOrganizationMember: null,\n  });\n\n  React.useEffect(() => {\n    return clerk.addListener(e => setState({ ...e }));\n  }, []);\n\n  const derivedState = deriveState(clerkLoaded, state, initialState);\n  const clerkCtx = React.useMemo(() => ({ value: clerk }), [clerkLoaded]);\n  const clientCtx = React.useMemo(() => ({ value: state.client }), [state.client]);\n\n  const {\n    sessionId,\n    session,\n    userId,\n    user,\n    orgId,\n    actor,\n    lastOrganizationInvitation,\n    lastOrganizationMember,\n    organization,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n  } = derivedState;\n\n  const authCtx = React.useMemo(() => {\n    const value = { sessionId, userId, actor, orgId, orgRole, orgSlug, orgPermissions };\n    return { value };\n  }, [sessionId, userId, actor, orgId, orgRole, orgSlug]);\n  const userCtx = React.useMemo(() => ({ value: user }), [userId, user]);\n  const sessionCtx = React.useMemo(() => ({ value: session }), [sessionId, session]);\n  const organizationCtx = React.useMemo(() => {\n    const value = {\n      organization: organization,\n      lastOrganizationInvitation: lastOrganizationInvitation,\n      lastOrganizationMember: lastOrganizationMember,\n    };\n    return { value };\n  }, [orgId, organization, lastOrganizationInvitation, lastOrganizationMember]);\n\n  return (\n    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk\n    <IsomorphicClerkContext.Provider value={clerkCtx}>\n      <ClientContext.Provider value={clientCtx}>\n        <SessionContext.Provider value={sessionCtx}>\n          <OrganizationProvider {...organizationCtx.value}>\n            <AuthContext.Provider value={authCtx}>\n              <UserContext.Provider value={userCtx}>{children}</UserContext.Provider>\n            </AuthContext.Provider>\n          </OrganizationProvider>\n        </SessionContext.Provider>\n      </ClientContext.Provider>\n    </IsomorphicClerkContext.Provider>\n  );\n}\n\nconst useLoadedIsomorphicClerk = (options: IsomorphicClerkOptions) => {\n  const [loaded, setLoaded] = React.useState(false);\n  const isomorphicClerk = React.useMemo(() => IsomorphicClerk.getOrCreateInstance(options), []);\n\n  React.useEffect(() => {\n    isomorphicClerk.__unstable__updateProps({ appearance: options.appearance });\n  }, [options.appearance]);\n\n  React.useEffect(() => {\n    isomorphicClerk.__unstable__updateProps({ options });\n  }, [options.localization]);\n\n  React.useEffect(() => {\n    isomorphicClerk.addOnLoaded(() => setLoaded(true));\n  }, []);\n\n  React.useEffect(() => {\n    return () => {\n      IsomorphicClerk.clearInstance();\n    };\n  }, []);\n\n  return { isomorphicClerk, loaded };\n};\n", "import { inBrowser } from '@clerk/shared/browser';\nimport { deprecated } from '@clerk/shared/deprecated';\nimport { handleValueOrFn } from '@clerk/shared/handleValueOrFn';\nimport type {\n  ActiveSessionResource,\n  AuthenticateWithGoogleOneTapParams,\n  AuthenticateWithMetamaskParams,\n  BeforeEmitCallback,\n  Clerk,\n  ClientResource,\n  CreateOrganizationParams,\n  CreateOrganizationProps,\n  DomainOrProxyUrl,\n  GoogleOneTapProps,\n  HandleEmailLinkVerificationParams,\n  HandleMagicLinkVerificationParams,\n  HandleOAuthCallbackParams,\n  InstanceType,\n  ListenerCallback,\n  LoadedClerk,\n  OrganizationListProps,\n  OrganizationMembershipResource,\n  OrganizationProfileProps,\n  OrganizationResource,\n  OrganizationSwitcherProps,\n  RedirectOptions,\n  SDKMetadata,\n  SetActiveParams,\n  SignInProps,\n  SignInRedirectOptions,\n  SignInResource,\n  SignOut,\n  SignOutCallback,\n  SignOutOptions,\n  SignUpProps,\n  SignUpRedirectOptions,\n  SignUpResource,\n  UnsubscribeCallback,\n  UserButtonProps,\n  UserProfileProps,\n  UserResource,\n} from '@clerk/types';\n\nimport { unsupportedNonBrowserDomainOrProxyUrlFunction } from './errors';\nimport type {\n  BrowserClerk,\n  BrowserClerkConstructor,\n  ClerkProp,\n  HeadlessBrowserClerk,\n  HeadlessBrowserClerkConstrutor,\n  IsomorphicClerkOptions,\n} from './types';\nimport { isConstructor, loadClerkJsScript } from './utils';\n\nexport interface Global {\n  Clerk?: HeadlessBrowserClerk | BrowserClerk;\n}\n\ndeclare const global: Global;\n\ntype GenericFunction<TArgs = never> = (...args: TArgs[]) => unknown;\n\ntype MethodName<T> = {\n  [P in keyof T]: T[P] extends GenericFunction ? P : never;\n}[keyof T];\n\ntype MethodCallback = () => Promise<unknown> | unknown;\n\ntype IsomorphicLoadedClerk = Omit<\n  LoadedClerk,\n  /**\n   * Override ClerkJS methods in order to support premountMethodCalls\n   */\n  | 'buildSignInUrl'\n  | 'buildSignUpUrl'\n  | 'buildUserProfileUrl'\n  | 'buildCreateOrganizationUrl'\n  | 'buildOrganizationProfileUrl'\n  | 'buildHomeUrl'\n  | 'buildUrlWithAuth'\n  | 'redirectWithAuth'\n  | 'redirectToSignIn'\n  | 'redirectToSignUp'\n  | 'handleRedirectCallback'\n  | 'handleGoogleOneTapCallback'\n  | 'handleUnauthenticated'\n  | 'authenticateWithMetamask'\n  | 'authenticateWithGoogleOneTap'\n  | 'createOrganization'\n  | 'getOrganization'\n  | 'mountUserButton'\n  | 'mountOrganizationList'\n  | 'mountOrganizationSwitcher'\n  | 'mountOrganizationProfile'\n  | 'mountCreateOrganization'\n  | 'mountSignUp'\n  | 'mountSignIn'\n  | 'mountUserProfile'\n  | 'client'\n  | 'getOrganizationMemberships'\n> & {\n  // TODO: Align return type\n  redirectWithAuth: (...args: Parameters<Clerk['redirectWithAuth']>) => void;\n  // TODO: Align return type\n  redirectToSignIn: (options: SignInRedirectOptions) => void;\n  // TODO: Align return type\n  redirectToSignUp: (options: SignUpRedirectOptions) => void;\n  // TODO: Align return type and parms\n  handleRedirectCallback: (params: HandleOAuthCallbackParams) => void;\n  handleGoogleOneTapCallback: (signInOrUp: SignInResource | SignUpResource, params: HandleOAuthCallbackParams) => void;\n  handleUnauthenticated: () => void;\n  // TODO: Align Promise unknown\n  authenticateWithMetamask: (params: AuthenticateWithMetamaskParams) => Promise<void>;\n  authenticateWithGoogleOneTap: (\n    params: AuthenticateWithGoogleOneTapParams,\n  ) => Promise<SignInResource | SignUpResource>;\n  // TODO: Align return type (maybe not possible or correct)\n  createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource | void>;\n  // TODO: Align return type (maybe not possible or correct)\n  getOrganization: (organizationId: string) => Promise<OrganizationResource | void>;\n\n  // TODO: Align return type\n  buildSignInUrl: (opts?: RedirectOptions) => string | void;\n  // TODO: Align return type\n  buildSignUpUrl: (opts?: RedirectOptions) => string | void;\n  // TODO: Align return type\n  buildUserProfileUrl: () => string | void;\n  // TODO: Align return type\n  buildCreateOrganizationUrl: () => string | void;\n  // TODO: Align return type\n  buildOrganizationProfileUrl: () => string | void;\n  // TODO: Align return type\n  buildHomeUrl: () => string | void;\n  // TODO: Align return type\n  buildUrlWithAuth: (to: string) => string | void;\n\n  // TODO: Align optional props\n  mountUserButton: (node: HTMLDivElement, props: UserButtonProps) => void;\n  mountOrganizationList: (node: HTMLDivElement, props: OrganizationListProps) => void;\n  mountOrganizationSwitcher: (node: HTMLDivElement, props: OrganizationSwitcherProps) => void;\n  mountOrganizationProfile: (node: HTMLDivElement, props: OrganizationProfileProps) => void;\n  mountCreateOrganization: (node: HTMLDivElement, props: CreateOrganizationProps) => void;\n  mountSignUp: (node: HTMLDivElement, props: SignUpProps) => void;\n  mountSignIn: (node: HTMLDivElement, props: SignInProps) => void;\n  mountUserProfile: (node: HTMLDivElement, props: UserProfileProps) => void;\n  client: ClientResource | undefined;\n\n  getOrganizationMemberships: () => Promise<OrganizationMembershipResource[] | void>;\n};\n\nexport default class IsomorphicClerk implements IsomorphicLoadedClerk {\n  private readonly mode: 'browser' | 'server';\n  private readonly options: IsomorphicClerkOptions;\n  private readonly Clerk: ClerkProp;\n  private clerkjs: BrowserClerk | HeadlessBrowserClerk | null = null;\n  private preopenOneTap?: null | GoogleOneTapProps = null;\n  private preopenSignIn?: null | SignInProps = null;\n  private preopenSignUp?: null | SignUpProps = null;\n  private preopenUserProfile?: null | UserProfileProps = null;\n  private preopenOrganizationProfile?: null | OrganizationProfileProps = null;\n  private preopenCreateOrganization?: null | CreateOrganizationProps = null;\n  private premountSignInNodes = new Map<HTMLDivElement, SignInProps>();\n  private premountSignUpNodes = new Map<HTMLDivElement, SignUpProps>();\n  private premountUserProfileNodes = new Map<HTMLDivElement, UserProfileProps>();\n  private premountUserButtonNodes = new Map<HTMLDivElement, UserButtonProps>();\n  private premountOrganizationProfileNodes = new Map<HTMLDivElement, OrganizationProfileProps>();\n  private premountCreateOrganizationNodes = new Map<HTMLDivElement, CreateOrganizationProps>();\n  private premountOrganizationSwitcherNodes = new Map<HTMLDivElement, OrganizationSwitcherProps>();\n  private premountOrganizationListNodes = new Map<HTMLDivElement, OrganizationListProps>();\n  private premountMethodCalls = new Map<MethodName<BrowserClerk>, MethodCallback>();\n  private loadedListeners: Array<() => void> = [];\n\n  #loaded = false;\n  #domain: DomainOrProxyUrl['domain'];\n  #proxyUrl: DomainOrProxyUrl['proxyUrl'];\n  #frontendApi: string | undefined;\n  #publishableKey: string | undefined;\n\n  get publishableKey(): string | undefined {\n    return this.#publishableKey;\n  }\n\n  get loaded(): boolean {\n    return this.#loaded;\n  }\n\n  static #instance: IsomorphicClerk | null | undefined;\n\n  static getOrCreateInstance(options: IsomorphicClerkOptions) {\n    // During SSR: a new instance should be created for every request\n    // During CSR: use the cached instance for the whole lifetime of the app\n    // Also will recreate the instance if the provided Clerk instance changes\n    // This method should be idempotent in both scenarios\n    if (!inBrowser() || !this.#instance || (options.Clerk && this.#instance.Clerk !== options.Clerk)) {\n      this.#instance = new IsomorphicClerk(options);\n    }\n    return this.#instance;\n  }\n\n  static clearInstance() {\n    this.#instance = null;\n  }\n\n  get domain(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#domain, new URL(window.location.href), '');\n    }\n    if (typeof this.#domain === 'function') {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#domain || '';\n  }\n\n  get proxyUrl(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use proxy as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#proxyUrl, new URL(window.location.href), '');\n    }\n    if (typeof this.#proxyUrl === 'function') {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#proxyUrl || '';\n  }\n\n  constructor(options: IsomorphicClerkOptions) {\n    const { Clerk = null, frontendApi, publishableKey } = options || {};\n    this.#frontendApi = frontendApi;\n    this.#publishableKey = publishableKey;\n    this.#proxyUrl = options?.proxyUrl;\n    this.#domain = options?.domain;\n    this.options = options;\n    this.Clerk = Clerk;\n    this.mode = inBrowser() ? 'browser' : 'server';\n    void this.loadClerkJS();\n  }\n\n  get sdkMetadata(): SDKMetadata | undefined {\n    return this.clerkjs?.sdkMetadata || this.options.sdkMetadata || undefined;\n  }\n\n  get instanceType(): InstanceType | undefined {\n    return this.clerkjs?.instanceType;\n  }\n\n  get frontendApi(): string {\n    return this.clerkjs?.frontendApi || this.#frontendApi || '';\n  }\n\n  get isStandardBrowser(): boolean {\n    return this.clerkjs?.isStandardBrowser || this.options.standardBrowser || false;\n  }\n\n  get isSatellite(): boolean {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.options.isSatellite, new URL(window.location.href), false);\n    }\n    if (typeof this.options.isSatellite === 'function') {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return false;\n  }\n\n  isReady = (): boolean => Boolean(this.clerkjs?.isReady());\n\n  buildSignInUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignInUrl(opts) || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignInUrl', callback);\n    }\n  };\n\n  buildSignUpUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignUpUrl(opts) || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignUpUrl', callback);\n    }\n  };\n\n  buildUserProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildUserProfileUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUserProfileUrl', callback);\n    }\n  };\n\n  buildCreateOrganizationUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildCreateOrganizationUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildCreateOrganizationUrl', callback);\n    }\n  };\n\n  buildOrganizationProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildOrganizationProfileUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildOrganizationProfileUrl', callback);\n    }\n  };\n\n  buildHomeUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildHomeUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildHomeUrl', callback);\n    }\n  };\n\n  buildUrlWithAuth = (to: string): string | void => {\n    const callback = () => this.clerkjs?.buildUrlWithAuth(to) || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUrlWithAuth', callback);\n    }\n  };\n\n  handleUnauthenticated = (): void => {\n    const callback = () => this.clerkjs?.handleUnauthenticated();\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('handleUnauthenticated', callback);\n    }\n  };\n\n  #waitForClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk> {\n    return new Promise<HeadlessBrowserClerk | BrowserClerk>(resolve => {\n      if (this.#loaded) {\n        resolve(this.clerkjs!);\n      }\n      this.addOnLoaded(() => resolve(this.clerkjs!));\n    });\n  }\n\n  async loadClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk | undefined> {\n    if (this.mode !== 'browser' || this.#loaded) {\n      return;\n    }\n\n    // Store frontendAPI value on window as a fallback. This value can be used as a\n    // fallback during ClerkJS hot loading in case ClerkJS fails to find the\n    // \"data-clerk-frontend-api\" attribute on its script tag.\n\n    // This can happen when the DOM is altered completely during client rehydration.\n    // For example, in Remix with React 18 the document changes completely via `hydrateRoot(document)`.\n\n    // For more information refer to:\n    // - https://github.com/remix-run/remix/issues/2947\n    // - https://github.com/facebook/react/issues/24430\n    if (typeof window !== 'undefined') {\n      window.__clerk_frontend_api = this.frontendApi;\n      window.__clerk_publishable_key = this.publishableKey;\n      window.__clerk_proxy_url = this.proxyUrl;\n      window.__clerk_domain = this.domain;\n    }\n\n    try {\n      if (this.Clerk) {\n        // Set a fixed Clerk version\n        let c: ClerkProp;\n\n        if (isConstructor<BrowserClerkConstructor | HeadlessBrowserClerkConstrutor>(this.Clerk)) {\n          // Construct a new Clerk object if a constructor is passed\n          c = new this.Clerk(this.publishableKey || this.frontendApi || '', {\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n          } as any);\n          await c.load(this.options);\n        } else {\n          // Otherwise use the instantiated Clerk object\n          c = this.Clerk;\n\n          if (!c.isReady()) {\n            await c.load(this.options);\n          }\n        }\n\n        global.Clerk = c;\n      } else {\n        // Hot-load latest ClerkJS from Clerk CDN\n        if (!global.Clerk) {\n          await loadClerkJsScript({\n            ...this.options,\n            frontendApi: this.frontendApi,\n            publishableKey: this.publishableKey,\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n          });\n        }\n\n        if (!global.Clerk) {\n          throw new Error('Failed to download latest ClerkJS. Contact <EMAIL>.');\n        }\n\n        await global.Clerk.load(this.options);\n      }\n\n      global.Clerk.sdkMetadata = this.options.sdkMetadata ?? { name: PACKAGE_NAME, version: PACKAGE_VERSION };\n\n      if (global.Clerk?.loaded || global.Clerk?.isReady()) {\n        return this.hydrateClerkJS(global.Clerk);\n      }\n      return;\n    } catch (err) {\n      const error = err as Error;\n      // In Next.js we can throw a full screen error in development mode.\n      // However, in production throwing an error results in an infinite loop.\n      // More info at: https://github.com/vercel/next.js/issues/6973\n      if (process.env.NODE_ENV === 'production') {\n        console.error(error.stack || error.message || error);\n      } else {\n        throw err;\n      }\n      return;\n    }\n  }\n\n  public addOnLoaded = (cb: () => void) => {\n    this.loadedListeners.push(cb);\n    /**\n     * When IsomorphicClerk is loaded execute the callback directly\n     */\n    if (this.loaded) {\n      this.emitLoaded();\n    }\n  };\n\n  public emitLoaded = () => {\n    this.loadedListeners.forEach(cb => cb());\n    this.loadedListeners = [];\n  };\n\n  private hydrateClerkJS = (clerkjs: BrowserClerk | HeadlessBrowserClerk | undefined) => {\n    if (!clerkjs) {\n      throw new Error('Failed to hydrate latest Clerk JS');\n    }\n\n    this.clerkjs = clerkjs;\n\n    this.premountMethodCalls.forEach(cb => cb());\n\n    if (this.preopenSignIn !== null) {\n      clerkjs.openSignIn(this.preopenSignIn);\n    }\n\n    if (this.preopenSignUp !== null) {\n      clerkjs.openSignUp(this.preopenSignUp);\n    }\n\n    if (this.preopenUserProfile !== null) {\n      clerkjs.openUserProfile(this.preopenUserProfile);\n    }\n\n    if (this.preopenOneTap !== null) {\n      clerkjs.openGoogleOneTap(this.preopenOneTap);\n    }\n\n    if (this.preopenOrganizationProfile !== null) {\n      clerkjs.openOrganizationProfile(this.preopenOrganizationProfile);\n    }\n\n    if (this.preopenCreateOrganization !== null) {\n      clerkjs.openCreateOrganization(this.preopenCreateOrganization);\n    }\n\n    this.premountSignInNodes.forEach((props: SignInProps, node: HTMLDivElement) => {\n      clerkjs.mountSignIn(node, props);\n    });\n\n    this.premountSignUpNodes.forEach((props: SignUpProps, node: HTMLDivElement) => {\n      clerkjs.mountSignUp(node, props);\n    });\n\n    this.premountUserProfileNodes.forEach((props: UserProfileProps, node: HTMLDivElement) => {\n      clerkjs.mountUserProfile(node, props);\n    });\n\n    this.premountUserButtonNodes.forEach((props: UserButtonProps, node: HTMLDivElement) => {\n      clerkjs.mountUserButton(node, props);\n    });\n\n    this.premountOrganizationListNodes.forEach((props: OrganizationListProps, node: HTMLDivElement) => {\n      clerkjs.mountOrganizationList(node, props);\n    });\n\n    this.#loaded = true;\n    this.emitLoaded();\n    return this.clerkjs;\n  };\n\n  get version(): string | undefined {\n    return this.clerkjs?.version;\n  }\n\n  get client(): ClientResource | undefined {\n    if (this.clerkjs) {\n      return this.clerkjs.client;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  get session(): ActiveSessionResource | undefined | null {\n    if (this.clerkjs) {\n      return this.clerkjs.session;\n    } else {\n      return undefined;\n    }\n  }\n\n  get user(): UserResource | undefined | null {\n    if (this.clerkjs) {\n      return this.clerkjs.user;\n    } else {\n      return undefined;\n    }\n  }\n\n  get organization(): OrganizationResource | undefined | null {\n    if (this.clerkjs) {\n      return this.clerkjs.organization;\n    } else {\n      return undefined;\n    }\n  }\n\n  get __unstable__environment(): any {\n    if (this.clerkjs) {\n      return (this.clerkjs as any).__unstable__environment;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  __unstable__setEnvironment(...args: any): void {\n    if (this.clerkjs && '__unstable__setEnvironment' in this.clerkjs) {\n      (this.clerkjs as any).__unstable__setEnvironment(args);\n    } else {\n      return undefined;\n    }\n  }\n\n  __unstable__updateProps = (props: any): any => {\n    // Handle case where accounts has clerk-react@4 installed, but clerk-js@3 is manually loaded\n    if (this.clerkjs && '__unstable__updateProps' in this.clerkjs) {\n      (this.clerkjs as any).__unstable__updateProps(props);\n    } else {\n      return undefined;\n    }\n  };\n\n  /**\n   * `setActive` can be used to set the active session and/or organization.\n   */\n  setActive = ({ session, organization, beforeEmit }: SetActiveParams): Promise<void> => {\n    if (this.clerkjs) {\n      return this.clerkjs.setActive({ session, organization, beforeEmit });\n    } else {\n      return Promise.reject();\n    }\n  };\n\n  setSession = (session: ActiveSessionResource | string | null, beforeEmit?: BeforeEmitCallback): Promise<void> => {\n    deprecated('setSession', 'Use `Clerk.setActive` instead');\n    return this.setActive({ session, beforeEmit });\n  };\n\n  openSignIn = (props?: SignInProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openSignIn(props);\n    } else {\n      this.preopenSignIn = props;\n    }\n  };\n\n  closeSignIn = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeSignIn();\n    } else {\n      this.preopenSignIn = null;\n    }\n  };\n\n  openGoogleOneTap = (props?: GoogleOneTapProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openGoogleOneTap(props);\n    } else {\n      this.preopenOneTap = props;\n    }\n  };\n\n  closeGoogleOneTap = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeGoogleOneTap();\n    } else {\n      this.preopenOneTap = null;\n    }\n  };\n\n  openUserProfile = (props?: UserProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openUserProfile(props);\n    } else {\n      this.preopenUserProfile = props;\n    }\n  };\n\n  closeUserProfile = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeUserProfile();\n    } else {\n      this.preopenUserProfile = null;\n    }\n  };\n\n  openOrganizationProfile = (props?: OrganizationProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openOrganizationProfile(props);\n    } else {\n      this.preopenOrganizationProfile = props;\n    }\n  };\n\n  closeOrganizationProfile = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeOrganizationProfile();\n    } else {\n      this.preopenOrganizationProfile = null;\n    }\n  };\n\n  openCreateOrganization = (props?: CreateOrganizationProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openCreateOrganization(props);\n    } else {\n      this.preopenCreateOrganization = props;\n    }\n  };\n\n  closeCreateOrganization = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeCreateOrganization();\n    } else {\n      this.preopenCreateOrganization = null;\n    }\n  };\n\n  openSignUp = (props?: SignUpProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openSignUp(props);\n    } else {\n      this.preopenSignUp = props;\n    }\n  };\n\n  closeSignUp = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeSignUp();\n    } else {\n      this.preopenSignUp = null;\n    }\n  };\n\n  mountSignIn = (node: HTMLDivElement, props: SignInProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountSignIn(node, props);\n    } else {\n      this.premountSignInNodes.set(node, props);\n    }\n  };\n\n  unmountSignIn = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountSignIn(node);\n    } else {\n      this.premountSignInNodes.delete(node);\n    }\n  };\n\n  mountSignUp = (node: HTMLDivElement, props: SignUpProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountSignUp(node, props);\n    } else {\n      this.premountSignUpNodes.set(node, props);\n    }\n  };\n\n  unmountSignUp = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountSignUp(node);\n    } else {\n      this.premountSignUpNodes.delete(node);\n    }\n  };\n\n  mountUserProfile = (node: HTMLDivElement, props: UserProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountUserProfile(node, props);\n    } else {\n      this.premountUserProfileNodes.set(node, props);\n    }\n  };\n\n  unmountUserProfile = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountUserProfile(node);\n    } else {\n      this.premountUserProfileNodes.delete(node);\n    }\n  };\n\n  mountOrganizationProfile = (node: HTMLDivElement, props: OrganizationProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountOrganizationProfile(node, props);\n    } else {\n      this.premountOrganizationProfileNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationProfile = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountOrganizationProfile(node);\n    } else {\n      this.premountOrganizationProfileNodes.delete(node);\n    }\n  };\n\n  mountCreateOrganization = (node: HTMLDivElement, props: CreateOrganizationProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountCreateOrganization(node, props);\n    } else {\n      this.premountCreateOrganizationNodes.set(node, props);\n    }\n  };\n\n  unmountCreateOrganization = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountCreateOrganization(node);\n    } else {\n      this.premountCreateOrganizationNodes.delete(node);\n    }\n  };\n\n  mountOrganizationSwitcher = (node: HTMLDivElement, props: OrganizationSwitcherProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountOrganizationSwitcher(node, props);\n    } else {\n      this.premountOrganizationSwitcherNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationSwitcher = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountOrganizationSwitcher(node);\n    } else {\n      this.premountOrganizationSwitcherNodes.delete(node);\n    }\n  };\n\n  mountOrganizationList = (node: HTMLDivElement, props: OrganizationListProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountOrganizationList(node, props);\n    } else {\n      this.premountOrganizationListNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationList = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountOrganizationList(node);\n    } else {\n      this.premountOrganizationListNodes.delete(node);\n    }\n  };\n\n  mountUserButton = (node: HTMLDivElement, userButtonProps: UserButtonProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountUserButton(node, userButtonProps);\n    } else {\n      this.premountUserButtonNodes.set(node, userButtonProps);\n    }\n  };\n\n  unmountUserButton = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountUserButton(node);\n    } else {\n      this.premountUserButtonNodes.delete(node);\n    }\n  };\n\n  addListener = (listener: ListenerCallback): UnsubscribeCallback => {\n    const callback = () => this.clerkjs?.addListener(listener);\n\n    if (this.clerkjs) {\n      return callback() as UnsubscribeCallback;\n    } else {\n      this.premountMethodCalls.set('addListener', callback);\n      return () => this.premountMethodCalls.delete('addListener');\n    }\n  };\n\n  navigate = (to: string): void => {\n    const callback = () => this.clerkjs?.navigate(to);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('navigate', callback);\n    }\n  };\n\n  redirectWithAuth = (...args: Parameters<Clerk['redirectWithAuth']>): void => {\n    const callback = () => this.clerkjs?.redirectWithAuth(...args);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('redirectWithAuth', callback);\n    }\n  };\n\n  redirectToSignIn = (opts: SignInRedirectOptions): void => {\n    const callback = () => this.clerkjs?.redirectToSignIn(opts as any);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignIn', callback);\n    }\n  };\n\n  redirectToSignUp = (opts: SignUpRedirectOptions): void => {\n    const callback = () => this.clerkjs?.redirectToSignUp(opts as any);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignUp', callback);\n    }\n  };\n\n  redirectToUserProfile = (): void => {\n    const callback = () => this.clerkjs?.redirectToUserProfile();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToUserProfile', callback);\n    }\n  };\n\n  redirectToHome = (): void => {\n    const callback = () => this.clerkjs?.redirectToHome();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToHome', callback);\n    }\n  };\n\n  redirectToOrganizationProfile = (): void => {\n    const callback = () => this.clerkjs?.redirectToOrganizationProfile();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToOrganizationProfile', callback);\n    }\n  };\n\n  redirectToCreateOrganization = (): void => {\n    const callback = () => this.clerkjs?.redirectToCreateOrganization();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToCreateOrganization', callback);\n    }\n  };\n\n  handleRedirectCallback = (params: HandleOAuthCallbackParams): void => {\n    const callback = () => this.clerkjs?.handleRedirectCallback(params);\n    if (this.clerkjs && this.#loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleRedirectCallback', callback);\n    }\n  };\n  /**\n   * @deprecated Use `handleEmailLinkVerification` instead.\n   */\n  handleMagicLinkVerification = async (params: HandleMagicLinkVerificationParams): Promise<void> => {\n    deprecated('handleMagicLinkVerification', 'Use `handleEmailLinkVerification` instead.');\n    const callback = () => this.clerkjs?.handleMagicLinkVerification(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('handleMagicLinkVerification', callback);\n    }\n  };\n\n  handleGoogleOneTapCallback = (\n    signInOrUp: SignInResource | SignUpResource,\n    params: HandleOAuthCallbackParams,\n  ): void => {\n    const callback = () => this.clerkjs?.handleGoogleOneTapCallback(signInOrUp, params);\n    if (this.clerkjs && this.#loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleGoogleOneTapCallback', callback);\n    }\n  };\n\n  handleEmailLinkVerification = async (params: HandleEmailLinkVerificationParams): Promise<void> => {\n    const callback = () => this.clerkjs?.handleEmailLinkVerification(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('handleEmailLinkVerification', callback);\n    }\n  };\n\n  authenticateWithMetamask = async (params: AuthenticateWithMetamaskParams): Promise<void> => {\n    const callback = () => this.clerkjs?.authenticateWithMetamask(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithMetamask', callback);\n    }\n  };\n\n  authenticateWithGoogleOneTap = async (\n    params: AuthenticateWithGoogleOneTapParams,\n  ): Promise<SignInResource | SignUpResource> => {\n    const clerkjs = await this.#waitForClerkJS();\n    return clerkjs.authenticateWithGoogleOneTap(params);\n  };\n\n  createOrganization = async (params: CreateOrganizationParams): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.createOrganization(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('createOrganization', callback);\n    }\n  };\n\n  getOrganizationMemberships = async (): Promise<OrganizationMembershipResource[] | void> => {\n    const callback = () => this.clerkjs?.getOrganizationMemberships();\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<OrganizationMembershipResource[]>;\n    } else {\n      this.premountMethodCalls.set('getOrganizationMemberships', callback);\n    }\n  };\n\n  getOrganization = async (organizationId: string): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.getOrganization(organizationId);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('getOrganization', callback);\n    }\n  };\n\n  signOut: SignOut = async (\n    signOutCallbackOrOptions?: SignOutCallback | SignOutOptions,\n    options?: SignOutOptions,\n  ): Promise<void> => {\n    const callback = () => this.clerkjs?.signOut(signOutCallbackOrOptions as any, options);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('signOut', callback);\n    }\n  };\n}\n", "import type {\n  ActiveSessionResource,\n  InitialState,\n  MembershipRole,\n  OrganizationCustomPermissionKey,\n  OrganizationResource,\n  Resources,\n  UserResource,\n} from '@clerk/types';\n\nexport const deriveState = (clerkLoaded: boolean, state: Resources, initialState: InitialState | undefined) => {\n  if (!clerkLoaded && initialState) {\n    return deriveFromSsrInitialState(initialState);\n  }\n  return deriveFromClientSideState(state);\n};\n\nconst deriveFromSsrInitialState = (initialState: InitialState) => {\n  const userId = initialState.userId;\n  const user = initialState.user as UserResource;\n  const sessionId = initialState.sessionId;\n  const session = initialState.session as ActiveSessionResource;\n  const organization = initialState.organization as OrganizationResource;\n  const orgId = initialState.orgId;\n  const orgRole = initialState.orgRole as MembershipRole;\n  const orgPermissions = initialState.orgPermissions as OrganizationCustomPermissionKey[];\n  const orgSlug = initialState.orgSlug;\n  const actor = initialState.actor;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    organization,\n    orgId,\n    orgRole,\n    orgPermissions,\n    orgSlug,\n    actor,\n    lastOrganizationInvitation: null,\n    lastOrganizationMember: null,\n  };\n};\n\nconst deriveFromClientSideState = (state: Resources) => {\n  const userId: string | null | undefined = state.user ? state.user.id : state.user;\n  const user = state.user;\n  const sessionId: string | null | undefined = state.session ? state.session.id : state.session;\n  const session = state.session;\n  const actor = session?.actor;\n  const organization = state.organization;\n  const orgId: string | null | undefined = state.organization ? state.organization.id : state.organization;\n  const orgSlug = organization?.slug;\n  const membership = organization\n    ? user?.organizationMemberships?.find(om => om.organization.id === orgId)\n    : organization;\n  const orgPermissions = membership ? membership.permissions : membership;\n  const orgRole = membership ? membership.role : membership;\n\n  const lastOrganizationInvitation = state.lastOrganizationInvitation;\n  const lastOrganizationMember = state.lastOrganizationMember;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    organization,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    actor,\n    lastOrganizationInvitation,\n    lastOrganizationMember,\n  };\n};\n", "import { createContextAndHook } from '@clerk/shared/react';\nimport type { ActJWTClaim, MembershipRole, OrganizationCustomPermissionKey } from '@clerk/types';\n\nexport const [AuthContext, useAuthContext] = createContextAndHook<{\n  userId: string | null | undefined;\n  sessionId: string | null | undefined;\n  actor: ActJWTClaim | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: MembershipRole | null | undefined;\n  orgSlug: string | null | undefined;\n  orgPermissions: OrganizationCustomPermissionKey[] | null | undefined;\n}>('AuthContext');\n", "import type {\n  CheckAuthorizationWithCustomPermissions,\n  HandleOAuthCallbackParams,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n} from '@clerk/types';\nimport React from 'react';\n\nimport { useAuthContext } from '../contexts/AuthContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useSessionContext } from '../contexts/SessionContext';\nimport { LoadedGuarantee } from '../contexts/StructureContext';\nimport { useAuth } from '../hooks';\nimport type { RedirectToSignInProps, RedirectToSignUpProps, WithClerkProp } from '../types';\nimport { withClerk } from './withClerk';\n\nexport const SignedIn = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const { userId } = useAuthContext();\n  if (userId) {\n    return <>{children}</>;\n  }\n  return null;\n};\n\nexport const SignedOut = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const { userId } = useAuthContext();\n  if (userId === null) {\n    return <>{children}</>;\n  }\n  return null;\n};\n\nexport const ClerkLoaded = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (!isomorphicClerk.loaded) {\n    return null;\n  }\n  return <LoadedGuarantee>{children}</LoadedGuarantee>;\n};\n\nexport const ClerkLoading = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.loaded) {\n    return null;\n  }\n  return <>{children}</>;\n};\n\ntype ProtectProps = React.PropsWithChildren<\n  (\n    | {\n        condition?: never;\n        role: OrganizationCustomRoleKey;\n        permission?: never;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        permission: OrganizationCustomPermissionKey;\n      }\n    | {\n        condition: (has: CheckAuthorizationWithCustomPermissions) => boolean;\n        role?: never;\n        permission?: never;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        permission?: never;\n      }\n  ) & {\n    fallback?: React.ReactNode;\n  }\n>;\n\n/**\n * Use `<Protect/>` in order to prevent unauthenticated or unauthorized users from accessing the children passed to the component.\n *\n * Examples:\n * ```\n * <Protect permission=\"a_permission_key\" />\n * <Protect role=\"a_role_key\" />\n * <Protect condition={(has) => has({permission:\"a_permission_key\"})} />\n * <Protect condition={(has) => has({role:\"a_role_key\"})} />\n * <Protect fallback={<p>Unauthorized</p>} />\n * ```\n */\nexport const Protect = ({ children, fallback, ...restAuthorizedParams }: ProtectProps) => {\n  const { isLoaded, has, userId } = useAuth();\n\n  /**\n   * Avoid flickering children or fallback while clerk is loading sessionId or userId\n   */\n  if (!isLoaded) {\n    return null;\n  }\n\n  /**\n   * Fallback to UI provided by user or `null` if authorization checks failed\n   */\n  const unauthorized = <>{fallback ?? null}</>;\n\n  const authorized = <>{children}</>;\n\n  if (!userId) {\n    return unauthorized;\n  }\n\n  /**\n   * Check against the results of `has` called inside the callback\n   */\n  if (typeof restAuthorizedParams.condition === 'function') {\n    if (restAuthorizedParams.condition(has)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  if (restAuthorizedParams.role || restAuthorizedParams.permission) {\n    if (has(restAuthorizedParams)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  /**\n   * If neither of the authorization params are passed behave as the `<SignedIn/>`.\n   * If fallback is present render that instead of rendering nothing.\n   */\n  return authorized;\n};\n\nexport const RedirectToSignIn = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignInProps>) => {\n  const { client, session } = clerk;\n  // TODO: Remove temp use of __unstable__environment\n  const { __unstable__environment } = clerk as any;\n\n  const hasActiveSessions = client.activeSessions && client.activeSessions.length > 0;\n\n  React.useEffect(() => {\n    if (session === null && hasActiveSessions && __unstable__environment) {\n      const { afterSignOutOneUrl } = __unstable__environment.displayConfig;\n      void clerk.navigate(afterSignOutOneUrl);\n    } else {\n      void clerk.redirectToSignIn(props);\n    }\n  }, []);\n\n  return null;\n}, 'RedirectToSignIn');\n\nexport const RedirectToSignUp = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignUpProps>) => {\n  React.useEffect(() => {\n    void clerk.redirectToSignUp(props);\n  }, []);\n\n  return null;\n}, 'RedirectToSignUp');\n\nexport const RedirectToUserProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    clerk.redirectToUserProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToUserProfile');\n\nexport const RedirectToOrganizationProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    clerk.redirectToOrganizationProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToOrganizationProfile');\n\nexport const RedirectToCreateOrganization = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    clerk.redirectToCreateOrganization();\n  }, []);\n\n  return null;\n}, 'RedirectToCreateOrganization');\n\nexport const AuthenticateWithRedirectCallback = withClerk(\n  ({ clerk, ...handleRedirectCallbackParams }: WithClerkProp<HandleOAuthCallbackParams>) => {\n    React.useEffect(() => {\n      void clerk.handleRedirectCallback(handleRedirectCallbackParams);\n    }, []);\n\n    return null;\n  },\n  'AuthenticateWithRedirectCallback',\n);\n\nexport const MultisessionAppSupport = ({ children }: React.PropsWithChildren<unknown>): JSX.Element => {\n  const session = useSessionContext();\n  return <React.Fragment key={session ? session.id : 'no-users'}>{children}</React.Fragment>;\n};\n", "import type { UserResource } from '@clerk/types';\n\nimport { useUserContext } from '../contexts/UserContext';\n\ntype UseUserReturn =\n  | { isLoaded: false; isSignedIn: undefined; user: undefined }\n  | { isLoaded: true; isSignedIn: false; user: null }\n  | { isLoaded: true; isSignedIn: true; user: UserResource };\n\n/**\n * Returns the current auth state and if a user is signed in, the user object.\n *\n * Until Clerk loads and initializes, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access `isSignedIn` state and `user`.\n *\n * For projects using NextJs or Remix, you can make this state available during SSR\n * simply by using the `withServerSideAuth` helper and setting the `loadUser` flag to `true`.\n *\n *\n * @example\n * A simple example:\n *\n * import { useUser } from '@clerk/clerk-react'\n *\n * function Hello() {\n *   const { isSignedIn, user } = useUser();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *   return <div>Hello, {user.firstName}</div>\n * }\n *\n * @example\n * Basic example in a NextJs app. This page will be fully rendered during SSR:\n *\n * import { useUser } from '@clerk/nextjs'\n * import { withServerSideAuth } from '@clerk/nextjs/api'\n *\n * export getServerSideProps = withServerSideAuth({ loadUser: true});\n *\n * export HelloPage = () => {\n *   const { isSignedIn, user } = useUser();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *   return <div>Hello, {user.firstName}</div>\n * }\n *\n */\nexport function useUser(): UseUserReturn {\n  const user = useUserContext();\n\n  if (user === undefined) {\n    return { isLoaded: false, isSignedIn: undefined, user: undefined };\n  }\n\n  if (user === null) {\n    return { isLoaded: true, isSignedIn: false, user: null };\n  }\n\n  return { isLoaded: true, isSignedIn: true, user };\n}\n", "import type {\n  ActJWTClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  MembershipRole,\n  SignOut,\n} from '@clerk/types';\nimport { useCallback } from 'react';\n\nimport { useAuthContext } from '../contexts/AuthContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { invalidStateError, useAuthHasRequiresRoleOrPermission } from '../errors';\nimport type IsomorphicClerk from '../isomorphicClerk';\nimport { createGetToken, createSignOut } from './utils';\n\ntype CheckAuthorizationSignedOut = undefined;\ntype CheckAuthorizationWithoutOrgOrUser = (params?: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => false;\n\ntype UseAuthReturn =\n  | {\n      isLoaded: false;\n      isSignedIn: undefined;\n      userId: undefined;\n      sessionId: undefined;\n      actor: undefined;\n      orgId: undefined;\n      orgRole: undefined;\n      orgSlug: undefined;\n      has: CheckAuthorizationSignedOut;\n      signOut: SignOut;\n      getToken: GetToken;\n    }\n  | {\n      isLoaded: true;\n      isSignedIn: false;\n      userId: null;\n      sessionId: null;\n      actor: null;\n      orgId: null;\n      orgRole: null;\n      orgSlug: null;\n      has: CheckAuthorizationWithoutOrgOrUser;\n      signOut: SignOut;\n      getToken: GetToken;\n    }\n  | {\n      isLoaded: true;\n      isSignedIn: true;\n      userId: string;\n      sessionId: string;\n      actor: ActJWTClaim | null;\n      orgId: null;\n      orgRole: null;\n      orgSlug: null;\n      has: CheckAuthorizationWithoutOrgOrUser;\n      signOut: SignOut;\n      getToken: GetToken;\n    }\n  | {\n      isLoaded: true;\n      isSignedIn: true;\n      userId: string;\n      sessionId: string;\n      actor: ActJWTClaim | null;\n      orgId: string;\n      orgRole: MembershipRole;\n      orgSlug: string | null;\n      has: CheckAuthorizationWithCustomPermissions;\n      signOut: SignOut;\n      getToken: GetToken;\n    };\n\ntype UseAuth = () => UseAuthReturn;\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data  during SSR\n * simply by using the `withServerSideAuth` helper.\n *\n * @example\n * A simple example:\n *\n * import { useAuth } from '@clerk/clerk-react'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * Basic example in a NextJs app. This page will be fully rendered during SSR:\n *\n * import { useAuth } from '@clerk/nextjs'\n * import { withServerSideAuth } from '@clerk/nextjs/api'\n *\n * export getServerSideProps = withServerSideAuth();\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n */\nexport const useAuth: UseAuth = () => {\n  const { sessionId, userId, actor, orgId, orgRole, orgSlug, orgPermissions } = useAuthContext();\n  const isomorphicClerk = useIsomorphicClerkContext() as unknown as IsomorphicClerk;\n\n  const getToken: GetToken = useCallback(createGetToken(isomorphicClerk), [isomorphicClerk]);\n  const signOut: SignOut = useCallback(createSignOut(isomorphicClerk), [isomorphicClerk]);\n\n  const has = useCallback(\n    (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => {\n      if (!params?.permission && !params?.role) {\n        throw new Error(useAuthHasRequiresRoleOrPermission);\n      }\n\n      if (!orgId || !userId || !orgRole || !orgPermissions) {\n        return false;\n      }\n\n      if (params.permission) {\n        return orgPermissions.includes(params.permission);\n      }\n\n      if (params.role) {\n        return orgRole === params.role;\n      }\n\n      return false;\n    },\n    [orgId, orgRole, userId, orgPermissions],\n  );\n\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    };\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    };\n  }\n\n  if (!!sessionId && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    };\n  }\n\n  if (!!sessionId && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    };\n  }\n\n  throw new Error(invalidStateError);\n};\n", "import type IsomorphicClerk from '../isomorphicClerk';\n\n/**\n * @internal\n */\nconst clerkLoaded = (isomorphicClerk: IsomorphicClerk) => {\n  return new Promise<void>(resolve => {\n    if (isomorphicClerk.loaded) {\n      resolve();\n    }\n    isomorphicClerk.addOnLoaded(resolve);\n  });\n};\n\n/**\n * @internal\n */\nexport const createGetToken = (isomorphicClerk: IsomorphicClerk) => {\n  return async (options: any) => {\n    await clerkLoaded(isomorphicClerk);\n    if (!isomorphicClerk.session) {\n      return null;\n    }\n    return isomorphicClerk.session.getToken(options);\n  };\n};\n\n/**\n * @internal\n */\nexport const createSignOut = (isomorphicClerk: IsomorphicClerk) => {\n  return async (...args: any) => {\n    await clerkLoaded(isomorphicClerk);\n    return isomorphicClerk.signOut(...args);\n  };\n};\n", "import type { ActiveSessionResource } from '@clerk/types';\n\nimport { useSessionContext } from '../contexts/SessionContext';\n\ntype UseSessionReturn =\n  | { isLoaded: false; isSignedIn: undefined; session: undefined }\n  | { isLoaded: true; isSignedIn: false; session: null }\n  | { isLoaded: true; isSignedIn: true; session: ActiveSessionResource };\n\ntype UseSession = () => UseSessionReturn;\n\n/**\n * Returns the current auth state and if a session exists, the session object.\n *\n * Until Clerk loads and initializes, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access `isSignedIn` state and `session`.\n *\n * For projects using NextJs or Remix, you can make this state available during SSR\n * simply by using the `withServerSideAuth` helper and setting the `loadSession` flag to `true`.\n *\n * @example\n * A simple example:\n *\n * import { useSession } from '@clerk/clerk-react'\n *\n * function Hello() {\n *   const { isSignedIn, session } = useSession();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *   return <div>{session.updatedAt}</div>\n * }\n *\n * @example\n * Basic example in a NextJs app. This page will be fully rendered during SSR:\n *\n * import { useSession } from '@clerk/nextjs'\n * import { withServerSideAuth } from '@clerk/nextjs/api'\n *\n * export getServerSideProps = withServerSideAuth({ loadSession: true});\n *\n * export HelloPage = () => {\n *   const { isSignedIn, session } = useSession();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *  return <div>{session.updatedAt}</div>\n * }\n */\nexport const useSession: UseSession = () => {\n  const session = useSessionContext();\n\n  if (session === undefined) {\n    return { isLoaded: false, isSignedIn: undefined, session: undefined };\n  }\n\n  if (session === null) {\n    return { isLoaded: true, isSignedIn: false, session: null };\n  }\n\n  return { isLoaded: true, isSignedIn: true, session };\n};\n", "import type { LoadedClerk } from '@clerk/types';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\nexport const useClerk = (): LoadedClerk => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  // The actual value is an instance of IsomorphicClerk, not Clerk\n  // we expose is as a Clerk instance\n  return isomorphicClerk as unknown as LoadedClerk;\n};\n", "import type { SetActive, SetSession, SignInResource } from '@clerk/types';\n\nimport { useClientContext } from '../contexts/ClientContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\ntype UseSignInReturn =\n  | {\n      isLoaded: false;\n      signIn: undefined;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: undefined;\n      setActive: undefined;\n    }\n  | {\n      isLoaded: true;\n      signIn: SignInResource;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: SetSession;\n      setActive: SetActive;\n    };\n\ntype UseSignIn = () => UseSignInReturn;\n\nexport const useSignIn: UseSignIn = () => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  if (!client) {\n    return { isLoaded: false, signIn: undefined, setSession: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    signIn: client.signIn,\n    setSession: isomorphicClerk.setSession,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n", "import type { SetActive, SetSession, SignUpResource } from '@clerk/types';\n\nimport { useClientContext } from '../contexts/ClientContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\ntype UseSignUpReturn =\n  | {\n      isLoaded: false;\n      signUp: undefined;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: undefined;\n      setActive: undefined;\n    }\n  | {\n      isLoaded: true;\n      signUp: SignUpResource;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: SetSession;\n      setActive: SetActive;\n    };\n\ntype UseSignUp = () => UseSignUpReturn;\n\nexport const useSignUp: UseSignUp = () => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  if (!client) {\n    return { isLoaded: false, signUp: undefined, setSession: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    signUp: client.signUp,\n    setSession: isomorphicClerk.setSession,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n", "import type { SessionResource, SetActive, SetSession } from '@clerk/types';\n\nimport { useClientContext } from '../contexts/ClientContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\ntype UseSessionListReturn =\n  | {\n      isLoaded: false;\n      sessions: undefined;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: undefined;\n      setActive: undefined;\n    }\n  | {\n      isLoaded: true;\n      sessions: SessionResource[];\n\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: SetSession;\n      setActive: SetActive;\n    };\n\ntype UseSessionList = () => UseSessionListReturn;\n\nexport const useSessionList: UseSessionList = () => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  if (!client) {\n    return { isLoaded: false, sessions: undefined, setSession: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    sessions: client.sessions,\n    setSession: isomorphicClerk.setSession,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n", "import { deprecated } from '@clerk/shared/deprecated';\nimport type {\n  CreateMagicLinkFlowReturn,\n  EmailAddressResource,\n  SignInResource,\n  SignInStartMagicLinkFlowParams,\n  SignUpResource,\n  StartMagicLinkFlowParams,\n} from '@clerk/types';\nimport React from 'react';\n\ntype MagicLinkable = SignUpResource | EmailAddressResource | SignInResource;\ntype UseMagicLinkSignInReturn = CreateMagicLinkFlowReturn<SignInStartMagicLinkFlowParams, SignInResource>;\ntype UseMagicLinkSignUpReturn = CreateMagicLinkFlowReturn<StartMagicLinkFlowParams, SignUpResource>;\ntype UseMagicLinkEmailAddressReturn = CreateMagicLinkFlowReturn<StartMagicLinkFlowParams, EmailAddressResource>;\n\n/**\n * @deprecated Use `useEmailLink` instead.\n */\nfunction useMagicLink(resource: SignInResource): UseMagicLinkSignInReturn;\n/**\n * @deprecated Use `useEmailLink` instead.\n */\nfunction useMagicLink(resource: SignUpResource): UseMagicLinkSignUpReturn;\n/**\n * @deprecated Use `useEmailLink` instead.\n */\nfunction useMagicLink(resource: EmailAddressResource): UseMagicLinkEmailAddressReturn;\nfunction useMagicLink(\n  resource: MagicLinkable,\n): UseMagicLinkSignInReturn | UseMagicLinkSignUpReturn | UseMagicLinkEmailAddressReturn {\n  deprecated('useMagicLink', 'Use `useEmailLink` instead.');\n\n  const { startMagicLinkFlow, cancelMagicLinkFlow } = React.useMemo(() => resource.createMagicLinkFlow(), [resource]);\n\n  React.useEffect(() => {\n    return cancelMagicLinkFlow;\n  }, []);\n\n  return {\n    startMagicLinkFlow,\n    cancelMagicLinkFlow,\n  } as UseMagicLinkSignInReturn | UseMagicLinkSignUpReturn | UseMagicLinkEmailAddressReturn;\n}\n\nexport { useMagicLink };\n", "import type {\n  CreateEmailLinkFlowReturn,\n  EmailAddressResource,\n  SignInResource,\n  SignInStartEmailLinkFlowParams,\n  SignUpResource,\n  StartEmailLinkFlowParams,\n} from '@clerk/types';\nimport React from 'react';\n\ntype EmailLinkable = SignUpResource | EmailAddressResource | SignInResource;\ntype UseEmailLinkSignInReturn = CreateEmailLinkFlowReturn<SignInStartEmailLinkFlowParams, SignInResource>;\ntype UseEmailLinkSignUpReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, SignUpResource>;\ntype UseEmailLinkEmailAddressReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, EmailAddressResource>;\n\nfunction useEmailLink(resource: SignInResource): UseEmailLinkSignInReturn;\nfunction useEmailLink(resource: SignUpResource): UseEmailLinkSignUpReturn;\nfunction useEmailLink(resource: EmailAddressResource): UseEmailLinkEmailAddressReturn;\nfunction useEmailLink(\n  resource: EmailLinkable,\n): UseEmailLinkSignInReturn | UseEmailLinkSignUpReturn | UseEmailLinkEmailAddressReturn {\n  const { startEmailLinkFlow, cancelEmailLinkFlow } = React.useMemo(() => resource.createEmailLinkFlow(), [resource]);\n\n  React.useEffect(() => {\n    return cancelEmailLinkFlow;\n  }, []);\n\n  return {\n    startEmailLinkFlow,\n    cancelEmailLinkFlow,\n  } as UseEmailLinkSignInReturn | UseEmailLinkSignUpReturn | UseEmailLinkEmailAddressReturn;\n}\n\nexport { useEmailLink };\n", "import type { UserResource } from '@clerk/types';\nimport React from 'react';\n\nimport { useUserContext } from '../contexts/UserContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withUser = <P extends { user: UserResource }>(Component: React.ComponentType<P>, displayName?: string) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC: React.FC<Omit<P, 'user'>> = (props: Omit<P, 'user'>) => {\n    const user = useUserContext();\n\n    if (!user) {\n      return null;\n    }\n\n    return (\n      <Component\n        {...(props as P)}\n        user={user}\n      />\n    );\n  };\n\n  HOC.displayName = `withUser(${displayName})`;\n  return HOC;\n};\n\nexport const WithUser: React.FC<{\n  children: (user: UserResource) => React.ReactNode;\n}> = ({ children }) => {\n  const user = useUserContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  return <>{children(user)}</>;\n};\n", "import type { SessionResource } from '@clerk/types';\nimport React from 'react';\n\nimport { useSessionContext } from '../contexts/SessionContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withSession = <P extends { session: SessionResource }>(\n  Component: React.ComponentType<P>,\n  displayName?: string,\n) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC: React.FC<Omit<P, 'session'>> = (props: Omit<P, 'session'>) => {\n    const session = useSessionContext();\n\n    if (!session) {\n      return null;\n    }\n\n    return (\n      <Component\n        {...(props as P)}\n        session={session}\n      />\n    );\n  };\n\n  HOC.displayName = `withSession(${displayName})`;\n  return HOC;\n};\n\nexport const WithSession: React.FC<{\n  children: (session: SessionResource) => React.ReactNode;\n}> = ({ children }) => {\n  const session = useSessionContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!session) {\n    return null;\n  }\n\n  return <>{children(session)}</>;\n};\n", "import React from 'react';\n\nimport type { SignInButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInButton = withClerk(({ clerk, children, ...props }: WithClerkProp<SignInButtonProps>) => {\n  const { afterSignInUrl, afterSignUpUrl, redirectUrl, mode, ...rest } = props;\n\n  children = normalizeWithDefaultValue(children, 'Sign in');\n  const child = assertSingleChild(children)('SignInButton');\n\n  const clickHandler = () => {\n    const opts = { afterSignInUrl, afterSignUpUrl, redirectUrl };\n    if (mode === 'modal') {\n      return clerk.openSignIn(opts);\n    }\n    return clerk.redirectToSignIn(opts);\n  };\n\n  const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n    await safeExecute((child as any).props.onClick)(e);\n    return clickHandler();\n  };\n\n  const childProps = { ...rest, onClick: wrappedChildClickHandler };\n  return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n}, 'SignInButton');\n", "import React from 'react';\n\nimport type { SignUpButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignUpButton = withClerk(({ clerk, children, ...props }: WithClerkProp<SignUpButtonProps>) => {\n  const { afterSignInUrl, afterSignUpUrl, redirectUrl, mode, unsafeMetadata, ...rest } = props;\n\n  children = normalizeWithDefaultValue(children, 'Sign up');\n  const child = assertSingleChild(children)('SignUpButton');\n\n  const clickHandler = () => {\n    const opts = { afterSignInUrl, afterSignUpUrl, redirectUrl, unsafeMetadata };\n\n    if (mode === 'modal') {\n      return clerk.openSignUp(opts);\n    }\n\n    return clerk.redirectToSignUp(opts);\n  };\n\n  const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n    await safeExecute((child as any).props.onClick)(e);\n    return clickHandler();\n  };\n\n  const childProps = { ...rest, onClick: wrappedChildClickHandler };\n  return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n}, 'SignUpButton');\n", "import type { SignOutCallback, SignOutOptions } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport type SignOutButtonProps = {\n  signOutCallback?: SignOutCallback;\n  signOutOptions?: SignOutOptions;\n  children?: React.ReactNode;\n};\n\nexport const SignOutButton = withClerk(\n  ({ clerk, children, ...props }: React.PropsWithChildren<WithClerkProp<SignOutButtonProps>>) => {\n    const { signOutCallback, signOutOptions, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign out');\n    const child = assertSingleChild(children)('SignOutButton');\n\n    const clickHandler = () => {\n      return clerk.signOut(signOutCallback, signOutOptions);\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  'SignOutButton',\n);\n", "import React from 'react';\n\nimport type { SignInWithMetamaskButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInWithMetamaskButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<SignInWithMetamaskButtonProps>) => {\n    const { redirectUrl, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign in with Metamask');\n    const child = assertSingleChild(children)('SignInWithMetamaskButton');\n\n    // TODO: Properly fix this code\n    // eslint-disable-next-line @typescript-eslint/require-await\n    const clickHandler = async () => {\n      async function authenticate() {\n        await clerk.authenticateWithMetamask({ redirectUrl });\n      }\n      void authenticate();\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  'SignInWithMetamask',\n);\n"], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,6BACE,WAAWA,QAAM,oBACf,oBAAoB,MACtB,QAAQ;AAAA,UACN;AAAA,QACF;AACF,YAAI,QAAQ,YAAY;AACxB,YAAI,CAAC,4BAA4B;AAC/B,cAAI,cAAc,YAAY;AAC9B,mBAAS,OAAO,WAAW,MACxB,QAAQ;AAAA,YACP;AAAA,UACF,GACC,6BAA6B;AAAA,QAClC;AACA,sBAAcC,UAAS;AAAA,UACrB,MAAM,EAAE,OAAc,YAAyB;AAAA,QACjD,CAAC;AACD,YAAI,OAAO,YAAY,CAAC,EAAE,MACxB,cAAc,YAAY,CAAC;AAC7B,QAAAC;AAAA,UACE,WAAY;AACV,iBAAK,QAAQ;AACb,iBAAK,cAAc;AACnB,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,UAC5D;AAAA,UACA,CAAC,WAAW,OAAO,WAAW;AAAA,QAChC;AACA,QAAAC;AAAA,UACE,WAAY;AACV,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAC1D,mBAAO,UAAU,WAAY;AAC3B,qCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,YAC5D,CAAC;AAAA,UACH;AAAA,UACA,CAAC,SAAS;AAAA,QACZ;AACA,QAAAC,eAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,eAAO,KAAK;AACZ,YAAI;AACF,cAAI,YAAY,kBAAkB;AAClC,iBAAO,CAAC,SAAS,MAAM,SAAS;AAAA,QAClC,SAAS,OAAO;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,eAAO,YAAY;AAAA,MACrB;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAIJ,UAAQ,iBACV,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzDC,YAAWD,QAAM,UACjBG,aAAYH,QAAM,WAClBE,mBAAkBF,QAAM,iBACxBI,iBAAgBJ,QAAM,eACtB,oBAAoB,OACpB,6BAA6B,OAC7B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,YAC9B,gBAAgB,OAAO,OAAO,SAAS,gBACnC,yBACA;AACR,cAAQ,uBACN,WAAWA,QAAM,uBAAuBA,QAAM,uBAAuB;AACvE,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AC9FL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA,IAAI,gBAAgB,CAAC,KAAK,QAAQ,QAAQ;AACxC,MAAI,CAAC,OAAO,IAAI,GAAG;AACjB,UAAM,UAAU,YAAY,GAAG;AACnC;AACA,IAAI,eAAe,CAAC,KAAK,QAAQ,WAAW;AAC1C,gBAAc,KAAK,QAAQ,yBAAyB;AACpD,SAAO,SAAS,OAAO,KAAK,GAAG,IAAI,OAAO,IAAI,GAAG;AACnD;AACA,IAAI,eAAe,CAAC,KAAK,QAAQ,UAAU;AACzC,MAAI,OAAO,IAAI,GAAG;AAChB,UAAM,UAAU,mDAAmD;AACrE,oBAAkB,UAAU,OAAO,IAAI,GAAG,IAAI,OAAO,IAAI,KAAK,KAAK;AACrE;AACA,IAAI,eAAe,CAAC,KAAK,QAAQ,OAAO,WAAW;AACjD,gBAAc,KAAK,QAAQ,wBAAwB;AACnD,WAAS,OAAO,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,KAAK,KAAK;AACxD,SAAO;AACT;AACA,IAAI,kBAAkB,CAAC,KAAK,QAAQ,WAAW;AAC7C,gBAAc,KAAK,QAAQ,uBAAuB;AAClD,SAAO;AACT;;;ACfA,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,QAAQ;AACnD,SAAO,SAAS,OAAO,WAAW,cAAc,SAAS;AAC3D;;;ACJO,IAAM,iBAAiB,CAAC,SAAiB;AAC9C,MAAI,OAAO,SAAS,eAAe,OAAO,SAAS,YAAY;AAC7D,WAAO,KAAK,IAAI;EAClB,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ;AACzD,WAAO,IAAI,OAAO,OAAO,MAAM,QAAQ,EAAE,SAAS;EACpD;AACA,SAAO;AACT;;;ACPA,IAAM,8BAA8B;AACpC,IAAM,8BAA8B;AAY7B,SAAS,oBAAoB,KAAgD;AAClF,QAAM,OAAO;AAEb,MAAI,CAAC,iBAAiB,GAAG,GAAG;AAC1B,WAAO;EACT;AAEA,QAAM,eAAe,IAAI,WAAW,2BAA2B,IAAI,eAAe;AAElF,MAAI,cAAc,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AAElD,MAAI,CAAC,YAAY,SAAS,GAAG,GAAG;AAC9B,WAAO;EACT;AAEA,gBAAc,YAAY,MAAM,GAAG,EAAE;AAErC,SAAO;IACL;IACA;EACF;AACF;AAEO,SAAS,iBAAiB,KAAa;AAC5C,QAAM,OAAO;AAEb,QAAM,iBAAiB,IAAI,WAAW,2BAA2B,KAAK,IAAI,WAAW,2BAA2B;AAEhH,QAAM,6BAA6B,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,GAAG;AAEvF,SAAO,kBAAkB;AAC3B;AAEO,SAAS,uBAAuB,KAAa;AAClD,QAAM,OAAO;AAEb,SAAO,IAAI,WAAW,QAAQ;AAChC;AAEO,SAAS,6BAA6B;AAE3C,QAAM,0BAA0B;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AAEA,QAAM,uBAAuB,oBAAI,IAAqB;AAEtD,SAAO;IACL,mBAAmB,CAAC,QAA+B;AACjD,UAAI,CAAC,KAAK;AACR,eAAO;MACT;AAEA,YAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI;AACrD,UAAI,MAAM,qBAAqB,IAAI,QAAQ;AAC3C,UAAI,QAAQ,QAAW;AACrB,cAAM,wBAAwB,KAAK,CAAA,MAAK,SAAS,SAAS,CAAC,CAAC;AAC5D,6BAAqB,IAAI,UAAU,GAAG;MACxC;AACA,aAAO;IACT;EACF;AACF;;;ACvFA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAIK,YAAW,CAAC,QAAQ,QAAQ;AAC9B,WAAS,QAAQ;AACf,cAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAChE;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;;;ACd9I,IAAAC,iBAAkB;;;ACFX,IAAM,2BAA2B,MAAe;AACrD,MAAI;AACF,WAAO;EAET,SAAS,KAAK;EAAC;AAIf,SAAO;AACT;AAEO,IAAM,oBAAoB,MAAe;AAC9C,MAAI;AACF,WAAO;EAET,SAAS,KAAK;EAAC;AAGf,SAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;AACpD,MAAI;AACF,WAAO;EAET,SAAS,KAAK;EAAC;AAGf,SAAO;AACT;ACRA,IAAM,oBAAoB,oBAAI,IAAY;AACnC,IAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;AACjF,QAAM,cAAc,kBAAkB,KAAK,wBAAwB;AACnE,QAAM,YAAY,OAAA,OAAA,MAAO;AACzB,MAAI,kBAAkB,IAAI,SAAS,KAAK,aAAa;AACnD;EACF;AACA,oBAAkB,IAAI,SAAS;AAE/B,UAAQ;IACN,iCAAiC,MAAM;EAAmE,OAAO;EACnH;AACF;AAkDO,IAAM,2BAA2B,CACtC,KACA,UACA,SACA,QACS;AACT,MAAI,QAAQ,IAAI,QAAQ;AACxB,SAAO,eAAe,KAAK,UAAU;IACnC,MAAM;AACJ,iBAAW,UAAU,SAAS,GAAG;AACjC,aAAO;IACT;IACA,IAAI,GAAY;AACd,cAAQ;IACV;EACF,CAAC;AACH;;;AC5DO,SAAS,aAAa,OAAY;AACvC,SAAO,wBAAwB,KAAK,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,KAAK;AAC9F;AAEO,SAAS,wBAAwB,KAAwC;AAC9E,SAAO,gBAAgB;AACzB;AAkBO,SAAS,oBAAoB,KAAoC;AACtE,SAAO,uBAAuB;AAChC;AAEO,SAAS,gBAAgB,KAAgC;AAC9D,SAAO,UAAU,OAAO,CAAC,MAAM,OAAO,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,aAAa;AAClF;AAiHO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;EAGxC,YAAY,MAAc;AACxB,UAAM,IAAI;AACV,SAAK,OAAO;AACZ,WAAO,eAAe,MAAM,gBAAe,SAAS;AACpD,eAAW,kBAAkB,+BAA+B;EAC9D;AACF;AAEO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;EAGxC,YAAY,MAAc;AACxB,UAAM,IAAI;AACV,SAAK,OAAO;AACZ,WAAO,eAAe,MAAM,gBAAe,SAAS;EACtD;AACF;AAMO,SAAS,iBAAiB,KAAmC;AAClE,aAAW,oBAAoB,iCAAiC;AAChE,SAAO,eAAe;AACxB;AAEO,SAAS,iBAAiB,KAAmC;AAClE,SAAO,eAAe;AACxB;AAEA,IAAM,sBAAsB;EAC1B,SAAS;EACT,QAAQ;AACV;AAKO,IAAM,qBAAqB,IAAI,MAAM,qBAAqB;EAC/D,IAAI,QAAQ,MAAM,UAAU;AAC1B,eAAW,sBAAsB,mCAAmC;AACpE,WAAO,QAAQ,IAAI,QAAQ,MAAM,QAAQ;EAC3C;AACF,CAAC;AAEM,IAAM,qBAAqB;EAChC,SAAS;EACT,QAAQ;AACV;AAEA,IAAM,kBAAkB,OAAO,OAAO;EACpC,gCAAgC;EAChC,6BAA6B;EAC7B,mCAAmC;EACnC,mCAAmC;AACrC,CAAC;AA2BM,SAAS,kBAAkB,EAAE,aAAa,eAAe,GAAsC;AACpG,MAAI,MAAM;AAEV,QAAM,WAAW;IACf,GAAG;IACH,GAAG;EACL;AAEA,WAAS,aAAa,YAAoB,cAAgD;AACxF,QAAI,CAAC,cAAc;AACjB,aAAO,GAAG,GAAG,KAAK,UAAU;IAC9B;AAEA,QAAI,MAAM;AACV,UAAM,UAAU,WAAW,SAAS,uBAAuB;AAE3D,eAAW,SAAS,SAAS;AAC3B,YAAM,eAAe,aAAa,MAAM,CAAC,CAAC,KAAK,IAAI,SAAS;AAC5D,YAAM,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,WAAW;IAClD;AAEA,WAAO,GAAG,GAAG,KAAK,GAAG;EACvB;AAEA,SAAO;IACL,eAAe,EAAE,aAAAC,aAAY,GAAsC;AACjE,UAAI,OAAOA,iBAAgB,UAAU;AACnC,cAAMA;MACR;AACA,aAAO;IACT;IAEA,YAAY,EAAE,gBAAAC,gBAAe,GAAsC;AACjE,aAAO,OAAO,UAAUA,mBAAkB,CAAC,CAAC;AAC5C,aAAO;IACT;IAEA,gCAAgC,QAAiC;AAC/D,YAAM,IAAI,MAAM,aAAa,SAAS,mCAAmC,MAAM,CAAC;IAClF;IAEA,6BAA6B,QAAiC;AAC5D,YAAM,IAAI,MAAM,aAAa,SAAS,gCAAgC,MAAM,CAAC;IAC/E;IAEA,qBAAqB,QAAiC;AACpD,YAAM,IAAI,MAAM,aAAa,SAAS,6BAA6B,MAAM,CAAC;IAC5E;IAEA,kCAAyC;AACvC,YAAM,IAAI,MAAM,aAAa,SAAS,iCAAiC,CAAC;IAC1E;EACF;AACF;;;ACrTO,IAAM,uBAAuB;AAQ7B,IAAM,8BACX;AAEK,IAAM,+BAA+B;AAErC,IAAM,oCAAoC,CAAC,SAChD,yDAAyD,IAAI;AAExD,IAAM,oBACX;AAEK,IAAM,gDACX;AAEK,IAAM,+BACX;AACK,IAAM,+BACX;AAEK,IAAM,uCACX;AACK,IAAM,uCACX;AAEK,IAAM,8BAA8B,CAAC,kBAC1C,WAAW,aAAa,wBAAwB,aAAa,iBAAiB,aAAa;AAEtF,IAAM,uBAAuB,CAAC,kBACnC,0BAA0B,aAAa;AAElC,IAAM,uBAAuB,CAAC,kBACnC,0BAA0B,aAAa;AAElC,IAAM,qCACX;;;ACtDF,mBAAkB;AAIX,IAAM,oBACX,CAAC,aACD,CAAC,SAAyF;AACxF,MAAI;AACF,WAAO,aAAAC,QAAM,SAAS,KAAK,QAAQ;EACrC,SAAS,GAAG;AACV,UAAM,IAAI,MAAM,kCAAkC,IAAI,CAAC;EACzD;AACF;AAEK,IAAM,4BAA4B,CAAC,UAAuC,gBAAwB;AACvG,MAAI,CAAC,UAAU;AACb,eAAW;EACb;AACA,MAAI,OAAO,aAAa,UAAU;AAChC,eAAW,aAAAA,QAAA,cAAC,UAAA,MAAQ,QAAS;EAC/B;AACA,SAAO;AACT;AAEO,IAAM,cACX,CAAC,OACD,IAAI,SAAc;AAChB,MAAI,MAAM,OAAO,OAAO,YAAY;AAClC,WAAO,GAAG,GAAG,IAAI;EACnB;AACF;;;AC3BF,IAAM,eAAe,kBAAkB,EAAE,aAAa,eAAe,CAAC;AAEtE,SAAS,mCAAmC,SAA8B;AACxE,eAAa,YAAY,OAAO,EAAE,eAAe,OAAO;AAC1D;;;ACPO,SAAS,cAAiB,GAAgB;AAC/C,SAAO,OAAO,MAAM;AACtB;;;ACFA,IAAM,oBAAoB;AAC1B,IAAM,eAAe;AASrB,eAAsB,WAAW,MAAM,IAAI,MAAqD;AAC9F,QAAM,EAAE,OAAO,OAAO,YAAY,YAAY,IAAI,QAAQ,CAAC;AAC3D,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,CAAC,KAAK;AACR,aAAO,YAAY;IACrB;AAEA,QAAI,CAAC,YAAY,CAAC,SAAS,MAAM;AAC/B,aAAO,iBAAiB;IAC1B;AAEA,UAAM,SAAS,SAAS,cAAc,QAAQ;AAE9C,mBAAe,OAAO,aAAa,eAAe,WAAW;AAC7D,WAAO,QAAQ,SAAS;AACxB,WAAO,QAAQ,SAAS;AAExB,WAAO,iBAAiB,QAAQ,MAAM;AACpC,aAAO,OAAO;AACd,cAAQ,MAAM;IAChB,CAAC;AAED,WAAO,iBAAiB,SAAS,MAAM;AACrC,aAAO,OAAO;AACd,aAAO;IACT,CAAC;AAED,WAAO,MAAM;AACb,kBAAA,OAAA,SAAA,WAAa,MAAA;AACb,aAAS,KAAK,YAAY,MAAM;EAClC,CAAC;AACH;;;ACvCO,SAAS,gBAAgB,KAAyB;AACvD,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AAEA,SAAO,cAAc,GAAG,KAAK,mBAAmB,GAAG;AACrD;AAEO,SAAS,cAAc,KAAyB;AACrD,SAAO,iBAAiB,KAAK,OAAO,EAAE;AACxC;AAEO,SAAS,mBAAmB,KAAa;AAC9C,SAAO,IAAI,WAAW,GAAG;AAC3B;AAEO,SAAS,sBAAsB,KAAiC;AACrE,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AACA,SAAO,mBAAmB,GAAG,IAAI,IAAI,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE,SAAS,IAAI;AACrF;;;AEVO,SAAS,eAAe,KAAyB;AACtD,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AACA,MAAI;AACJ,MAAI,IAAI,MAAM,iBAAiB,GAAG;AAChC,YAAQ;EACV,WAAW,IAAI,MAAM,kBAAkB,GAAG;AACxC,WAAO;EACT,OAAO;AACL,YAAQ;EACV;AAEA,QAAM,WAAW,IAAI,QAAQ,OAAO,EAAE;AACtC,SAAO,SAAS,QAAQ;AAC1B;;;AC3BA,IAAM,EAAE,kBAAkB,IAAI,2BAA2B;;;ACQlD,IAAM,kBAAkB,CAAC,mBAAuC;AACrE,MAAI,gBAAgB;AAClB,WAAO;EACT;AAEA,QAAM,gBAAgB,iBAAiB,QAAe;AACtD,MAAI,eAAe;AACjB,QAAI,kBAAkB,YAAY;AAChC,aAAO;IACT;AAEA,WAAO;EACT;AAEA,SAAO,gBAAgB,QAAe;AACxC;AAGA,IAAM,mBAAmB,CAAC,mBAAwB;AA3BlD,MAAA;AA2BqD,UAAA,KAAA,eAAe,MAAM,SAAS,MAA9B,OAAA,SAAA,GAAkC,CAAA;AAAA;AACvF,IAAM,kBAAkB,CAAC,mBAA2B,eAAe,MAAM,GAAG,EAAE,CAAC;;;AClB/E,IAAM,uBAAuB;AAOtB,IAAM,oBAAoB,CAAC,SAAmC;AACnE,QAAM,EAAE,aAAa,eAAe,IAAI;AAExC,MAAI,CAAC,kBAAkB,CAAC,aAAa;AACnC,iBAAa,gCAAgC;EAC/C;AAEA,SAAO,WAAW,iBAAiB,IAAI,GAAG;IACxC,OAAO;IACP,aAAa;IACb,YAAY,6BAA6B,IAAI;EAC/C,CAAC,EAAE,MAAM,MAAM;AACb,UAAM,IAAI,MAAM,oBAAoB;EACtC,CAAC;AACH;AAEA,IAAM,mBAAmB,CAAC,SAAmC;AAjC7D,MAAA,IAAA;AAkCE,QAAM,EAAE,YAAY,gBAAgB,gBAAgB,UAAU,QAAQ,gBAAgB,YAAY,IAAI;AAEtG,MAAI,YAAY;AACd,WAAO;EACT;AAEA,MAAI,aAAa;AACjB,MAAI,CAAC,CAAC,YAAY,gBAAgB,QAAQ,GAAG;AAC3C,iBAAa,sBAAsB,QAAQ,EAAE,QAAQ,iBAAiB,EAAE;EAC1E,WAAW,UAAU,CAAC,oBAAkB,KAAA,oBAAoB,cAAc,MAAlC,OAAA,SAAA,GAAqC,gBAAe,eAAe,EAAE,GAAG;AAC9G,iBAAa,eAAe,MAAM;EACpC,OAAO;AACL,mBAAa,KAAA,oBAAoB,cAAc,MAAlC,OAAA,SAAA,GAAqC,gBAAe,eAAe;EAClF;AAEA,QAAM,UAAU,iBAAiB,GAAG,eAAe,QAAQ,QAAQ,EAAE,CAAC,MAAM;AAC5E,QAAM,UAAU,gBAAgB,cAAc;AAC9C,SAAO,WAAW,UAAU,wBAAwB,OAAO,eAAe,OAAO;AACnF;AAEA,IAAM,+BAA+B,CAAC,YAAsC,CAAC,WAA8B;AACzG,QAAM,EAAE,gBAAgB,aAAa,UAAU,OAAO,IAAI;AAC1D,MAAI,gBAAgB;AAClB,WAAO,aAAa,8BAA8B,cAAc;EAClE,WAAW,aAAa;AACtB,WAAO,aAAa,2BAA2B,WAAW;EAC5D;AAEA,MAAI,UAAU;AACZ,WAAO,aAAa,wBAAwB,QAAQ;EACtD;AAEA,MAAI,QAAQ;AACV,WAAO,aAAa,qBAAqB,MAAM;EACjD;AACF;;;ACrEA,IAAAC,gBAAkB;AAElB,IAAM,SAAS,oBAAI,IAAoB;AAEhC,SAAS,4BAA4B,MAAc,OAAe,WAAW,GAAS;AAC3F,gBAAAC,QAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,OAAO,IAAI,IAAI,KAAK;AAClC,QAAI,SAAS,UAAU;AACrB,YAAM,IAAI,MAAM,KAAK;IACvB;AACA,WAAO,IAAI,MAAM,QAAQ,CAAC;AAE1B,WAAO,MAAM;AACX,aAAO,IAAI,OAAO,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC;IAC9C;EACF,GAAG,CAAC,CAAC;AACP;AAEO,SAAS,6BACd,kBACA,MACA,OACwB;AACxB,QAAM,cAAc,iBAAiB,eAAe,iBAAiB,QAAQ,QAAQ;AACrF,QAAM,MAAM,CAAC,UAAa;AACxB,gCAA4B,MAAM,KAAK;AACvC,WAAO,cAAAA,QAAA,cAAC,kBAAA,EAAkB,GAAI,MAAA,CAAe;EAC/C;AACA,MAAI,cAAc,gCAAgC,WAAW;AAC7D,SAAO;AACT;;;AC9BA,IAAAC,gBAAgC;AAChC,uBAA6B;AAgBtB,IAAM,yBAAyB,CAAC,aAA6C;AAClF,QAAM,eAAe,MAAM,SAAS,MAAM,EAAE,KAAK,IAAI;AACrD,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAA6B,YAAY;AAEnE,SAAO,SAAS,IAAI,CAAC,IAAI,WAAW;IAClC,IAAI,GAAG;IACP,OAAO,CAAC,SAAkB,SAAS,CAAA,cAAa,UAAU,IAAI,CAAC,GAAG,MAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;IACjG,SAAS,MAAM,SAAS,CAAA,cAAa,UAAU,IAAI,CAAC,GAAG,MAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;IACtF,QAAQ,MAAM,cAAAC,QAAA,cAAA,cAAAA,QAAA,UAAA,MAAG,MAAM,KAAK,QAAI,+BAAa,GAAG,WAAW,MAAM,KAAK,CAAY,IAAI,IAAK;EAC7F,EAAE;AACJ;;;ACGO,SAAS,aAAa,KAAiC;AAC5D,SAAO,MAAM,IAAI,QAAQ,gBAAgB,CAAA,UAAS,MAAM,YAAY,EAAE,QAAQ,OAAO,EAAE,CAAC,IAAI;AAC9F;AAEO,SAAS,aAAa,KAAiC;AAC5D,SAAO,MAAM,IAAI,QAAQ,UAAU,CAAA,WAAU,IAAI,OAAO,YAAY,CAAC,EAAE,IAAI;AAC7E;AAEA,IAAM,8BAA8B,CAAC,cAAmB;AACtD,QAAM,gBAAgB,CAAC,QAAkB;AACvC,QAAI,CAAC,KAAK;AACR,aAAO;IACT;AAEA,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAO,IAAI,IAAI,CAAA,OAAM;AACnB,YAAI,OAAO,OAAO,YAAY,MAAM,QAAQ,EAAE,GAAG;AAC/C,iBAAO,cAAc,EAAE;QACzB;AACA,eAAO;MACT,CAAC;IACH;AAEA,UAAM,OAAO,EAAE,GAAG,IAAI;AACtB,UAAM,OAAO,OAAO,KAAK,IAAI;AAC7B,eAAW,WAAW,MAAM;AAC1B,YAAM,UAAU,UAAU,QAAQ,SAAS,CAAC;AAC5C,UAAI,YAAY,SAAS;AACvB,aAAK,OAAO,IAAI,KAAK,OAAO;AAC5B,eAAO,KAAK,OAAO;MACrB;AACA,UAAI,OAAO,KAAK,OAAO,MAAM,UAAU;AACrC,aAAK,OAAO,IAAI,cAAc,KAAK,OAAO,CAAC;MAC7C;IACF;AACA,WAAO;EACT;AAEA,SAAO;AACT;AAOO,IAAM,mBAAmB,4BAA4B,YAAY;AAOjE,IAAM,mBAAmB,4BAA4B,YAAY;;;AChExE,IAAM,yBAAyB,OAAO,OAAO;EAC3C,aAAa;EACb,cAAc;EACd,aAAa;EACb,cAAc;EACd,gBAAgB;EAChB,4BAA4B;AAC9B,CAAU;;;ACvBH,SAAS,gBAAmB,OAAyB,KAAU,cAAiC;AACrG,MAAI,OAAO,UAAU,YAAY;AAC/B,WAAQ,MAAwB,GAAG;EACrC;AAEA,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO;EACT;AAEA,MAAI,OAAO,iBAAiB,aAAa;AACvC,WAAO;EACT;AAEA,SAAO;AACT;;;ACbO,SAAS,YAAqB;AACnC,SAAO,OAAO,WAAW;AAC3B;AAEA,IAAM,YAAY;EAChB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AACA,IAAM,gBAAgB,IAAI,OAAO,UAAU,KAAK,GAAG,GAAG,GAAG;;;AGpClD,IAAM,oBAAoB,CAAC,YAAoB;AACpD,MAAI,yBAAyB,GAAG;AAC9B,YAAQ,MAAM,OAAO;EACvB;AACF;;;ACHA,IAAAC,iBAAkB;;;ACUlB,IAAAC,iBAAqC;;;ACZrC,IAAAC,iBAAkB;;;ACAlB,IAAAC,gBAAkB;;;AQDlB;AAAA;AAAA,mBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC,gBAA0E;AAC1E,kBAAqC;;;ACDrC,IAAAC,gBAAoI;AAGpI,IAAMC,QAAO,MAAI;AAAC;AAKlB,IAAM;AAAA;AAAA,EAA8BA,MAAK;AAAA;AACzC,IAAM,SAAS;AACf,IAAM,cAAc,CAAC,MAAI,MAAM;AAC/B,IAAM,aAAa,CAAC,MAAI,OAAO,KAAK;AACpC,IAAM,eAAe,CAAC,GAAG,OAAK;AAAA,EACtB,GAAG;AAAA,EACH,GAAG;AACP;AACJ,IAAM,gBAAgB,CAAC,MAAI,WAAW,EAAE,IAAI;AAM5C,IAAM,QAAQ,oBAAI,QAAQ;AAE1B,IAAI,UAAU;AASd,IAAM,aAAa,CAAC,QAAM;AACtB,QAAM,OAAO,OAAO;AACpB,QAAM,cAAc,OAAO,IAAI;AAC/B,QAAM,SAAS,eAAe;AAC9B,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,eAAe,QAAQ;AAGzD,aAAS,MAAM,IAAI,GAAG;AACtB,QAAI;AAAQ,aAAO;AAInB,aAAS,EAAE,UAAU;AACrB,UAAM,IAAI,KAAK,MAAM;AACrB,QAAI,eAAe,OAAO;AAEtB,eAAS;AACT,WAAI,QAAQ,GAAG,QAAQ,IAAI,QAAQ,SAAQ;AACvC,kBAAU,WAAW,IAAI,KAAK,CAAC,IAAI;AAAA,MACvC;AACA,YAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AACA,QAAI,eAAe,QAAQ;AAEvB,eAAS;AACT,YAAM,OAAO,OAAO,KAAK,GAAG,EAAE,KAAK;AACnC,aAAM,CAAC,YAAY,QAAQ,KAAK,IAAI,CAAC,GAAE;AACnC,YAAI,CAAC,YAAY,IAAI,KAAK,CAAC,GAAG;AAC1B,oBAAU,QAAQ,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI;AAAA,QACrD;AAAA,MACJ;AACA,YAAM,IAAI,KAAK,MAAM;AAAA,IACzB;AAAA,EACJ,OAAO;AACH,aAAS,SAAS,IAAI,OAAO,IAAI,QAAQ,WAAW,IAAI,SAAS,IAAI,QAAQ,WAAW,KAAK,UAAU,GAAG,IAAI,KAAK;AAAA,EACvH;AACA,SAAO;AACX;AAGA,IAAM,iBAAiB,oBAAI,QAAQ;AAEnC,IAAM,cAAc,CAAC;AACrB,IAAM,gBAAgB,CAAC;AACvB,IAAM,gBAAgB;AAEtB,IAAM,kBAAkB,OAAO,UAAU;AACzC,IAAM,oBAAoB,OAAO,YAAY;AAC7C,IAAM,2BAA2B,MAAI,mBAAmB,OAAO,OAAO,uBAAuB,KAAK;AAClG,IAAM,oBAAoB,CAACC,QAAO,QAAM;AACpC,QAAM,QAAQ,eAAe,IAAIA,MAAK;AACtC,SAAO;AAAA;AAAA,IAEH,MAAI,CAAC,YAAY,GAAG,KAAKA,OAAM,IAAI,GAAG,KAAK;AAAA;AAAA,IAE3C,CAAC,SAAO;AACJ,UAAI,CAAC,YAAY,GAAG,GAAG;AACnB,cAAM,OAAOA,OAAM,IAAI,GAAG;AAG1B,YAAI,EAAE,OAAO,gBAAgB;AACzB,wBAAc,GAAG,IAAI;AAAA,QACzB;AACA,cAAM,CAAC,EAAE,KAAK,aAAa,MAAM,IAAI,GAAG,QAAQ,WAAW;AAAA,MAC/D;AAAA,IACJ;AAAA;AAAA,IAEA,MAAM,CAAC;AAAA;AAAA,IAEP,MAAI;AACA,UAAI,CAAC,YAAY,GAAG,GAAG;AAEnB,YAAI,OAAO;AAAe,iBAAO,cAAc,GAAG;AAAA,MACtD;AAEA,aAAO,CAAC,YAAY,GAAG,KAAKA,OAAM,IAAI,GAAG,KAAK;AAAA,IAClD;AAAA,EACJ;AACJ;AASI,IAAI,SAAS;AACjB,IAAM,WAAW,MAAI;AAErB,IAAM,CAAC,eAAe,cAAc,IAAI,mBAAmB,OAAO,mBAAmB;AAAA,EACjF,OAAO,iBAAiB,KAAK,MAAM;AAAA,EACnC,OAAO,oBAAoB,KAAK,MAAM;AAC1C,IAAI;AAAA,EACAD;AAAA,EACAA;AACJ;AACA,IAAM,YAAY,MAAI;AAClB,QAAM,kBAAkB,qBAAqB,SAAS;AACtD,SAAO,YAAY,eAAe,KAAK,oBAAoB;AAC/D;AACA,IAAM,YAAY,CAAC,aAAW;AAE1B,MAAI,mBAAmB;AACnB,aAAS,iBAAiB,oBAAoB,QAAQ;AAAA,EAC1D;AACA,gBAAc,SAAS,QAAQ;AAC/B,SAAO,MAAI;AACP,QAAI,mBAAmB;AACnB,eAAS,oBAAoB,oBAAoB,QAAQ;AAAA,IAC7D;AACA,mBAAe,SAAS,QAAQ;AAAA,EACpC;AACJ;AACA,IAAM,gBAAgB,CAAC,aAAW;AAE9B,QAAM,WAAW,MAAI;AACjB,aAAS;AACT,aAAS;AAAA,EACb;AAEA,QAAM,YAAY,MAAI;AAClB,aAAS;AAAA,EACb;AACA,gBAAc,UAAU,QAAQ;AAChC,gBAAc,WAAW,SAAS;AAClC,SAAO,MAAI;AACP,mBAAe,UAAU,QAAQ;AACjC,mBAAe,WAAW,SAAS;AAAA,EACvC;AACJ;AACA,IAAM,SAAS;AAAA,EACX;AAAA,EACA;AACJ;AACA,IAAM,uBAAuB;AAAA,EACzB;AAAA,EACA;AACJ;AAEA,IAAM,kBAAkB,CAAC,cAAAE,QAAM;AAC/B,IAAM,YAAY,CAAC,mBAAmB,UAAU;AAEhD,IAAM,MAAM,CAAC,MAAI,yBAAyB,IAAI,OAAO,uBAAuB,EAAE,CAAC,IAAI,WAAW,GAAG,CAAC;AAIlG,IAAM,4BAA4B,YAAY,0BAAY;AAE1D,IAAM,sBAAsB,OAAO,cAAc,eAAe,UAAU;AAE1E,IAAM,iBAAiB,CAAC,aAAa,wBAAwB;AAAA,EACzD;AAAA,EACA;AACJ,EAAE,SAAS,oBAAoB,aAAa,KAAK,oBAAoB;AAErE,IAAM,YAAY,CAAC,QAAM;AACrB,MAAI,WAAW,GAAG,GAAG;AACjB,QAAI;AACA,YAAM,IAAI;AAAA,IACd,SAAS,KAAK;AAEV,YAAM;AAAA,IACV;AAAA,EACJ;AAGA,QAAM,OAAO;AAEb,QAAM,OAAO,OAAO,WAAW,OAAO,MAAM,QAAQ,GAAG,IAAI,IAAI,SAAS,OAAO,WAAW,GAAG,IAAI;AACjG,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAGA,IAAI,cAAc;AAClB,IAAM,eAAe,MAAI,EAAE;AAE3B,IAAM,cAAc;AACpB,IAAM,kBAAkB;AACxB,IAAM,eAAe;AACrB,IAAM,yBAAyB;AAE/B,IAAI,YAAY;AAAA,EACd,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,eAAe,kBAAkB,MAAM;AACnC,QAAM,CAACD,QAAO,MAAM,OAAO,KAAK,IAAI;AAGpC,QAAM,UAAU,aAAa;AAAA,IACzB,eAAe;AAAA,IACf,cAAc;AAAA,EAClB,GAAG,OAAO,UAAU,YAAY;AAAA,IAC5B,YAAY;AAAA,EAChB,IAAI,SAAS,CAAC,CAAC;AACf,MAAI,gBAAgB,QAAQ;AAC5B,QAAM,wBAAwB,QAAQ;AACtC,MAAI,iBAAiB,QAAQ;AAC7B,QAAM,aAAa,QAAQ,eAAe;AAC1C,QAAM,kBAAkB,CAAC,UAAQ;AAC7B,WAAO,OAAO,0BAA0B,aAAa,sBAAsB,KAAK,IAAI,0BAA0B;AAAA,EAClH;AACA,QAAM,eAAe,QAAQ;AAG7B,MAAI,WAAW,IAAI,GAAG;AAClB,UAAM,YAAY;AAClB,UAAM,cAAc,CAAC;AACrB,UAAM,KAAKA,OAAM,KAAK;AACtB,eAAW,OAAO,IAAG;AACjB;AAAA;AAAA,QACA,CAAC,iBAAiB,KAAK,GAAG,KAAK,UAAUA,OAAM,IAAI,GAAG,EAAE,EAAE;AAAA,QAAG;AACzD,oBAAY,KAAK,GAAG;AAAA,MACxB;AAAA,IACJ;AACA,WAAO,QAAQ,IAAI,YAAY,IAAI,WAAW,CAAC;AAAA,EACnD;AACA,SAAO,YAAY,IAAI;AACvB,iBAAe,YAAY,IAAI;AAE3B,UAAM,CAAC,GAAG,IAAI,UAAU,EAAE;AAC1B,QAAI,CAAC;AAAK;AACV,UAAM,CAAC,KAAK,GAAG,IAAI,kBAAkBA,QAAO,GAAG;AAC/C,UAAM,CAAC,oBAAoB,UAAU,OAAO,OAAO,IAAI,eAAe,IAAIA,MAAK;AAC/E,UAAM,eAAe,mBAAmB,GAAG;AAC3C,UAAM,kBAAkB,MAAI;AACxB,UAAI,YAAY;AAGZ,eAAO,MAAM,GAAG;AAChB,eAAO,QAAQ,GAAG;AAClB,YAAI,gBAAgB,aAAa,CAAC,GAAG;AACjC,iBAAO,aAAa,CAAC,EAAE,YAAY,EAAE,KAAK,MAAI,IAAI,EAAE,IAAI;AAAA,QAC5D;AAAA,MACJ;AACA,aAAO,IAAI,EAAE;AAAA,IACjB;AAEA,QAAI,KAAK,SAAS,GAAG;AAEjB,aAAO,gBAAgB;AAAA,IAC3B;AACA,QAAI,OAAO;AACX,QAAI;AAEJ,UAAM,mBAAmB,aAAa;AACtC,aAAS,GAAG,IAAI;AAAA,MACZ;AAAA,MACA;AAAA,IACJ;AACA,UAAM,oBAAoB,CAAC,YAAY,cAAc;AACrD,UAAM,QAAQ,IAAI;AAIlB,UAAM,gBAAgB,MAAM;AAC5B,UAAM,cAAc,MAAM;AAC1B,UAAM,gBAAgB,YAAY,WAAW,IAAI,gBAAgB;AAEjE,QAAI,mBAAmB;AACnB,uBAAiB,WAAW,cAAc,IAAI,eAAe,eAAe,aAAa,IAAI;AAE7F,UAAI;AAAA,QACA,MAAM;AAAA,QACN,IAAI;AAAA,MACR,CAAC;AAAA,IACL;AACA,QAAI,WAAW,IAAI,GAAG;AAElB,UAAI;AACA,eAAO,KAAK,aAAa;AAAA,MAC7B,SAAS,KAAK;AAEV,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAEA,QAAI,QAAQ,cAAc,IAAI,GAAG;AAG7B,aAAO,MAAM,KAAK,MAAM,CAAC,QAAM;AAC3B,gBAAQ;AAAA,MACZ,CAAC;AAID,UAAI,qBAAqB,SAAS,GAAG,EAAE,CAAC,GAAG;AACvC,YAAI;AAAO,gBAAM;AACjB,eAAO;AAAA,MACX,WAAW,SAAS,qBAAqB,gBAAgB,KAAK,GAAG;AAG7D,wBAAgB;AAChB,eAAO;AAEP,YAAI;AAAA,UACA;AAAA,UACA,IAAI;AAAA,QACR,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,QAAI,eAAe;AACf,UAAI,CAAC,OAAO;AAER,YAAI,WAAW,aAAa,GAAG;AAC3B,iBAAO,cAAc,MAAM,aAAa;AAAA,QAC5C;AAEA,YAAI;AAAA,UACA;AAAA,UACA,OAAO;AAAA,UACP,IAAI;AAAA,QACR,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,aAAS,GAAG,EAAE,CAAC,IAAI,aAAa;AAEhC,UAAM,MAAM,MAAM,gBAAgB;AAGlC,QAAI;AAAA,MACA,IAAI;AAAA,IACR,CAAC;AAED,QAAI,OAAO;AACP,UAAI;AAAc,cAAM;AACxB;AAAA,IACJ;AACA,WAAO,gBAAgB,MAAM;AAAA,EACjC;AACJ;AAEA,IAAM,oBAAoB,CAAC,cAAc,SAAO;AAC5C,aAAU,OAAO,cAAa;AAC1B,QAAI,aAAa,GAAG,EAAE,CAAC;AAAG,mBAAa,GAAG,EAAE,CAAC,EAAE,IAAI;AAAA,EACvD;AACJ;AACA,IAAM,YAAY,CAAC,UAAU,YAAU;AAMnC,MAAI,CAAC,eAAe,IAAI,QAAQ,GAAG;AAC/B,UAAM,OAAO,aAAa,sBAAsB,OAAO;AAGvD,UAAM,qBAAqB,CAAC;AAC5B,UAAME,UAAS,eAAe,KAAK,WAAW,QAAQ;AACtD,QAAI,UAAUH;AACd,UAAM,gBAAgB,CAAC;AACvB,UAAM,YAAY,CAAC,KAAK,aAAW;AAC/B,YAAM,OAAO,cAAc,GAAG,KAAK,CAAC;AACpC,oBAAc,GAAG,IAAI;AACrB,WAAK,KAAK,QAAQ;AAClB,aAAO,MAAI,KAAK,OAAO,KAAK,QAAQ,QAAQ,GAAG,CAAC;AAAA,IACpD;AACA,UAAM,SAAS,CAAC,KAAK,OAAO,SAAO;AAC/B,eAAS,IAAI,KAAK,KAAK;AACvB,YAAM,OAAO,cAAc,GAAG;AAC9B,UAAI,MAAM;AACN,mBAAW,MAAM,MAAK;AAClB,aAAG,OAAO,IAAI;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,eAAe,MAAI;AACrB,UAAI,CAAC,eAAe,IAAI,QAAQ,GAAG;AAE/B,uBAAe,IAAI,UAAU;AAAA,UACzB;AAAA,UACA,CAAC;AAAA,UACD,CAAC;AAAA,UACD,CAAC;AAAA,UACDG;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AACD,YAAI,CAAC,WAAW;AAOZ,gBAAM,eAAe,KAAK,UAAU,WAAW,KAAK,WAAW,kBAAkB,KAAK,WAAW,oBAAoB,WAAW,CAAC,CAAC;AAClI,gBAAM,mBAAmB,KAAK,cAAc,WAAW,KAAK,WAAW,kBAAkB,KAAK,WAAW,oBAAoB,eAAe,CAAC,CAAC;AAC9I,oBAAU,MAAI;AACV,4BAAgB,aAAa;AAC7B,gCAAoB,iBAAiB;AAIrC,2BAAe,OAAO,QAAQ;AAAA,UAClC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,iBAAa;AAMb,WAAO;AAAA,MACH;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA,eAAe,IAAI,QAAQ,EAAE,CAAC;AAAA,EAClC;AACJ;AAGA,IAAM,eAAe,CAAC,GAAG,IAAI,QAAQ,YAAY,SAAO;AACpD,QAAM,gBAAgB,OAAO;AAC7B,QAAM,oBAAoB,KAAK;AAE/B,QAAM,UAAU,CAAC,GAAG,KAAK,OAAO,IAAI,QAAQ,MAAM,oBAAoB,IAAI,oBAAoB,OAAO,OAAO;AAC5G,MAAI,CAAC,YAAY,aAAa,KAAK,oBAAoB,eAAe;AAClE;AAAA,EACJ;AACA,aAAW,YAAY,SAAS,IAAI;AACxC;AACA,IAAM,UAAU,CAAC,aAAa,YAAU,WAAW,WAAW,KAAK,WAAW,OAAO;AAErF,IAAM,CAAC,OAAO,MAAM,IAAI,UAAU,oBAAI,IAAI,CAAC;AAE3C,IAAM,gBAAgB;AAAA,EAAa;AAAA;AAAA,IAE/B,eAAeH;AAAA,IACf,WAAWA;AAAA,IACX,SAASA;AAAA,IACT;AAAA,IACA,aAAaA;AAAA;AAAA,IAEb,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA;AAAA,IAEpB,oBAAoB,iBAAiB,MAAQ;AAAA,IAC7C,uBAAuB,IAAI;AAAA,IAC3B,kBAAkB,IAAI;AAAA,IACtB,gBAAgB,iBAAiB,MAAO;AAAA;AAAA,IAExC;AAAA,IACA,UAAU,MAAI;AAAA,IACd;AAAA,IACA;AAAA,IACA,UAAU,CAAC;AAAA,EACf;AAAA;AAAA,EACA;AAAM;AAEN,IAAM,eAAe,CAAC,GAAG,MAAI;AAEzB,QAAM,IAAI,aAAa,GAAG,CAAC;AAE3B,MAAI,GAAG;AACH,UAAM,EAAE,KAAK,IAAK,UAAU,GAAI,IAAI;AACpC,UAAM,EAAE,KAAK,IAAK,UAAU,GAAI,IAAI;AACpC,QAAI,MAAM,IAAI;AACV,QAAE,MAAM,GAAG,OAAO,EAAE;AAAA,IACxB;AACA,QAAI,MAAM,IAAI;AACV,QAAE,WAAW,aAAa,IAAI,EAAE;AAAA,IACpC;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,uBAAmB,6BAAc,CAAC,CAAC;AACzC,IAAM,YAAY,CAAC,UAAQ;AACvB,QAAM,EAAE,MAAO,IAAI;AACnB,QAAM,mBAAe,0BAAW,gBAAgB;AAChD,QAAM,qBAAqB,WAAW,KAAK;AAC3C,QAAM,aAAS,uBAAQ,MAAI,qBAAqB,MAAM,YAAY,IAAI,OAAO;AAAA,IACzE;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,qBAAiB,uBAAQ,MAAI,qBAAqB,SAAS,aAAa,cAAc,MAAM,GAAG;AAAA,IACjG;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,WAAW,UAAU,OAAO;AAElC,QAAM,sBAAkB,sBAAO,SAAS;AACxC,MAAI,YAAY,CAAC,gBAAgB,SAAS;AACtC,oBAAgB,UAAU,UAAU,SAAS,eAAe,SAAS,KAAK,GAAG,MAAM;AAAA,EACvF;AACA,QAAM,eAAe,gBAAgB;AAErC,MAAI,cAAc;AACd,mBAAe,QAAQ,aAAa,CAAC;AACrC,mBAAe,SAAS,aAAa,CAAC;AAAA,EAC1C;AAEA,4BAA0B,MAAI;AAC1B,QAAI,cAAc;AACd,mBAAa,CAAC,KAAK,aAAa,CAAC,EAAE;AACnC,aAAO,aAAa,CAAC;AAAA,IACzB;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,aAAO,6BAAc,iBAAiB,UAAU,aAAa,OAAO;AAAA,IAChE,OAAO;AAAA,EACX,CAAC,CAAC;AACN;AAGA,IAAM,iBAAiB,mBAAmB,OAAO;AACjD,IAAM,MAAM,iBAAiB,OAAO,uBAAuB,CAAC;AAC5D,IAAM,gBAAgB,MAAI;AACtB,MAAI,gBAAgB;AAEhB,WAAO,yBAAyB,cAAAE;AAAA,EACpC;AACJ;AAEA,IAAM,YAAY,CAAC,SAAO;AACtB,SAAO,WAAW,KAAK,CAAC,CAAC,IAAI;AAAA,IACzB,KAAK,CAAC;AAAA,IACN,KAAK,CAAC;AAAA,IACN,KAAK,CAAC,KAAK,CAAC;AAAA,EAChB,IAAI;AAAA,IACA,KAAK,CAAC;AAAA,IACN;AAAA,KACC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;AAAA,EAC/C;AACJ;AAEA,IAAM,eAAe,MAAI;AACrB,SAAO,aAAa,mBAAe,0BAAW,gBAAgB,CAAC;AACnE;AAEA,IAAM,UAAU,CAAC,MAAM,YAAU;AAC7B,QAAM,CAAC,KAAK,KAAK,IAAI,UAAU,IAAI;AACnC,QAAM,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,eAAe,IAAI,KAAK;AAEhD,MAAI,QAAQ,GAAG;AAAG,WAAO,QAAQ,GAAG;AACpC,QAAM,MAAM,QAAQ,KAAK;AACzB,UAAQ,GAAG,IAAI;AACf,SAAO;AACX;AACA,IAAM,aAAa,CAAC,eAAa,CAAC,MAAM,UAAU,WAAS;AAEnD,QAAM,UAAU,aAAa,IAAI,SAAO;AACpC,UAAM,CAAC,GAAG,IAAI,UAAU,IAAI;AAC5B,UAAM,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,eAAe,IAAI,KAAK;AAChD,UAAM,MAAM,QAAQ,GAAG;AACvB,QAAI,YAAY,GAAG;AAAG,aAAO,SAAS,GAAG,IAAI;AAC7C,WAAO,QAAQ,GAAG;AAClB,WAAO;AAAA,EACX;AACA,SAAO,WAAW,MAAM,SAAS,MAAM;AAC3C;AAEJ,IAAM,sBAAsB,IAAI,OAAO,UAAU;AAIjD,IAAM,WAAW,CAAC,SAAO;AACrB,SAAO,SAAS,cAAc,MAAM;AAEhC,UAAM,iBAAiB,aAAa;AAEpC,UAAM,CAAC,KAAK,IAAI,OAAO,IAAI,UAAU,IAAI;AAEzC,UAAM,SAAS,aAAa,gBAAgB,OAAO;AAEnD,QAAI,OAAO;AACX,UAAM,EAAE,KAAAE,KAAK,IAAI;AACjB,UAAMC,eAAcD,QAAO,CAAC,GAAG,OAAO,mBAAmB;AACzD,aAAQ,IAAIC,YAAW,QAAQ,OAAK;AAChC,aAAOA,YAAW,CAAC,EAAE,IAAI;AAAA,IAC7B;AACA,WAAO,KAAK,KAAK,MAAM,OAAO,WAAW,MAAM,MAAM;AAAA,EACzD;AACJ;AA0EA,IAAM,oBAAoB,CAAC,KAAK,WAAW,aAAW;AAClD,QAAM,oBAAoB,UAAU,GAAG,MAAM,UAAU,GAAG,IAAI,CAAC;AAC/D,oBAAkB,KAAK,QAAQ;AAC/B,SAAO,MAAI;AACP,UAAM,QAAQ,kBAAkB,QAAQ,QAAQ;AAChD,QAAI,SAAS,GAAG;AAEZ,wBAAkB,KAAK,IAAI,kBAAkB,kBAAkB,SAAS,CAAC;AACzE,wBAAkB,IAAI;AAAA,IAC1B;AAAA,EACJ;AACJ;AAGA,IAAM,iBAAiB,CAACC,SAAQC,gBAAa;AACzC,SAAO,IAAI,SAAO;AACd,UAAM,CAAC,KAAK,IAAI,MAAM,IAAI,UAAU,IAAI;AACxC,UAAM,QAAQ,OAAO,OAAO,CAAC,GAAG,OAAOA,WAAU;AACjD,WAAOD,QAAO,KAAK,IAAI;AAAA,MACnB,GAAG;AAAA,MACH,KAAK;AAAA,IACT,CAAC;AAAA,EACL;AACJ;AAEA,cAAc;;;ADptBd,IAAM,qBAAqB,CAAC,QAAM,UAAU,GAAG,EAAE,CAAC;AAGlD,IAAME,OAAM,cAAAC,QAAa,QAAQ,CAAC,YAAU;AACxC,MAAI,QAAQ,WAAW,WAAW;AAC9B,UAAM;AAAA,EACV,WAAW,QAAQ,WAAW,aAAa;AACvC,WAAO,QAAQ;AAAA,EACnB,WAAW,QAAQ,WAAW,YAAY;AACtC,UAAM,QAAQ;AAAA,EAClB,OAAO;AACH,YAAQ,SAAS;AACjB,YAAQ,KAAK,CAAC,MAAI;AACd,cAAQ,SAAS;AACjB,cAAQ,QAAQ;AAAA,IACpB,GAAG,CAAC,MAAI;AACJ,cAAQ,SAAS;AACjB,cAAQ,SAAS;AAAA,IACrB,CAAC;AACD,UAAM;AAAA,EACV;AACJ;AACA,IAAM,cAAc;AAAA,EAChB,QAAQ;AACZ;AACA,IAAM,gBAAgB,CAAC,MAAM,SAAS,WAAS;AAC3C,QAAM,EAAE,OAAAC,QAAQ,SAAAC,UAAU,UAAW,cAAe,mBAAoB,mBAAoB,iBAAkB,mBAAoB,oBAAqB,iBAAkB,IAAI;AAC7K,QAAM,CAAC,oBAAoB,UAAU,OAAO,OAAO,IAAI,eAAe,IAAID,MAAK;AAK/E,QAAM,CAAC,KAAK,KAAK,IAAI,UAAU,IAAI;AAEnC,QAAM,wBAAoB,sBAAO,KAAK;AAGtC,QAAM,mBAAe,sBAAO,KAAK;AAEjC,QAAM,aAAS,sBAAO,GAAG;AACzB,QAAM,iBAAa,sBAAO,OAAO;AACjC,QAAM,gBAAY,sBAAO,MAAM;AAC/B,QAAM,YAAY,MAAI,UAAU;AAChC,QAAM,WAAW,MAAI,UAAU,EAAE,UAAU,KAAK,UAAU,EAAE,SAAS;AACrE,QAAM,CAAC,UAAU,UAAU,gBAAgB,eAAe,IAAI,kBAAkBA,QAAO,GAAG;AAC1F,QAAM,wBAAoB,sBAAO,CAAC,CAAC,EAAE;AACrC,QAAM,WAAW,YAAY,YAAY,IAAI,OAAO,SAAS,GAAG,IAAI;AACpE,QAAM,UAAU,CAAC,MAAM,YAAU;AAC7B,eAAU,KAAK,mBAAkB;AAC7B,YAAM,IAAI;AACV,UAAI,MAAM,QAAQ;AACd,YAAI,CAACC,SAAQ,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG;AAC/B,cAAI,CAAC,YAAY,KAAK,CAAC,CAAC,GAAG;AACvB,mBAAO;AAAA,UACX;AACA,cAAI,CAACA,SAAQ,cAAc,QAAQ,CAAC,CAAC,GAAG;AACpC,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ,OAAO;AACH,YAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG;AACxB,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,kBAAc,uBAAQ,MAAI;AAC5B,UAAM,sBAAsB,MAAI;AAC5B,UAAI,CAAC;AAAK,eAAO;AACjB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI,CAAC,YAAY,iBAAiB;AAAG,eAAO;AAE5C,UAAI,UAAU,EAAE,SAAS;AAAG,eAAO;AACnC,UAAI;AAAU,eAAO;AACrB,UAAI,CAAC,YAAY,iBAAiB;AAAG,eAAO;AAC5C,aAAO;AAAA,IACX,GAAG;AAEH,UAAM,mBAAmB,CAAC,UAAQ;AAE9B,YAAM,WAAW,aAAa,KAAK;AACnC,aAAO,SAAS;AAChB,UAAI,CAAC,oBAAoB;AACrB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,QACH,cAAc;AAAA,QACd,WAAW;AAAA,QACX,GAAG;AAAA,MACP;AAAA,IACJ;AACA,UAAMC,cAAa,SAAS;AAC5B,UAAM,cAAc,gBAAgB;AACpC,UAAM,iBAAiB,iBAAiBA,WAAU;AAClD,UAAM,iBAAiBA,gBAAe,cAAc,iBAAiB,iBAAiB,WAAW;AAIjG,QAAI,oBAAoB;AACxB,WAAO;AAAA,MACH,MAAI;AACA,cAAM,cAAc,iBAAiB,SAAS,CAAC;AAC/C,cAAM,gBAAgB,QAAQ,aAAa,iBAAiB;AAC5D,YAAI,eAAe;AAWf,4BAAkB,OAAO,YAAY;AACrC,4BAAkB,YAAY,YAAY;AAC1C,4BAAkB,eAAe,YAAY;AAC7C,4BAAkB,QAAQ,YAAY;AACtC,iBAAO;AAAA,QACX,OAAO;AACH,8BAAoB;AACpB,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA,MAAI;AAAA,IACR;AAAA,EAEJ,GAAG;AAAA,IACCF;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,aAAS,sCAAqB;AAAA,IAAY,CAAC,aAAW,eAAe,KAAK,CAAC,SAAS,SAAO;AACzF,UAAI,CAAC,QAAQ,MAAM,OAAO;AAAG,iBAAS;AAAA,IAC1C,CAAC;AAAA;AAAA,IACL;AAAA,MACIA;AAAA,MACA;AAAA,IACJ;AAAA,EAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAClC,QAAM,iBAAiB,CAAC,kBAAkB;AAC1C,QAAM,iBAAiB,mBAAmB,GAAG,KAAK,mBAAmB,GAAG,EAAE,SAAS;AACnF,QAAM,aAAa,OAAO;AAC1B,QAAM,OAAO,YAAY,UAAU,IAAI,WAAW;AAClD,QAAM,QAAQ,OAAO;AAErB,QAAM,mBAAe,sBAAO,IAAI;AAChC,QAAM,eAAe,mBAAmB,YAAY,UAAU,IAAI,aAAa,UAAU,aAAa;AAItG,QAAM,+BAA+B,MAAI;AAErC,QAAI,kBAAkB,CAAC,YAAY,KAAK;AAAG,aAAO;AAElD,QAAI,kBAAkB,CAAC,YAAY,iBAAiB;AAAG,aAAO;AAE9D,QAAI,UAAU,EAAE,SAAS;AAAG,aAAO;AAInC,QAAI;AAAU,aAAO,YAAY,IAAI,IAAI,QAAQ;AAGjD,WAAO,YAAY,IAAI,KAAK;AAAA,EAChC,GAAG;AAGH,QAAM,yBAAyB,CAAC,EAAE,OAAO,WAAW,kBAAkB;AACtE,QAAM,eAAe,YAAY,OAAO,YAAY,IAAI,yBAAyB,OAAO;AACxF,QAAM,YAAY,YAAY,OAAO,SAAS,IAAI,yBAAyB,OAAO;AAGlF,QAAM,iBAAa;AAAA,IAAY,OAAO,mBAAiB;AACnD,YAAM,iBAAiB,WAAW;AAClC,UAAI,CAAC,OAAO,CAAC,kBAAkB,aAAa,WAAW,UAAU,EAAE,SAAS,GAAG;AAC3E,eAAO;AAAA,MACX;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,UAAU;AACd,YAAM,OAAO,kBAAkB,CAAC;AAGhC,YAAM,wBAAwB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK;AAWlD,YAAM,oBAAoB,MAAI;AAC3B,YAAI,iBAAiB;AACjB,iBAAO,CAAC,aAAa,WAAW,QAAQ,OAAO,WAAW,kBAAkB;AAAA,QAChF;AACA,eAAO,QAAQ,OAAO;AAAA,MAC1B;AAEA,YAAM,aAAa;AAAA,QACf,cAAc;AAAA,QACd,WAAW;AAAA,MACf;AACA,YAAM,8BAA8B,MAAI;AACpC,iBAAS,UAAU;AAAA,MACvB;AACA,YAAM,eAAe,MAAI;AAErB,cAAM,cAAc,MAAM,GAAG;AAC7B,YAAI,eAAe,YAAY,CAAC,MAAM,SAAS;AAC3C,iBAAO,MAAM,GAAG;AAAA,QACpB;AAAA,MACJ;AAEA,YAAM,eAAe;AAAA,QACjB,cAAc;AAAA,MAClB;AAGA,UAAI,YAAY,SAAS,EAAE,IAAI,GAAG;AAC9B,qBAAa,YAAY;AAAA,MAC7B;AACA,UAAI;AACA,YAAI,uBAAuB;AACvB,mBAAS,YAAY;AAGrB,cAAI,OAAO,kBAAkB,YAAY,SAAS,EAAE,IAAI,GAAG;AACvD,uBAAW,MAAI;AACX,kBAAI,WAAW,kBAAkB,GAAG;AAChC,0BAAU,EAAE,cAAc,KAAK,MAAM;AAAA,cACzC;AAAA,YACJ,GAAG,OAAO,cAAc;AAAA,UAC5B;AAGA,gBAAM,GAAG,IAAI;AAAA,YACT,eAAe,KAAK;AAAA,YACpB,aAAa;AAAA,UACjB;AAAA,QACJ;AACA,SAAC,SAAS,OAAO,IAAI,MAAM,GAAG;AAC9B,kBAAU,MAAM;AAChB,YAAI,uBAAuB;AAGvB,qBAAW,cAAc,OAAO,gBAAgB;AAAA,QACpD;AAOA,YAAI,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG,EAAE,CAAC,MAAM,SAAS;AAC1C,cAAI,uBAAuB;AACvB,gBAAI,kBAAkB,GAAG;AACrB,wBAAU,EAAE,YAAY,GAAG;AAAA,YAC/B;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,mBAAW,QAAQ;AAanB,cAAM,eAAe,SAAS,GAAG;AACjC,YAAI,CAAC,YAAY,YAAY;AAAA,SAC5B,WAAW,aAAa,CAAC;AAAA,QAC1B,WAAW,aAAa,CAAC;AAAA,QACzB,aAAa,CAAC,MAAM,IAAI;AACpB,sCAA4B;AAC5B,cAAI,uBAAuB;AACvB,gBAAI,kBAAkB,GAAG;AACrB,wBAAU,EAAE,YAAY,GAAG;AAAA,YAC/B;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAGA,cAAM,YAAY,SAAS,EAAE;AAG7B,mBAAW,OAAOC,SAAQ,WAAW,OAAO,IAAI,YAAY;AAE5D,YAAI,uBAAuB;AACvB,cAAI,kBAAkB,GAAG;AACrB,sBAAU,EAAE,UAAU,SAAS,KAAK,MAAM;AAAA,UAC9C;AAAA,QACJ;AAAA,MACJ,SAAS,KAAK;AACV,qBAAa;AACb,cAAM,gBAAgB,UAAU;AAChC,cAAM,EAAE,mBAAoB,IAAI;AAEhC,YAAI,CAAC,cAAc,SAAS,GAAG;AAE3B,qBAAW,QAAQ;AAGnB,cAAI,yBAAyB,kBAAkB,GAAG;AAC9C,0BAAc,QAAQ,KAAK,KAAK,aAAa;AAC7C,gBAAI,uBAAuB,QAAQ,WAAW,kBAAkB,KAAK,mBAAmB,GAAG,GAAG;AAC1F,kBAAI,SAAS,GAAG;AAIZ,8BAAc,aAAa,KAAK,KAAK,eAAe,CAAC,UAAQ;AACzD,wBAAM,eAAe,mBAAmB,GAAG;AAC3C,sBAAI,gBAAgB,aAAa,CAAC,GAAG;AACjC,iCAAa,CAAC,EAAE,UAAiB,wBAAwB,KAAK;AAAA,kBAClE;AAAA,gBACJ,GAAG;AAAA,kBACC,aAAa,KAAK,cAAc,KAAK;AAAA,kBACrC,QAAQ;AAAA,gBACZ,CAAC;AAAA,cACL;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,gBAAU;AAEV,kCAA4B;AAC5B,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA;AAAA,MACI;AAAA,MACAD;AAAA,IACJ;AAAA,EAAC;AAID,QAAM,kBAAc;AAAA;AAAA,IACpB,IAAI,SAAO;AACP,aAAO,eAAeA,QAAO,OAAO,SAAS,GAAG,IAAI;AAAA,IACxD;AAAA;AAAA,IACA,CAAC;AAAA,EAAC;AAEF,4BAA0B,MAAI;AAC1B,eAAW,UAAU;AACrB,cAAU,UAAU;AAGpB,QAAI,CAAC,YAAY,UAAU,GAAG;AAC1B,mBAAa,UAAU;AAAA,IAC3B;AAAA,EACJ,CAAC;AAED,4BAA0B,MAAI;AAC1B,QAAI,CAAC;AAAK;AACV,UAAM,iBAAiB,WAAW,KAAK,WAAW,WAAW;AAG7D,QAAI,yBAAyB;AAC7B,UAAM,eAAe,CAAC,MAAM,OAAO,CAAC,MAAI;AACpC,UAAI,QAAQ,UAAiB,aAAa;AACtC,cAAM,MAAM,KAAK,IAAI;AACrB,YAAI,UAAU,EAAE,qBAAqB,MAAM,0BAA0B,SAAS,GAAG;AAC7E,mCAAyB,MAAM,UAAU,EAAE;AAC3C,yBAAe;AAAA,QACnB;AAAA,MACJ,WAAW,QAAQ,UAAiB,iBAAiB;AACjD,YAAI,UAAU,EAAE,yBAAyB,SAAS,GAAG;AACjD,yBAAe;AAAA,QACnB;AAAA,MACJ,WAAW,QAAQ,UAAiB,cAAc;AAC9C,eAAO,WAAW;AAAA,MACtB,WAAW,QAAQ,UAAiB,wBAAwB;AACxD,eAAO,WAAW,IAAI;AAAA,MAC1B;AACA;AAAA,IACJ;AACA,UAAM,cAAc,kBAAkB,KAAK,oBAAoB,YAAY;AAE3E,iBAAa,UAAU;AACvB,WAAO,UAAU;AACjB,sBAAkB,UAAU;AAE5B,aAAS;AAAA,MACL,IAAI;AAAA,IACR,CAAC;AAED,QAAI,6BAA6B;AAC7B,UAAI,YAAY,IAAI,KAAK,WAAW;AAEhC,uBAAe;AAAA,MACnB,OAAO;AAGH,YAAI,cAAc;AAAA,MACtB;AAAA,IACJ;AACA,WAAO,MAAI;AAEP,mBAAa,UAAU;AACvB,kBAAY;AAAA,IAChB;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,EACJ,CAAC;AAED,4BAA0B,MAAI;AAC1B,QAAI;AACJ,aAAS,OAAO;AAGZ,YAAM,WAAW,WAAW,eAAe,IAAI,gBAAgB,SAAS,EAAE,IAAI,IAAI;AAIlF,UAAI,YAAY,UAAU,IAAI;AAC1B,gBAAQ,WAAW,SAAS,QAAQ;AAAA,MACxC;AAAA,IACJ;AACA,aAAS,UAAU;AAGf,UAAI,CAAC,SAAS,EAAE,UAAU,qBAAqB,UAAU,EAAE,UAAU,OAAO,sBAAsB,UAAU,EAAE,SAAS,IAAI;AACvH,mBAAW,WAAW,EAAE,KAAK,IAAI;AAAA,MACrC,OAAO;AAEH,aAAK;AAAA,MACT;AAAA,IACJ;AACA,SAAK;AACL,WAAO,MAAI;AACP,UAAI,OAAO;AACP,qBAAa,KAAK;AAClB,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,mCAAc,YAAY;AAK1B,MAAI,YAAY,YAAY,IAAI,KAAK,KAAK;AAItC,QAAI,CAAC,mBAAmB,WAAW;AAC/B,YAAM,IAAI,MAAM,uDAAuD;AAAA,IAC3E;AAEA,eAAW,UAAU;AACrB,cAAU,UAAU;AACpB,iBAAa,UAAU;AACvB,UAAM,MAAM,QAAQ,GAAG;AACvB,QAAI,CAAC,YAAY,GAAG,GAAG;AACnB,YAAM,UAAU,YAAY,GAAG;AAC/B,MAAAF,KAAI,OAAO;AAAA,IACf;AACA,QAAI,YAAY,KAAK,GAAG;AACpB,YAAM,UAAU,WAAW,WAAW;AACtC,UAAI,CAAC,YAAY,YAAY,GAAG;AAC5B,gBAAQ,SAAS;AACjB,gBAAQ,QAAQ;AAAA,MACpB;AACA,MAAAA,KAAI,OAAO;AAAA,IACf,OAAO;AACH,YAAM;AAAA,IACV;AAAA,EACJ;AACA,SAAO;AAAA,IACH,QAAQ;AAAA,IACR,IAAI,OAAQ;AACR,wBAAkB,OAAO;AACzB,aAAO;AAAA,IACX;AAAA,IACA,IAAI,QAAS;AACT,wBAAkB,QAAQ;AAC1B,aAAO;AAAA,IACX;AAAA,IACA,IAAI,eAAgB;AAChB,wBAAkB,eAAe;AACjC,aAAO;AAAA,IACX;AAAA,IACA,IAAI,YAAa;AACb,wBAAkB,YAAY;AAC9B,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,IAAMK,aAAY,OAAO,eAAe,WAAa,gBAAgB;AAAA,EACjE,OAAO;AACX,CAAC;AAeG,IAAM,SAAS,SAAS,aAAa;;;AE3hBzC,IAAAC,gBAAoC;AAGpC,IAAAC,eAAqC;AAErC,IAAM,kBAAkB;AACxB,IAAM,kBAAkB,CAAC,WAAS;AAC9B,SAAO,UAAU,SAAS,OAAO,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC;AACvD;AAOA,IAAM,gBAAgB,QAAQ,QAAQ;AAItC,IAAM,WAAW,CAAC,eAAa,CAAC,QAAQ,IAAI,WAAS;AAC7C,QAAM,kBAAc,sBAAO,KAAK;AAChC,QAAM,EAAE,OAAAC,QAAQ,cAAa,GAAI,gBAAe,OAAQ,cAAa,OAAQ,sBAAqB,MAAO,oBAAmB,OAAQ,WAAU,MAAO,IAAI;AAGzJ,MAAI;AACJ,MAAI;AACA,kBAAc,gBAAgB,MAAM;AACpC,QAAI;AAAa,oBAAc,kBAAkB;AAAA,EACrD,SAAS,KAAK;AAAA,EAEd;AACA,QAAM,CAAC,KAAK,KAAK,cAAc,IAAI,kBAAkBA,QAAO,WAAW;AACvE,QAAM,kBAAc,2BAAY,MAAI;AAChC,UAAM,OAAO,YAAY,IAAI,EAAE,EAAE,IAAI,cAAc,IAAI,EAAE;AACzD,WAAO;AAAA,EAEX,GAAG;AAAA,IACCA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,6CAAqB;AAAA,IAAY,CAAC,aAAW;AACzC,UAAI;AAAa,eAAO,eAAe,aAAa,MAAI;AACpD,mBAAS;AAAA,QACb,CAAC;AACD,aAAO,MAAI;AAAA,MAAC;AAAA,IAChB;AAAA;AAAA,IACA;AAAA,MACIA;AAAA,MACA;AAAA,IACJ;AAAA,EAAC,GAAG,aAAa,WAAW;AAC5B,QAAM,sBAAkB,2BAAY,MAAI;AACpC,UAAM,iBAAiB,IAAI,EAAE;AAC7B,WAAO,YAAY,cAAc,IAAI,cAAc;AAAA,EAGvD,GAAG;AAAA,IACC;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,sBAAkB,sBAAO,gBAAgB,CAAC;AAEhD,4BAA0B,MAAI;AAC1B,QAAI,CAAC,YAAY,SAAS;AACtB,kBAAY,UAAU;AACtB;AAAA,IACJ;AACA,QAAI,aAAa;AAGb,UAAI;AAAA,QACA,IAAI,cAAc,gBAAgB,UAAU,gBAAgB;AAAA,MAChE,CAAC;AAAA,IACL;AAAA,EAGJ,GAAG;AAAA,IACC;AAAA,IACAA;AAAA,EACJ,CAAC;AAED,QAAM,0BAA0B,qBAAqB,CAAC,YAAY;AAElE,QAAM,MAAM,WAAW,aAAa,OAAO,QAAM;AAE7C,UAAM,qBAAqB,IAAI,EAAE;AAEjC,UAAM,OAAO,CAAC;AACd,UAAM,WAAW,gBAAgB;AACjC,UAAM,CAAC,QAAQ,IAAI,kBAAkBA,QAAO,GAAG;AAC/C,UAAM,YAAY,SAAS,EAAE;AAC7B,UAAM,eAAe,CAAC;AACtB,QAAI,mBAAmB;AACvB,aAAQ,IAAI,GAAG,IAAI,UAAU,EAAE,GAAE;AAC7B,YAAM,CAAC,SAAS,OAAO,IAAI,UAAU,OAAO,GAAG,WAAW,OAAO,gBAAgB,CAAC;AAClF,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AACA,YAAM,CAAC,aAAa,WAAW,IAAI,kBAAkBA,QAAO,OAAO;AAEnE,UAAI,WAAW,YAAY,EAAE;AAQ7B,YAAM,kBAAkB,iBAAiB,sBAAsB,YAAY,QAAQ,KAAK,uBAAuB,CAAC,KAAK,CAAC,YAAY,SAAS,KAAK,2BAA2B,aAAa,CAAC,YAAY,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,QAAQ,UAAU,CAAC,GAAG,QAAQ;AAC5P,UAAI,MAAM,iBAAiB;AACvB,cAAM,aAAa,YAAU;AACzB,qBAAW,MAAM,GAAG,OAAO;AAC3B,sBAAY;AAAA,YACR,MAAM;AAAA,YACN,IAAI;AAAA,UACR,CAAC;AACD,eAAK,CAAC,IAAI;AAAA,QACd;AACA,YAAI,UAAU;AACV,uBAAa,KAAK,UAAU;AAAA,QAChC,OAAO;AACH,gBAAM,WAAW;AAAA,QACrB;AAAA,MACJ,OAAO;AACH,aAAK,CAAC,IAAI;AAAA,MACd;AACA,UAAI,CAAC,UAAU;AACX,2BAAmB;AAAA,MACvB;AAAA,IACJ;AAEA,QAAI,UAAU;AACV,YAAM,QAAQ,IAAI,aAAa,IAAI,CAAC,MAAI,EAAE,CAAC,CAAC;AAAA,IAChD;AAEA,QAAI;AAAA,MACA,IAAI;AAAA,IACR,CAAC;AAED,WAAO;AAAA,EACX,GAAG,MAAM;AACT,QAAMC,cAAS;AAAA;AAAA,IACf,SAAS,MAAM,MAAM;AAGjB,YAAM,UAAU,OAAO,SAAS,YAAY;AAAA,QACxC,YAAY;AAAA,MAChB,IAAI,QAAQ,CAAC;AAEb,YAAM,mBAAmB,QAAQ,eAAe;AAEhD,UAAI,CAAC;AAAa,eAAO;AACzB,UAAI,kBAAkB;AAClB,YAAI,CAAC,YAAY,IAAI,GAAG;AAEpB,cAAI;AAAA,YACA,IAAI;AAAA,UACR,CAAC;AAAA,QACL,OAAO;AAEH,cAAI;AAAA,YACA,IAAI;AAAA,UACR,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO,UAAU,SAAS,IAAI,OAAO,MAAM;AAAA,QACvC,GAAG;AAAA,QACH,YAAY;AAAA,MAChB,CAAC,IAAI,IAAI,OAAO;AAAA,IACpB;AAAA;AAAA;AAAA,IAEA;AAAA,MACI;AAAA,MACAD;AAAA,IACJ;AAAA,EAAC;AAED,QAAM,cAAU;AAAA,IAAY,CAAC,QAAM;AAE/B,UAAI,CAAC;AAAa,eAAO;AACzB,YAAM,CAAC,EAAE,UAAU,IAAI,kBAAkBA,QAAO,WAAW;AAC3D,UAAI;AACJ,UAAI,WAAW,GAAG,GAAG;AACjB,eAAO,IAAI,gBAAgB,CAAC;AAAA,MAChC,WAAW,OAAO,OAAO,UAAU;AAC/B,eAAO;AAAA,MACX;AACA,UAAI,OAAO,QAAQ;AAAU,eAAO;AACpC,iBAAW;AAAA,QACP,IAAI;AAAA,MACR,CAAC;AACD,sBAAgB,UAAU;AAE1B,YAAM,OAAO,CAAC;AACd,YAAM,CAAC,gBAAgB,IAAI,kBAAkBA,QAAO,WAAW;AAC/D,UAAI,mBAAmB;AACvB,eAAQ,IAAI,GAAG,IAAI,MAAM,EAAE,GAAE;AACzB,cAAM,CAAC,OAAO,IAAI,UAAU,OAAO,GAAG,gBAAgB,CAAC;AACvD,cAAM,CAAC,QAAQ,IAAI,kBAAkBA,QAAO,OAAO;AAEnD,cAAM,WAAW,UAAU,SAAS,EAAE,OAAO;AAE7C,YAAI,YAAY,QAAQ,GAAG;AACvB,iBAAOC,QAAO,iBAAiB,EAAE,IAAI;AAAA,QACzC;AACA,aAAK,KAAK,QAAQ;AAClB,2BAAmB;AAAA,MACvB;AACA,aAAOA,QAAO,IAAI;AAAA,IACtB;AAAA;AAAA;AAAA,IAEA;AAAA,MACI;AAAA,MACAD;AAAA,MACAC;AAAA,MACA;AAAA,IACJ;AAAA,EAAC;AAGD,SAAO;AAAA,IACH,MAAM,gBAAgB;AAAA,IACtB;AAAA,IACA,QAAAA;AAAA,IACA,IAAI,OAAQ;AACR,aAAO,IAAI;AAAA,IACf;AAAA,IACA,IAAI,QAAS;AACT,aAAO,IAAI;AAAA,IACf;AAAA,IACA,IAAI,eAAgB;AAChB,aAAO,IAAI;AAAA,IACf;AAAA,IACA,IAAI,YAAa;AACb,aAAO,IAAI;AAAA,IACf;AAAA,EACJ;AACJ;AACJ,IAAM,iBAAiB,eAAe,QAAQ,QAAQ;;;ARjOtD,IAAAC,gBAAkB;ACVlB,IAAAA,gBAAuD;AIFvD,IAAAA,iBAAkB;APGX,SAAS,oBAAoB,YAAqB,UAA2D;AAClH,MAAI,CAAC,YAAY;AACf,UAAM,OAAO,aAAa,WAAW,IAAI,MAAM,QAAQ,IAAI,IAAI,MAAM,GAAG,SAAS,WAAW,YAAY;EAC1G;AACF;AAYO,IAAM,uBAAuB,CAClC,aACA,YAC8E;AAC9E,QAAM,EAAE,cAAc,oBAAoB,IAAI,WAAW,CAAC;AAC1D,QAAM,MAAM,cAAAC,QAAM,cAA6C,MAAS;AACxE,MAAI,cAAc;AAElB,QAAM,SAAS,MAAM;AACnB,UAAM,MAAM,cAAAA,QAAM,WAAW,GAAG;AAChC,gBAAY,KAAK,GAAG,WAAW,YAAY;AAC3C,WAAQ,IAAY;EACtB;AAEA,QAAM,yBAAyB,MAAM;AACnC,UAAM,MAAM,cAAAA,QAAM,WAAW,GAAG;AAChC,WAAO,MAAM,IAAI,QAAQ,CAAC;EAC5B;AAEA,SAAO,CAAC,KAAK,QAAQ,sBAAsB;AAC7C;ACvCA,IAAA,oBAAA,CAAA;AAAAC,UAAA,mBAAA;EAAA,WAAA,MAAAC;EAAA,QAAA,MAAAC;EAAA,gBAAA,MAAAA;AAAA,CAAA;AACA,WAAA,mBAAA,YAAA;ACiBA,IAAM,CAAC,sBAAsB,uBAAuB,IAAI,qBAAkC,sBAAsB;AAChH,IAAM,CAAC,aAAa,cAAc,IAAI,qBAAsD,aAAa;AACzG,IAAM,CAAC,eAAe,gBAAgB,IAAI,qBAAwD,eAAe;AACjH,IAAM,CAAC,gBAAgB,iBAAiB,IAAI;EAC1C;AACF;AAgBA,IAAM,CAAC,6BAA6B,sBAAsB,IAAI,qBAI3D,qBAAqB;AAExB,IAAM,uBAAuB,CAAC;EAC5B;EACA;EACA;EACA;EACA;AACF,MAKM;AACJ,SACEH,cAAAA,QAAA,cAACE,YAAA,EAAU,OAAO,UAAA,GAChBF,cAAAA,QAAA;IAAC,4BAA4B;IAA5B;MACC,OAAO;QACL,OAAO;UACL;UACA;UACA;QACF;MACF;IAAA;IAEC;EACH,CACF;AAEJ;ACjEA,SAAS,iBAAiB,MAA+B,MAAwD;AAC/G,QAAM,UAAU,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC;AACzC,QAAM,sBAA+C,CAAC;AAEtD,aAAW,QAAQ,OAAO,KAAK,IAAI,GAAG;AACpC,QAAI,CAAC,QAAQ,IAAI,IAAI,GAAG;AACtB,0BAAoB,IAAI,IAAI,KAAK,IAAI;IACvC;EACF;AAEA,SAAO;AACT;AAaO,IAAM,oBAAoB,CAAmC,QAA8B,kBAAqB;AA/BvH,MAAA,IAAA,IAAA;AAgCE,QAAM,oBAAoB,OAAO,WAAW,aAAa;AAGzD,QAAM,qBAAiB;IACrB,oBAAoB,cAAc,eAAc,KAAA,UAAA,OAAA,SAAA,OAAQ,gBAAR,OAAA,KAAuB,cAAc;EACvF;AACA,QAAM,kBAAc,sBAAO,oBAAoB,cAAc,YAAW,KAAA,UAAA,OAAA,SAAA,OAAQ,aAAR,OAAA,KAAoB,cAAc,QAAQ;AAElH,QAAM,SAAkC,CAAC;AACzC,aAAW,OAAO,OAAO,KAAK,aAAa,GAAG;AAE5C,WAAO,GAAG,IAAI,oBAAoB,cAAc,GAAG,KAAI,KAAA,UAAA,OAAA,SAAA,OAAS,GAAA,MAAT,OAAA,KAAiB,cAAc,GAAG;EAC3F;AAEA,SAAO;IACL,GAAG;IACH,aAAa,eAAe;IAC5B,UAAU,YAAY;EACxB;AACF;AAyCO,IAAM,qBAAyC,CAAC,QAAQ,SAAS,SAAS,cAAc;AA5F/F,MAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AA6FE,QAAM,CAAC,eAAe,gBAAgB,QAAI,yBAAS,KAAA,OAAO,gBAAP,OAAA,KAAsB,CAAC;AAG1E,QAAM,qBAAiB,uBAAO,KAAA,OAAO,gBAAP,OAAA,KAAsB,CAAC;AACrD,QAAM,kBAAc,uBAAO,KAAA,OAAO,aAAP,OAAA,KAAmB,EAAE;AAEhD,QAAM,WAAU,KAAA,QAAQ,YAAR,OAAA,KAAmB;AACnC,QAAM,mBAAkB,KAAA,QAAQ,aAAR,OAAA,KAAoB;AAC5C,QAAM,oBAAmB,KAAA,QAAQ,qBAAR,OAAA,KAA4B;AAErD,QAAM,gBAAgB;IACpB,GAAG;IACH,GAAG;IACH,aAAa;IACb,UAAU,YAAY;EACxB;AAEA,QAAM;IACJ,MAAM;IACN,cAAc;IACd,WAAW;IACX,OAAO;IACP,QAAQ;EACV,IAAII;IACF,CAAC,mBAAmB,CAAC,CAAC,WAAW,UAAU,gBAAgB;IAC3D,CAAA,mBAAkB;AAEhB,YAAM,gBAAgB,iBAAiB,gBAAgB,SAAS;AAEhE,aAAO,WAAA,OAAA,SAAA,QAAU,aAAA;IACnB;IACA,EAAE,iBAAiB;EACrB;AAEA,QAAM;IACJ,MAAM;IACN,WAAW;IACX,cAAc;IACd,OAAO;IACP;IACA;IACA,QAAQ;EACV,IAAIA;IACF,CAAA,cAAa;AACX,UAAI,CAAC,mBAAmB,CAAC,SAAS;AAChC,eAAO;MACT;AAEA,aAAO;QACL,GAAG;QACH,GAAG;QACH,aAAa,eAAe,UAAU;QACtC,UAAU,YAAY;MACxB;IACF;IACA,CAAA,mBAAkB;AAEhB,YAAM,gBAAgB,iBAAiB,gBAAgB,SAAS;AAEhE,aAAO,WAAA,OAAA,SAAA,QAAU,aAAA;IACnB;EACF;AAEA,QAAM,WAAO,uBAAQ,MAAM;AACzB,QAAI,iBAAiB;AACnB,aAAO;IACT;AACA,WAAO;EACT,GAAG,CAAC,iBAAiB,MAAM,aAAa,CAAC;AAEzC,QAAM,gBAAmC;IACvC,CAAA,gBAAe;AACb,UAAI,iBAAiB;AACnB,aAAK,QAAQ,WAAW;AACxB;MACF;AACA,aAAO,iBAAiB,WAAW;IACrC;IACA,CAAC,OAAO;EACV;AAEA,QAAM,WAAO,uBAAQ,MAAM;AA9K7B,QAAAC,KAAAC;AA+KI,QAAI,iBAAiB;AACnB,cAAOD,MAAA,mBAAA,OAAA,SAAA,gBAAiB,IAAI,CAAA,MAAK,KAAA,OAAA,SAAA,EAAG,IAAA,EAAM,KAAA,MAAnC,OAAAA,MAA6C,CAAC;IACvD;AACA,YAAOC,MAAA,WAAA,OAAA,SAAA,QAAS,SAAT,OAAAA,MAAiB,CAAC;EAC3B,GAAG,CAAC,iBAAiB,SAAS,eAAe,CAAC;AAE9C,QAAM,YAAQ,uBAAQ,MAAM;AArL9B,QAAAD,KAAAC;AAsLI,QAAI,iBAAiB;AACnB,eAAOD,MAAA,mBAAA,OAAA,SAAA,iBAAkB,mBAAA,OAAA,SAAA,gBAAiB,UAAS,CAAA,MAA5C,OAAA,SAAAA,IAAgD,gBAAe;IACxE;AACA,YAAOC,MAAA,WAAA,OAAA,SAAA,QAAS,gBAAT,OAAAA,MAAwB;EACjC,GAAG,CAAC,iBAAiB,SAAS,eAAe,CAAC;AAE9C,QAAM,YAAY,kBAAkB,uBAAuB;AAC3D,QAAM,aAAa,kBAAkB,0BAA0B;AAC/D,QAAM,UAAU,CAAC,EAAE,kBAAkB,mBAAmB;AAIxD,QAAM,gBAAY,2BAAY,MAAM;AAClC,cAAU,CAAA,MAAK,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;EACnC,GAAG,CAAC,SAAS,CAAC;AAEd,QAAM,oBAAgB,2BAAY,MAAM;AACtC,cAAU,CAAA,MAAK,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;EACnC,GAAG,CAAC,SAAS,CAAC;AAEd,QAAM,eAAe,eAAe,UAAU,KAAK,YAAY;AAE/D,QAAM,YAAY,KAAK,MAAM,QAAQ,eAAe,YAAY,OAAO;AACvE,QAAM,cAAc,QAAQ,cAAc,YAAY,UAAU,OAAO,YAAY;AACnF,QAAM,mBAAmB,OAAO,KAAK,YAAY,UAAU,cAAc,YAAY;AAErF,QAAM,UAAuB,kBACzB,CAAA,UACE,kBAAkB,OAAO;IACvB,YAAY;EACd,CAAC,IACH,CAAA,UACE,UAAU,OAAO;IACf,YAAY;EACd,CAAC;AAEP,QAAM,aAAa,kBAAkB,MAAM,kBAAkB,IAAI,MAAM,UAAU;AAEjF,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;;IAEA;EACF;AACF;ACjHA,IAAM,6BAA6B;EACjC,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,SAAS;EACT,MAAM;EACN,WAAW;EACX,WAAW;EACX,WAAW;EACX,eAAe;EACf,aAAa;EACb,iBAAiB;EACjB,YAAY;EACZ,SAAS;AACX;AAEO,IAAM,kBAAmC,CAAA,WAAU;AACxD,QAAM;IACJ,gBAAgB;IAChB,gBAAgB;IAChB,SAAS;IACT,oBAAoB;IACpB,aAAa;IACb,aAAa;EACf,IAAI,UAAU,CAAC;AACf,QAAM,EAAE,cAAc,wBAAwB,2BAA2B,IAAI,uBAAuB;AACpG,QAAM,UAAU,kBAAkB;AAElC,QAAM,mBAAmB,kBAAkB,kBAAkB;IAC3D,aAAa;IACb,UAAU;IACV,kBAAkB;IAClB,UAAU;IACV,gBAAgB;EAClB,CAAC;AAED,QAAM,8BAA8B,kBAAkB,8BAA8B;IAClF,aAAa;IACb,UAAU;IACV,QAAQ;IACR,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,oBAAoB,kBAAkB,mBAAmB;IAC7D,aAAa;IACb,UAAU;IACV,MAAM;IACN,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,wBAAwB,kBAAkB,uBAAuB;IACrE,aAAa;IACb,UAAU;IACV,QAAQ,CAAC,SAAS;IAClB,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,QAAQ,wBAAwB;AAEtC,QAAM,cAAc,CAAC,EAAE,MAAM,UAAU,WAAW;AAElD,QAAM,eACJ,OAAO,qBAAqB,cACxB,SACA;IACE,aAAa,iBAAiB;IAC9B,UAAU,iBAAiB;IAC3B,gBAAgB,iBAAiB;EACnC;AAEN,QAAM,0BACJ,OAAO,iCAAiC,cACpC,SACA;IACE,aAAa,4BAA4B;IACzC,UAAU,4BAA4B;IACtC,QAAQ,4BAA4B;EACtC;AAEN,QAAM,gBACJ,OAAO,sBAAsB,cACzB,SACA;IACE,aAAa,kBAAkB;IAC/B,UAAU,kBAAkB;IAC5B,MAAM,kBAAkB;EAC1B;AAEN,QAAM,oBACJ,OAAO,0BAA0B,cAC7B,SACA;IACE,aAAa,sBAAsB;IACnC,UAAU,sBAAsB;IAChC,QAAQ,sBAAsB;EAChC;AAEN,QAAM,UAAU;IACd;MACE,GAAG;IACL;IACA,gBAAA,OAAA,SAAA,aAAc;IACd;MACE,kBAAkB,iBAAiB;MACnC,UAAU,iBAAiB;MAC3B,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,gBAAgB,gBAAA,OAAA,SAAA,aAAc;IAChC;EACF;AAEA,QAAM,qBAAqB;IAIzB;MACE,GAAG;IACL;IACA,gBAAA,OAAA,SAAA,aAAc;IACd;MACE,kBAAkB,4BAA4B;MAC9C,UAAU,4BAA4B;MACtC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,gBAAgB,gBAAA,OAAA,SAAA,aAAc;IAChC;EACF;AAEA,QAAM,cAAc;IAClB;MACE,GAAG;MACH,WAAW;IACb;IACA,gBAAA,OAAA,SAAA,aAAc;IACd;MACE,kBAAkB,kBAAkB;MACpC,UAAU,kBAAkB;MAC5B,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,gBAAgB,gBAAA,OAAA,SAAA,aAAc;IAChC;EACF;AAEA,QAAM,cAAc;IAClB;MACE,GAAG;IACL;IACA,gBAAA,OAAA,SAAA,aAAc;IACd;MACE,kBAAkB,sBAAsB;MACxC,UAAU,sBAAsB;MAChC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,gBAAgB,gBAAA,OAAA,SAAA,aAAc;IAChC;EACF;AAIA,QAAM,qBAAqB,CAAC,MAAM,SAC9B,MAAM,CAAC,IACP,MAAG;AA1ST,QAAA;AA0SY,YAAA,KAAA,MAAM,iBAAN,OAAA,SAAA,GAAoB,sBAAsB,oBAAA;EAAA;AAEpD,QAAM,iCAAiC,CAAC,MAAM,SAC1C,MAAM,CAAC,IACP,MAAG;AA9ST,QAAA;AA8SY,YAAA,KAAA,MAAM,iBAAN,OAAA,SAAA,GAAoB,eAAe,oBAAA;EAAA;AAE7C,MAAI,sBAAsB;AACxB,eAAW,qCAAqC,0DAA0D;EAC5G;AAEA,QAAM;IACJ,MAAM;IACN,cAAc;IACd,QAAQ;EACV,IAAIF;IACF,eAAe,uBACX,SAAS,WAAW,cAAc,4BAA4B,oBAAoB,IAClF;IACJ;EACF;AAEA,MAAI,sBAAsB;AACxB,eAAW,qCAAqC,0DAA0D;EAC5G;AAEA,QAAM;IACJ,MAAM;IACN,cAAc;IACd,QAAQ;EACV,IAAIA;IACF,eAAe,uBACX,SAAS,eAAe,cAAc,wBAAwB,oBAAoB,IAClF;IACJ;EACF;AAEA,MAAI,iBAAiB,QAAW;AAC9B,WAAO;MACL,UAAU;MACV,cAAc;MACd,gBAAgB;MAChB,gBAAgB;MAChB,YAAY;MACZ,SAAS;MACT,oBAAoB;MACpB,aAAa;MACb,aAAa;IACf;EACF;AAEA,MAAI,iBAAiB,MAAM;AACzB,WAAO;MACL,UAAU;MACV,cAAc;MACd,gBAAgB;MAChB,gBAAgB;MAChB,YAAY;MACZ,SAAS;MACT,oBAAoB;MACpB,aAAa;MACb,aAAa;IACf;EACF;AAGA,MAAI,CAAC,MAAM,UAAU,cAAc;AACjC,WAAO;MACL,UAAU;MACV;MACA,gBAAgB;MAChB,gBAAgB;MAChB,YAAY;MACZ,SAAS;MACT,oBAAoB;MACpB,aAAa;MACb,aAAa;IACf;EACF;AAEA,SAAO;IACL,UAAU,CAAC,wBAAwB,CAAC;IACpC;IACA;IACA,YAAY,iCAAiC,QAAS,KAAK,yBAAyB,aAAa,EAAE;;IACnG;IACA,kBAAkB,MAAM;AACtB,WAAK,qBAAqB;AAC1B,WAAK,qBAAqB;IAC5B;IACA;IACA;IACA;IACA;EACF;AACF;AAEA,SAAS,iCACP,yBACA,sBACA;AACA,SAAO,wBAAwB;IAC7B,CAAA,2BAA0B,uBAAuB,aAAa,OAAO;EACvE;AACF;AAEA,SAAS,SACP,MACA,cACA,UACA,YACA;AACA,SAAO,CAAC,MAAM,aAAa,IAAI,YAAA,OAAA,SAAA,SAAU,IAAI,YAAA,OAAA,SAAA,SAAU,WAAW,WAAW,QAAQ,WAAW,KAAK,EAClG,OAAO,OAAO,EACd,KAAK,GAAG;AACb;ACpXA,IAAMG,8BAA6B;EACjC,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,SAAS;EACT,MAAM;EACN,WAAW;EACX,WAAW;EACX,WAAW;EACX,eAAe;EACf,aAAa;EACb,iBAAiB;EACjB,YAAY;EACZ,SAAS;AACX;AAuCO,IAAM,sBAA2C,CAAA,WAAU;AAChE,QAAM,EAAE,iBAAiB,iBAAiB,gBAAgB,IAAI,UAAU,CAAC;AAEzE,QAAM,4BAA4B,kBAAkB,iBAAiB;IACnE,aAAa;IACb,UAAU;IACV,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,4BAA4B,kBAAkB,iBAAiB;IACnE,aAAa;IACb,UAAU;IACV,QAAQ;IACR,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,4BAA4B,kBAAkB,iBAAiB;IACnE,aAAa;IACb,UAAU;IACV,QAAQ;IACR,kBAAkB;IAClB,UAAU;EACZ,CAAC;AAED,QAAM,QAAQ,wBAAwB;AACtC,QAAM,OAAO,eAAe;AAE5B,QAAM,wBACJ,OAAO,oBAAoB,cACvB,SACA;IACE,aAAa,0BAA0B;IACvC,UAAU,0BAA0B;EACtC;AAEN,QAAM,wBACJ,OAAO,oBAAoB,cACvB,SACA;IACE,aAAa,0BAA0B;IACvC,UAAU,0BAA0B;IACpC,QAAQ,0BAA0B;EACpC;AAEN,QAAM,wBACJ,OAAO,oBAAoB,cACvB,SACA;IACE,aAAa,0BAA0B;IACvC,UAAU,0BAA0B;IACpC,QAAQ,0BAA0B;EACpC;AAEN,QAAM,gBAAgB,CAAC,EAAE,MAAM,UAAU;AAEzC,QAAM,cAAc;IAIlB;MACE,GAAG;MACH,WAAW;IACb;IACA,QAAA,OAAA,SAAA,KAAM;IACN;MACE,kBAAkB,0BAA0B;MAC5C,UAAU,0BAA0B;MACpC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,QAAQ,QAAA,OAAA,SAAA,KAAM;IAChB;EACF;AAEA,QAAM,cAAc;IAIlB;MACE,GAAG;IACL;IACA,QAAA,OAAA,SAAA,KAAM;IACN;MACE,kBAAkB,0BAA0B;MAC5C,UAAU,0BAA0B;MACpC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,QAAQ,QAAA,OAAA,SAAA,KAAM;IAChB;EACF;AAEA,QAAM,cAAc;IAIlB;MACE,GAAG;IACL;IACA,QAAA,OAAA,SAAA,KAAM;IACN;MACE,kBAAkB,0BAA0B;MAC5C,UAAU,0BAA0B;MACpC,SAAS,CAAC,CAAC;IACb;IACA;MACE,MAAM;MACN,QAAQ,QAAA,OAAA,SAAA,KAAM;IAChB;EACF;AAGA,MAAI,CAAC,eAAe;AAClB,WAAO;MACL,UAAU;MACV,kBAAkB;MAClB,oBAAoB;MACpB,WAAW;MACX,iBAAiBA;MACjB,iBAAiBA;MACjB,iBAAiBA;IACnB;EACF;AAEA,QAAM,SAAS;IACb,UAAU;IACV,kBAAkB,uBAAuB,KAAK,uBAAuB;IACrE,WAAW,MAAM;IACjB,oBAAoB,MAAM;IAC1B,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;EACnB;AACA,2BAAyB,QAAQ,oBAAoB,gCAAgC;AAErF,SAAO;AACT;AAEA,SAAS,uBAAuB,yBAA2D;AACzF,SAAO,wBAAwB,IAAI,CAAA,4BAA2B;IAC5D,YAAY;IACZ,cAAc,uBAAuB;EACvC,EAAE;AACJ;AC5LO,IAAM,mBAAqC,MAAM;AACtD,aAAW,oBAAoB,gEAAgE;AAC/F,QAAM,QAAQ,wBAAwB;AACtC,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO;MACL,UAAU;MACV,oBAAoB;MACpB,4BAA4B;MAC5B,iBAAiB;IACnB;EACF;AAEA,SAAO;IACL,UAAU;IACV,oBAAoB,MAAM;IAC1B,4BAA4B,MAAM;IAClC,iBAAiB,MAAM;EACzB;AACF;ACrEO,IAAM,sBAAsB,OAAO,WAAW,cAAcC,eAAAA,QAAM,kBAAkBA,eAAAA,QAAM;;;AIA1F,IAAM,CAAC,wBAAwB,yBAAyB,IAAI,CAAC,sBAAsB,uBAAuB;;;ACFjH,IAAAC,iBAAkB;;;ACEX,SAAS,6BAA6B,YAAyC;AACpF,MAAI,CAAC,YAAY;AACf,UAAM,IAAI,MAAM,oBAAoB;EACtC;AACF;;;ADEO,IAAM,yBAAyB,OAAO,OAAO;EAClD,cAAc,OAAO,OAAO;IAC1B,kBAAkB;EACpB,CAAC;EACD,kBAAkB,OAAO,OAAO;IAC9B,kBAAkB;EACpB,CAAC;AACH,CAAC;AAEM,IAAM,mBAAmB,eAAAC,QAAM,cAAiD,MAAS;AAEhG,iBAAiB,cAAc;AAE/B,IAAM,sBAAsB,MAA6B;AACvD,QAAM,eAAe,eAAAA,QAAM,WAAW,gBAAgB;AACtD,+BAA6B,YAAY;AACzC,SAAO;AACT;AAEO,IAAM,kBAA8D,CAAC,EAAE,SAAS,MAAM;AAC3F,QAAM,YAAY,oBAAoB;AACtC,MAAI,UAAU,kBAAkB;AAC9B,WAAO,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;EACrB;AACA,SACE,eAAAA,QAAA,cAAC,iBAAiB,UAAjB,EAA0B,OAAO,uBAAuB,iBAAA,GAAmB,QAAS;AAEzF;;;Ab5BO,IAAM,YAAY,CACvB,WACA,gBACG;AACH,gBAAc,eAAe,UAAU,eAAe,UAAU,QAAQ;AACxE,YAAU,cAAc;AACxB,QAAM,MAAM,CAAC,UAA4B;AACvC,UAAM,QAAQ,0BAA0B;AAExC,QAAI,CAAC,MAAM,QAAQ;AACjB,aAAO;IACT;AAEA,WACE,eAAAC,QAAA,cAAC,iBAAA,MACC,eAAAA,QAAA;MAAC;MAAA;QACE,GAAI;QACL;MAAA;IACF,CACF;EAEJ;AACA,MAAI,cAAc,aAAa,WAAW;AAC1C,SAAO;AACT;AAEO,IAAM,YAER,CAAC,EAAE,SAAS,MAAM;AACrB,QAAM,QAAQ,0BAA0B;AAExC,MAAI,OAAO,aAAa,YAAY;AAClC,UAAM,IAAI,MAAM,4BAA4B;EAC9C;AAEA,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO;EACT;AAEA,SAAO,eAAAA,QAAA,cAAC,iBAAA,MAAiB,SAAS,KAA+B,CAAE;AACrE;;;AD2CA,IAAM,eAAe,CAAC,UAAoC;AACxD,SAAO,WAAW;AACpB;AAEA,IAAM,cAAc,CAAC,UAAmC;AACtD,SAAO,UAAU;AACnB;AAEA,IAAM,SAAN,cAAqB,eAAAC,QAAM,cAAsC;EAAjE,cAAA;AAAA,UAAA,GAAA,SAAA;AACE,SAAQ,YAAY,eAAAA,QAAM,UAA0B;EAAA;EAEpD,mBAAmB,WAA6C;AArGlE,QAAA,IAAA,IAAA,IAAA;AAsGI,QAAI,CAAC,aAAa,SAAS,KAAK,CAAC,aAAa,KAAK,KAAK,GAAG;AACzD;IACF;AAEA,QACE,UAAU,MAAM,eAAe,KAAK,MAAM,MAAM,gBAChD,MAAA,KAAA,UAAU,UAAV,OAAA,SAAA,GAAiB,gBAAjB,OAAA,SAAA,GAA8B,cAAW,MAAA,KAAA,KAAK,MAAM,UAAX,OAAA,SAAA,GAAkB,gBAAlB,OAAA,SAAA,GAA+B,SACxE;AACA,WAAK,MAAM,YAAY,EAAE,MAAM,KAAK,UAAU,SAAS,OAAO,KAAK,MAAM,MAAM,CAAC;IAClF;EACF;EAEA,oBAAoB;AAClB,QAAI,KAAK,UAAU,SAAS;AAC1B,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,aAAK,MAAM,MAAM,KAAK,UAAU,SAAS,KAAK,MAAM,KAAK;MAC3D;AAEA,UAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,aAAK,MAAM,KAAK,KAAK,MAAM,KAAK;MAClC;IACF;EACF;EAEA,uBAAuB;AACrB,QAAI,KAAK,UAAU,SAAS;AAC1B,UAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,aAAK,MAAM,QAAQ,KAAK,UAAU,OAAO;MAC3C;AACA,UAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,aAAK,MAAM,MAAM;MACnB;IACF;EACF;EAEA,SAAS;AAzIX,QAAA,IAAA;AA0II,WACE,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MACE,eAAAA,QAAA,cAAC,OAAA,EAAI,KAAK,KAAK,UAAA,CAAW,GACzB,aAAa,KAAK,KAAK,OACtB,MAAA,KAAA,KAAK,UAAL,OAAA,SAAA,GAAY,uBAAZ,OAAA,SAAA,GAAgC,IAAI,CAAC,QAAQ,cAAU,8BAAc,QAAQ,EAAE,KAAK,MAAM,CAAC,CAAA,EAC/F;EAEJ;AACF;AAEO,IAAM,SAAS,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAAkC;AACnF,SACE,eAAAA,QAAA;IAAC;IAAA;MACC,OAAO,MAAM;MACb,SAAS,MAAM;MACf,aAAc,MAAc;MAC5B;IAAA;EACF;AAEJ,GAAG,QAAQ;AAEJ,IAAM,SAAS,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAAkC;AACnF,SACE,eAAAA,QAAA;IAAC;IAAA;MACC,OAAO,MAAM;MACb,SAAS,MAAM;MACf,aAAc,MAAc;MAC5B;IAAA;EACF;AAEJ,GAAG,QAAQ;AAEJ,SAAS,gBAAgB,EAAE,SAAS,GAA4C;AACrF,oBAAkB,4BAA4B;AAC9C,SAAO,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEO,SAAS,gBAAgB,EAAE,SAAS,GAA4C;AACrF,oBAAkB,4BAA4B;AAC9C,SAAO,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEA,IAAM,eAAe;EACnB,CAAC,EAAE,OAAO,GAAG,MAAM,MAA+E;AAChG,UAAM,EAAE,aAAa,mBAAmB,IAAI,0BAA0B,MAAM,QAAQ;AACpF,WACE,eAAAA,QAAA;MAAC;MAAA;QACC,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B,OAAO,EAAE,GAAG,OAAO,YAAY;QAC/B;MAAA;IACF;EAEJ;EACA;AACF;AAEO,IAAM,cAAqC,OAAO,OAAO,cAAc;EAC5E,MAAM;EACN,MAAM;AACR,CAAC;AAED,IAAM,cAAc;EAClB,CAAC,EAAE,OAAO,GAAG,MAAM,MAA2E;AAC5F,UAAM,EAAE,aAAa,mBAAmB,IAAI,0BAA0B,MAAM,QAAQ;AACpF,UAAM,mBAAmB,OAAO,OAAO,MAAM,oBAAoB,CAAC,GAAG,EAAE,YAAY,CAAC;AACpF,WACE,eAAAA,QAAA;MAAC;MAAA;QACC,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B,OAAO,EAAE,GAAG,OAAO,iBAAiB;QACpC;MAAA;IACF;EAEJ;EACA;AACF;AAEO,IAAM,aAAmC,OAAO,OAAO,aAAa;EACzE;EACA;AACF,CAAC;AAEM,SAAS,wBAAwB,EAAE,SAAS,GAAoD;AACrG,oBAAkB,oCAAoC;AACtD,SAAO,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEO,SAAS,wBAAwB,EAAE,SAAS,GAAoD;AACrG,oBAAkB,oCAAoC;AACtD,SAAO,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAEA,IAAM,uBAAuB;EAC3B,CAAC,EAAE,OAAO,GAAG,MAAM,MAAuF;AACxG,UAAM,EAAE,aAAa,mBAAmB,IAAI,kCAAkC,MAAM,QAAQ;AAC5F,WACE,eAAAA,QAAA;MAAC;MAAA;QACC,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B,OAAO,EAAE,GAAG,OAAO,YAAY;QAC/B;MAAA;IACF;EAEJ;EACA;AACF;AAEO,IAAM,sBAAqD,OAAO,OAAO,sBAAsB;EACpG,MAAM;EACN,MAAM;AACR,CAAC;AAEM,IAAM,qBAAqB,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA8C;AAC3G,SACE,eAAAA,QAAA;IAAC;IAAA;MACC,OAAO,MAAM;MACb,SAAS,MAAM;MACf,aAAc,MAAc;MAC5B;IAAA;EACF;AAEJ,GAAG,oBAAoB;AAEvB,IAAM,wBAAwB;EAC5B,CAAC,EAAE,OAAO,GAAG,MAAM,MAAqF;AACtG,UAAM,EAAE,aAAa,mBAAmB,IAAI,kCAAkC,MAAM,QAAQ;AAC5F,UAAM,2BAA2B,OAAO,OAAO,MAAM,4BAA4B,CAAC,GAAG,EAAE,YAAY,CAAC;AACpG,WACE,eAAAA,QAAA;MAAC;MAAA;QACC,OAAO,MAAM;QACb,SAAS,MAAM;QACf,aAAc,MAAc;QAC5B,OAAO,EAAE,GAAG,OAAO,yBAAyB;QAC5C;MAAA;IACF;EAEJ;EACA;AACF;AAEO,IAAM,uBAAuD,OAAO,OAAO,uBAAuB;EACvG;EACA;AACF,CAAC;AAEM,IAAM,mBAAmB,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA4C;AACvG,SACE,eAAAA,QAAA;IAAC;IAAA;MACC,OAAO,MAAM;MACb,SAAS,MAAM;MACf,aAAc,MAAc;MAC5B;IAAA;EACF;AAEJ,GAAG,kBAAkB;AAEd,IAAM,eAAe,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAAwC;AAC/F,SACE,eAAAA,QAAA;IAAC;IAAA;MACC,MAAM,MAAM;MACZ,OAAO,MAAM;MACb;IAAA;EACF;AAEJ,GAAG,cAAc;;;ADlSjB,IAAM,kBAAkB,CAAC,GAAQ,cAAqD;AACpF,SAAO,CAAC,CAAC,KAAK,eAAAC,QAAM,eAAe,CAAC,MAAM,KAAA,OAAA,SAAA,EAA0B,UAAS;AAC/E;AAEO,IAAM,4BAA4B,CAAC,aAAkD;AAC1F,QAAM,qBAAqB,CAAC,WAAW,UAAU;AACjD,SAAO,eAAe;IACpB;IACA;IACA,eAAe;IACf,eAAe;IACf,eAAe;EACjB,CAAC;AACH;AAEO,IAAM,oCAAoC,CAAC,aAAkD;AAClG,QAAM,qBAAqB,CAAC,WAAW,UAAU;AACjD,SAAO,eAAe;IACpB;IACA;IACA,eAAe;IACf,eAAe;IACf,eAAe;EACjB,CAAC;AACH;AAYA,IAAM,iBAAiB,CAAC;EACtB;EACA;EACA;EACA;EACA;AACF,MAA4B;AAC1B,QAAM,gBAAwC,CAAC;AAE/C,iBAAAA,QAAM,SAAS,QAAQ,UAAU,CAAA,UAAS;AACxC,QAAI,CAAC,gBAAgB,OAAO,aAAa,KAAK,CAAC,gBAAgB,OAAO,aAAa,GAAG;AACpF,UAAI,OAAO;AACT,0BAAkB,4BAA4B,aAAa,CAAC;MAC9D;AACA;IACF;AAEA,UAAM,EAAE,MAAM,IAAI;AAElB,UAAM,EAAE,UAAAC,WAAU,OAAO,KAAK,UAAU,IAAI;AAE5C,QAAI,gBAAgB,OAAO,aAAa,GAAG;AACzC,UAAI,cAAc,OAAO,kBAAkB,GAAG;AAE5C,sBAAc,KAAK,EAAE,MAAM,CAAC;MAC9B,WAAW,aAAa,KAAK,GAAG;AAE9B,sBAAc,KAAK,EAAE,OAAO,WAAW,UAAAA,WAAU,IAAI,CAAC;MACxD,OAAO;AACL,0BAAkB,qBAAqB,aAAa,CAAC;AACrD;MACF;IACF;AAEA,QAAI,gBAAgB,OAAO,aAAa,GAAG;AACzC,UAAI,eAAe,KAAK,GAAG;AAEzB,sBAAc,KAAK,EAAE,OAAO,WAAW,IAAI,CAAC;MAC9C,OAAO;AACL,0BAAkB,qBAAqB,aAAa,CAAC;AACrD;MACF;IACF;EACF,CAAC;AAED,QAAM,qBAAqD,CAAC;AAC5D,QAAM,uBAAuD,CAAC;AAC9D,QAAM,uBAAuD,CAAC;AAE9D,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,aAAa,EAAE,GAAG;AACpB,yBAAmB,KAAK,EAAE,WAAW,GAAG,UAAU,IAAI,MAAM,CAAC;AAC7D,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;AAChE;IACF;AACA,QAAI,eAAe,EAAE,GAAG;AACtB,2BAAqB,KAAK,EAAE,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC;IAClE;EACF,CAAC;AAED,QAAM,4BAA4B,uBAAuB,kBAAkB;AAC3E,QAAM,8BAA8B,uBAAuB,oBAAoB;AAC/E,QAAM,8BAA8B,uBAAuB,oBAAoB;AAE/E,QAAM,cAA4B,CAAC;AACnC,QAAM,qBAA4C,CAAC;AAEnD,gBAAc,QAAQ,CAAC,IAAI,UAAU;AACnC,QAAI,cAAc,IAAI,kBAAkB,GAAG;AACzC,kBAAY,KAAK,EAAE,OAAO,GAAG,MAAM,CAAC;AACpC;IACF;AACA,QAAI,aAAa,EAAE,GAAG;AACpB,YAAM;QACJ,QAAQ;QACR;QACA;MACF,IAAI,0BAA0B,KAAK,CAAA,MAAK,EAAE,OAAO,KAAK;AACtD,YAAM;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;MACX,IAAI,4BAA4B,KAAK,CAAA,MAAK,EAAE,OAAO,KAAK;AACxD,kBAAY,KAAK,EAAE,OAAO,GAAG,OAAO,KAAK,GAAG,KAAK,OAAO,SAAS,WAAW,YAAY,CAAC;AACzF,yBAAmB,KAAK,aAAa;AACrC,yBAAmB,KAAK,WAAW;AACnC;IACF;AACA,QAAI,eAAe,EAAE,GAAG;AACtB,YAAM;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;MACX,IAAI,4BAA4B,KAAK,CAAA,MAAK,EAAE,OAAO,KAAK;AACxD,kBAAY,KAAK,EAAE,OAAO,GAAG,OAAO,KAAK,GAAG,KAAK,WAAW,YAAY,CAAC;AACzE,yBAAmB,KAAK,WAAW;AACnC;IACF;EACF,CAAC;AAED,SAAO,EAAE,aAAa,mBAAmB;AAC3C;AAEA,IAAM,gBAAgB,CAAC,YAAiB,eAAkC;AACxE,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,WAAW,KAAK,CAAA,MAAK,MAAM,KAAK;AAC5E;AAEA,IAAM,eAAe,CAAC,eAA6B;AACjD,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACjD;AAEA,IAAM,iBAAiB,CAAC,eAA6B;AACnD,QAAM,EAAE,UAAU,OAAO,KAAK,UAAU,IAAI;AAC5C,SAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AAChD;;;AiBtKA,IAAAC,iBAAkB;;;ACFlB,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAsJA,IAAqB,mBAArB,MAAqBC,kBAAiD;EA6EpE,YAAY,SAAiC;AAkH7C,iBAAA,MAAA,eAAA;AA3LA,SAAQ,UAAsD;AAC9D,SAAQ,gBAA2C;AACnD,SAAQ,gBAAqC;AAC7C,SAAQ,gBAAqC;AAC7C,SAAQ,qBAA+C;AACvD,SAAQ,6BAA+D;AACvE,SAAQ,4BAA6D;AACrE,SAAQ,sBAAsB,oBAAI,IAAiC;AACnE,SAAQ,sBAAsB,oBAAI,IAAiC;AACnE,SAAQ,2BAA2B,oBAAI,IAAsC;AAC7E,SAAQ,0BAA0B,oBAAI,IAAqC;AAC3E,SAAQ,mCAAmC,oBAAI,IAA8C;AAC7F,SAAQ,kCAAkC,oBAAI,IAA6C;AAC3F,SAAQ,oCAAoC,oBAAI,IAA+C;AAC/F,SAAQ,gCAAgC,oBAAI,IAA2C;AACvF,SAAQ,sBAAsB,oBAAI,IAA8C;AAChF,SAAQ,kBAAqC,CAAC;AAE9C,iBAAA,MAAA,SAAU,KAAA;AACV,iBAAA,MAAA,SAAA,MAAA;AACA,iBAAA,MAAA,WAAA,MAAA;AACA,iBAAA,MAAA,cAAA,MAAA;AACA,iBAAA,MAAA,iBAAA,MAAA;AA2FA,SAAA,UAAU,MAAY;AA3QxB,UAAA;AA2Q2B,aAAA,SAAQ,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,QAAA,CAAS;IAAA;AAExD,SAAA,iBAAiB,CAAC,SAA0C;AAC1D,YAAM,WAAW,MAAG;AA9QxB,YAAA;AA8Q2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,eAAe,IAAA,MAAS;MAAA;AAC7D,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;MACzD;IACF;AAEA,SAAA,iBAAiB,CAAC,SAA0C;AAC1D,YAAM,WAAW,MAAG;AAvRxB,YAAA;AAuR2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,eAAe,IAAA,MAAS;MAAA;AAC7D,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;MACzD;IACF;AAEA,SAAA,sBAAsB,MAAqB;AACzC,YAAM,WAAW,MAAG;AAhSxB,YAAA;AAgS2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,oBAAA,MAAyB;MAAA;AAC9D,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,uBAAuB,QAAQ;MAC9D;IACF;AAEA,SAAA,6BAA6B,MAAqB;AAChD,YAAM,WAAW,MAAG;AAzSxB,YAAA;AAyS2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,2BAAA,MAAgC;MAAA;AACrE,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;MACrE;IACF;AAEA,SAAA,8BAA8B,MAAqB;AACjD,YAAM,WAAW,MAAG;AAlTxB,YAAA;AAkT2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,4BAAA,MAAiC;MAAA;AACtE,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;MACtE;IACF;AAEA,SAAA,eAAe,MAAqB;AAClC,YAAM,WAAW,MAAG;AA3TxB,YAAA;AA2T2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,aAAA,MAAkB;MAAA;AACvD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,gBAAgB,QAAQ;MACvD;IACF;AAEA,SAAA,mBAAmB,CAAC,OAA8B;AAChD,YAAM,WAAW,MAAG;AApUxB,YAAA;AAoU2B,iBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,iBAAiB,EAAA,MAAO;MAAA;AAC7D,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;MAC3D;IACF;AAEA,SAAA,wBAAwB,MAAY;AAClC,YAAM,WAAW,MAAG;AA7UxB,YAAA;AA6U2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,sBAAA;MAAA;AACrC,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,SAAS;MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;MAChE;IACF;AA8FA,SAAO,cAAc,CAAC,OAAmB;AACvC,WAAK,gBAAgB,KAAK,EAAE;AAI5B,UAAI,KAAK,QAAQ;AACf,aAAK,WAAW;MAClB;IACF;AAEA,SAAO,aAAa,MAAM;AACxB,WAAK,gBAAgB,QAAQ,CAAA,OAAM,GAAG,CAAC;AACvC,WAAK,kBAAkB,CAAC;IAC1B;AAEA,SAAQ,iBAAiB,CAAC,YAA6D;AACrF,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,mCAAmC;MACrD;AAEA,WAAK,UAAU;AAEf,WAAK,oBAAoB,QAAQ,CAAA,OAAM,GAAG,CAAC;AAE3C,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,WAAW,KAAK,aAAa;MACvC;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,WAAW,KAAK,aAAa;MACvC;AAEA,UAAI,KAAK,uBAAuB,MAAM;AACpC,gBAAQ,gBAAgB,KAAK,kBAAkB;MACjD;AAEA,UAAI,KAAK,kBAAkB,MAAM;AAC/B,gBAAQ,iBAAiB,KAAK,aAAa;MAC7C;AAEA,UAAI,KAAK,+BAA+B,MAAM;AAC5C,gBAAQ,wBAAwB,KAAK,0BAA0B;MACjE;AAEA,UAAI,KAAK,8BAA8B,MAAM;AAC3C,gBAAQ,uBAAuB,KAAK,yBAAyB;MAC/D;AAEA,WAAK,oBAAoB,QAAQ,CAAC,OAAoB,SAAyB;AAC7E,gBAAQ,YAAY,MAAM,KAAK;MACjC,CAAC;AAED,WAAK,oBAAoB,QAAQ,CAAC,OAAoB,SAAyB;AAC7E,gBAAQ,YAAY,MAAM,KAAK;MACjC,CAAC;AAED,WAAK,yBAAyB,QAAQ,CAAC,OAAyB,SAAyB;AACvF,gBAAQ,iBAAiB,MAAM,KAAK;MACtC,CAAC;AAED,WAAK,wBAAwB,QAAQ,CAAC,OAAwB,SAAyB;AACrF,gBAAQ,gBAAgB,MAAM,KAAK;MACrC,CAAC;AAED,WAAK,8BAA8B,QAAQ,CAAC,OAA8B,SAAyB;AACjG,gBAAQ,sBAAsB,MAAM,KAAK;MAC3C,CAAC;AAED,mBAAA,MAAK,SAAU,IAAA;AACf,WAAK,WAAW;AAChB,aAAO,KAAK;IACd;AAwDA,SAAA,0BAA0B,CAAC,UAAoB;AAE7C,UAAI,KAAK,WAAW,6BAA6B,KAAK,SAAS;AAC5D,aAAK,QAAgB,wBAAwB,KAAK;MACrD,OAAO;AACL,eAAO;MACT;IACF;AAKA,SAAA,YAAY,CAAC,EAAE,SAAS,cAAc,WAAW,MAAsC;AACrF,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,QAAQ,UAAU,EAAE,SAAS,cAAc,WAAW,CAAC;MACrE,OAAO;AACL,eAAO,QAAQ,OAAO;MACxB;IACF;AAEA,SAAA,aAAa,CAAC,SAAgD,eAAmD;AAC/G,iBAAW,cAAc,+BAA+B;AACxD,aAAO,KAAK,UAAU,EAAE,SAAS,WAAW,CAAC;IAC/C;AAEA,SAAA,aAAa,CAAC,UAA8B;AAC1C,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,WAAW,KAAK;MAC/B,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,cAAc,MAAY;AACxB,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,YAAY;MAC3B,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,mBAAmB,CAAC,UAAoC;AACtD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,iBAAiB,KAAK;MACrC,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,oBAAoB,MAAY;AAC9B,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,kBAAkB;MACjC,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,kBAAkB,CAAC,UAAmC;AACpD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,gBAAgB,KAAK;MACpC,OAAO;AACL,aAAK,qBAAqB;MAC5B;IACF;AAEA,SAAA,mBAAmB,MAAY;AAC7B,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,iBAAiB;MAChC,OAAO;AACL,aAAK,qBAAqB;MAC5B;IACF;AAEA,SAAA,0BAA0B,CAAC,UAA2C;AACpE,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,wBAAwB,KAAK;MAC5C,OAAO;AACL,aAAK,6BAA6B;MACpC;IACF;AAEA,SAAA,2BAA2B,MAAY;AACrC,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,yBAAyB;MACxC,OAAO;AACL,aAAK,6BAA6B;MACpC;IACF;AAEA,SAAA,yBAAyB,CAAC,UAA0C;AAClE,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,uBAAuB,KAAK;MAC3C,OAAO;AACL,aAAK,4BAA4B;MACnC;IACF;AAEA,SAAA,0BAA0B,MAAY;AACpC,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,wBAAwB;MACvC,OAAO;AACL,aAAK,4BAA4B;MACnC;IACF;AAEA,SAAA,aAAa,CAAC,UAA8B;AAC1C,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,WAAW,KAAK;MAC/B,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,cAAc,MAAY;AACxB,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,YAAY;MAC3B,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF;AAEA,SAAA,cAAc,CAAC,MAAsB,UAA6B;AAChE,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,YAAY,MAAM,KAAK;MACtC,OAAO;AACL,aAAK,oBAAoB,IAAI,MAAM,KAAK;MAC1C;IACF;AAEA,SAAA,gBAAgB,CAAC,SAA+B;AAC9C,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,cAAc,IAAI;MACjC,OAAO;AACL,aAAK,oBAAoB,OAAO,IAAI;MACtC;IACF;AAEA,SAAA,cAAc,CAAC,MAAsB,UAA6B;AAChE,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,YAAY,MAAM,KAAK;MACtC,OAAO;AACL,aAAK,oBAAoB,IAAI,MAAM,KAAK;MAC1C;IACF;AAEA,SAAA,gBAAgB,CAAC,SAA+B;AAC9C,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,cAAc,IAAI;MACjC,OAAO;AACL,aAAK,oBAAoB,OAAO,IAAI;MACtC;IACF;AAEA,SAAA,mBAAmB,CAAC,MAAsB,UAAkC;AAC1E,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,iBAAiB,MAAM,KAAK;MAC3C,OAAO;AACL,aAAK,yBAAyB,IAAI,MAAM,KAAK;MAC/C;IACF;AAEA,SAAA,qBAAqB,CAAC,SAA+B;AACnD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,mBAAmB,IAAI;MACtC,OAAO;AACL,aAAK,yBAAyB,OAAO,IAAI;MAC3C;IACF;AAEA,SAAA,2BAA2B,CAAC,MAAsB,UAA0C;AAC1F,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,yBAAyB,MAAM,KAAK;MACnD,OAAO;AACL,aAAK,iCAAiC,IAAI,MAAM,KAAK;MACvD;IACF;AAEA,SAAA,6BAA6B,CAAC,SAA+B;AAC3D,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,2BAA2B,IAAI;MAC9C,OAAO;AACL,aAAK,iCAAiC,OAAO,IAAI;MACnD;IACF;AAEA,SAAA,0BAA0B,CAAC,MAAsB,UAAyC;AACxF,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,wBAAwB,MAAM,KAAK;MAClD,OAAO;AACL,aAAK,gCAAgC,IAAI,MAAM,KAAK;MACtD;IACF;AAEA,SAAA,4BAA4B,CAAC,SAA+B;AAC1D,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,0BAA0B,IAAI;MAC7C,OAAO;AACL,aAAK,gCAAgC,OAAO,IAAI;MAClD;IACF;AAEA,SAAA,4BAA4B,CAAC,MAAsB,UAA2C;AAC5F,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,0BAA0B,MAAM,KAAK;MACpD,OAAO;AACL,aAAK,kCAAkC,IAAI,MAAM,KAAK;MACxD;IACF;AAEA,SAAA,8BAA8B,CAAC,SAA+B;AAC5D,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,4BAA4B,IAAI;MAC/C,OAAO;AACL,aAAK,kCAAkC,OAAO,IAAI;MACpD;IACF;AAEA,SAAA,wBAAwB,CAAC,MAAsB,UAAuC;AACpF,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,sBAAsB,MAAM,KAAK;MAChD,OAAO;AACL,aAAK,8BAA8B,IAAI,MAAM,KAAK;MACpD;IACF;AAEA,SAAA,0BAA0B,CAAC,SAA+B;AACxD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,wBAAwB,IAAI;MAC3C,OAAO;AACL,aAAK,8BAA8B,OAAO,IAAI;MAChD;IACF;AAEA,SAAA,kBAAkB,CAAC,MAAsB,oBAA2C;AAClF,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,gBAAgB,MAAM,eAAe;MACpD,OAAO;AACL,aAAK,wBAAwB,IAAI,MAAM,eAAe;MACxD;IACF;AAEA,SAAA,oBAAoB,CAAC,SAA+B;AAClD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,QAAQ,kBAAkB,IAAI;MACrC,OAAO;AACL,aAAK,wBAAwB,OAAO,IAAI;MAC1C;IACF;AAEA,SAAA,cAAc,CAAC,aAAoD;AACjE,YAAM,WAAW,MAAG;AA1yBxB,YAAA;AA0yB2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,YAAY,QAAA;MAAA;AAEjD,UAAI,KAAK,SAAS;AAChB,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,eAAe,QAAQ;AACpD,eAAO,MAAM,KAAK,oBAAoB,OAAO,aAAa;MAC5D;IACF;AAEA,SAAA,WAAW,CAAC,OAAqB;AAC/B,YAAM,WAAW,MAAG;AArzBxB,YAAA;AAqzB2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,SAAS,EAAA;MAAA;AAC9C,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,SAAS;MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,YAAY,QAAQ;MACnD;IACF;AAEA,SAAA,mBAAmB,IAAI,SAAsD;AAC3E,YAAM,WAAW,MAAG;AA9zBxB,YAAA;AA8zB2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,iBAAiB,GAAG,IAAA;MAAA;AACzD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,SAAS;MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;MAC3D;IACF;AAEA,SAAA,mBAAmB,CAAC,SAAsC;AACxD,YAAM,WAAW,MAAG;AAv0BxB,YAAA;AAu0B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,iBAAiB,IAAA;MAAA;AACtD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,SAAS;MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;MAC3D;IACF;AAEA,SAAA,mBAAmB,CAAC,SAAsC;AACxD,YAAM,WAAW,MAAG;AAh1BxB,YAAA;AAg1B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,iBAAiB,IAAA;MAAA;AACtD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,aAAK,SAAS;MAChB,OAAO;AACL,aAAK,oBAAoB,IAAI,oBAAoB,QAAQ;MAC3D;IACF;AAEA,SAAA,wBAAwB,MAAY;AAClC,YAAM,WAAW,MAAG;AAz1BxB,YAAA;AAy1B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,sBAAA;MAAA;AACrC,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,iBAAS;MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,yBAAyB,QAAQ;MAChE;IACF;AAEA,SAAA,iBAAiB,MAAY;AAC3B,YAAM,WAAW,MAAG;AAl2BxB,YAAA;AAk2B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,eAAA;MAAA;AACrC,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,iBAAS;MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,kBAAkB,QAAQ;MACzD;IACF;AAEA,SAAA,gCAAgC,MAAY;AAC1C,YAAM,WAAW,MAAG;AA32BxB,YAAA;AA22B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,8BAAA;MAAA;AACrC,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,iBAAS;MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,iCAAiC,QAAQ;MACxE;IACF;AAEA,SAAA,+BAA+B,MAAY;AACzC,YAAM,WAAW,MAAG;AAp3BxB,YAAA;AAo3B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,6BAAA;MAAA;AACrC,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,iBAAS;MACX,OAAO;AACL,aAAK,oBAAoB,IAAI,gCAAgC,QAAQ;MACvE;IACF;AAEA,SAAA,yBAAyB,CAAC,WAA4C;AA53BxE,UAAA;AA63BI,YAAM,WAAW,MAAG;AA73BxB,YAAAC;AA63B2B,gBAAAA,MAAA,KAAK,YAAL,OAAA,SAAAA,IAAc,uBAAuB,MAAA;MAAA;AAC5D,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAK,KAAA,SAAS,MAAT,OAAA,SAAA,GAAY,MAAM,MAAM;QAQ7B,CAAA;MACF,OAAO;AACL,aAAK,oBAAoB,IAAI,0BAA0B,QAAQ;MACjE;IACF;AAIA,SAAA,8BAA8B,OAAO,WAA6D;AAChG,iBAAW,+BAA+B,4CAA4C;AACtF,YAAM,WAAW,MAAG;AAj5BxB,YAAA;AAi5B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,4BAA4B,MAAA;MAAA;AACjE,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;MACtE;IACF;AAEA,SAAA,6BAA6B,CAC3B,YACA,WACS;AA55Bb,UAAA;AA65BI,YAAM,WAAW,MAAG;AA75BxB,YAAAA;AA65B2B,gBAAAA,MAAA,KAAK,YAAL,OAAA,SAAAA,IAAc,2BAA2B,YAAY,MAAA;MAAA;AAC5E,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAK,KAAA,SAAS,MAAT,OAAA,SAAA,GAAY,MAAM,MAAM;QAQ7B,CAAA;MACF,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;MACrE;IACF;AAEA,SAAA,8BAA8B,OAAO,WAA6D;AAChG,YAAM,WAAW,MAAG;AA96BxB,YAAA;AA86B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,4BAA4B,MAAA;MAAA;AACjE,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,+BAA+B,QAAQ;MACtE;IACF;AAEA,SAAA,2BAA2B,OAAO,WAA0D;AAC1F,YAAM,WAAW,MAAG;AAv7BxB,YAAA;AAu7B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,yBAAyB,MAAA;MAAA;AAC9D,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,4BAA4B,QAAQ;MACnE;IACF;AAEA,SAAA,+BAA+B,OAC7B,WAC6C;AAC7C,YAAM,UAAU,MAAM,gBAAA,MAAK,iBAAA,iBAAA,EAAL,KAAA,IAAA;AACtB,aAAO,QAAQ,6BAA6B,MAAM;IACpD;AAEA,SAAA,qBAAqB,OAAO,WAA2E;AACrG,YAAM,WAAW,MAAG;AAv8BxB,YAAA;AAu8B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,mBAAmB,MAAA;MAAA;AACxD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,sBAAsB,QAAQ;MAC7D;IACF;AAEA,SAAA,6BAA6B,YAA8D;AACzF,YAAM,WAAW,MAAG;AAh9BxB,YAAA;AAg9B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,2BAAA;MAAA;AACrC,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,8BAA8B,QAAQ;MACrE;IACF;AAEA,SAAA,kBAAkB,OAAO,mBAAiE;AACxF,YAAM,WAAW,MAAG;AAz9BxB,YAAA;AAy9B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,gBAAgB,cAAA;MAAA;AACrD,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,mBAAmB,QAAQ;MAC1D;IACF;AAEA,SAAA,UAAmB,OACjB,0BACAC,aACkB;AAClB,YAAM,WAAW,MAAG;AAr+BxB,YAAA;AAq+B2B,gBAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,QAAQ,0BAAiCA,QAAA;MAAA;AAC9E,UAAI,KAAK,WAAW,aAAA,MAAK,OAAA,GAAS;AAChC,eAAO,SAAS;MAClB,OAAO;AACL,aAAK,oBAAoB,IAAI,WAAW,QAAQ;MAClD;IACF;AAvwBE,UAAM,EAAE,QAAQ,MAAM,aAAa,eAAe,IAAI,WAAW,CAAC;AAClE,iBAAA,MAAK,cAAe,WAAA;AACpB,iBAAA,MAAK,iBAAkB,cAAA;AACvB,iBAAA,MAAK,WAAY,WAAA,OAAA,SAAA,QAAS,QAAA;AAC1B,iBAAA,MAAK,SAAU,WAAA,OAAA,SAAA,QAAS,MAAA;AACxB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,OAAO,UAAU,IAAI,YAAY;AACtC,SAAK,KAAK,YAAY;EACxB;EA3DA,IAAI,iBAAqC;AACvC,WAAO,aAAA,MAAK,eAAA;EACd;EAEA,IAAI,SAAkB;AACpB,WAAO,aAAA,MAAK,OAAA;EACd;EAIA,OAAO,oBAAoB,SAAiC;AAK1D,QAAI,CAAC,UAAU,KAAK,CAAC,aAAA,MAAK,SAAA,KAAc,QAAQ,SAAS,aAAA,MAAK,SAAA,EAAU,UAAU,QAAQ,OAAQ;AAChG,mBAAA,MAAK,WAAY,IAAIF,kBAAgB,OAAO,CAAA;IAC9C;AACA,WAAO,aAAA,MAAK,SAAA;EACd;EAEA,OAAO,gBAAgB;AACrB,iBAAA,MAAK,WAAY,IAAA;EACnB;EAEA,IAAI,SAAiB;AAGnB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,aAAA,MAAK,OAAA,GAAS,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,EAAE;IACxE;AACA,QAAI,OAAO,aAAA,MAAK,OAAA,MAAY,YAAY;AACtC,YAAM,IAAI,MAAM,6CAA6C;IAC/D;AACA,WAAO,aAAA,MAAK,OAAA,KAAW;EACzB;EAEA,IAAI,WAAmB;AAGrB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,aAAA,MAAK,SAAA,GAAW,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,EAAE;IAC1E;AACA,QAAI,OAAO,aAAA,MAAK,SAAA,MAAc,YAAY;AACxC,YAAM,IAAI,MAAM,6CAA6C;IAC/D;AACA,WAAO,aAAA,MAAK,SAAA,KAAa;EAC3B;EAcA,IAAI,cAAuC;AA/O7C,QAAA;AAgPI,aAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,gBAAe,KAAK,QAAQ,eAAe;EAClE;EAEA,IAAI,eAAyC;AAnP/C,QAAA;AAoPI,YAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc;EACvB;EAEA,IAAI,cAAsB;AAvP5B,QAAA;AAwPI,aAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,gBAAe,aAAA,MAAK,YAAA,KAAgB;EAC3D;EAEA,IAAI,oBAA6B;AA3PnC,QAAA;AA4PI,aAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,sBAAqB,KAAK,QAAQ,mBAAmB;EAC5E;EAEA,IAAI,cAAuB;AAGzB,QAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,aAAO,gBAAgB,KAAK,QAAQ,aAAa,IAAI,IAAI,OAAO,SAAS,IAAI,GAAG,KAAK;IACvF;AACA,QAAI,OAAO,KAAK,QAAQ,gBAAgB,YAAY;AAClD,YAAM,IAAI,MAAM,6CAA6C;IAC/D;AACA,WAAO;EACT;EAqFA,MAAM,cAAwE;AA9VhF,QAAA,IAAA,IAAA;AA+VI,QAAI,KAAK,SAAS,aAAa,aAAA,MAAK,OAAA,GAAS;AAC3C;IACF;AAYA,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,uBAAuB,KAAK;AACnC,aAAO,0BAA0B,KAAK;AACtC,aAAO,oBAAoB,KAAK;AAChC,aAAO,iBAAiB,KAAK;IAC/B;AAEA,QAAI;AACF,UAAI,KAAK,OAAO;AAEd,YAAI;AAEJ,YAAI,cAAwE,KAAK,KAAK,GAAG;AAEvF,cAAI,IAAI,KAAK,MAAM,KAAK,kBAAkB,KAAK,eAAe,IAAI;YAChE,UAAU,KAAK;YACf,QAAQ,KAAK;UACf,CAAQ;AACR,gBAAM,EAAE,KAAK,KAAK,OAAO;QAC3B,OAAO;AAEL,cAAI,KAAK;AAET,cAAI,CAAC,EAAE,QAAQ,GAAG;AAChB,kBAAM,EAAE,KAAK,KAAK,OAAO;UAC3B;QACF;AAEA,eAAO,QAAQ;MACjB,OAAO;AAEL,YAAI,CAAC,OAAO,OAAO;AACjB,gBAAM,kBAAkB;YACtB,GAAG,KAAK;YACR,aAAa,KAAK;YAClB,gBAAgB,KAAK;YACrB,UAAU,KAAK;YACf,QAAQ,KAAK;UACf,CAAC;QACH;AAEA,YAAI,CAAC,OAAO,OAAO;AACjB,gBAAM,IAAI,MAAM,+DAA+D;QACjF;AAEA,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO;MACtC;AAEA,aAAO,MAAM,eAAc,KAAA,KAAK,QAAQ,gBAAb,OAAA,KAA4B,EAAE,MAAM,sBAAc,SAAS,SAAgB;AAEtG,YAAI,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,aAAU,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,QAAA,IAAW;AACnD,eAAO,KAAK,eAAe,OAAO,KAAK;MACzC;AACA;IACF,SAAS,KAAK;AACZ,YAAM,QAAQ;AAId,UAAI,OAAuC;AACzC,gBAAQ,MAAM,MAAM,SAAS,MAAM,WAAW,KAAK;MACrD,OAAO;AACL,cAAM;MACR;AACA;IACF;EACF;EA2EA,IAAI,UAA8B;AA1fpC,QAAA;AA2fI,YAAO,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc;EACvB;EAEA,IAAI,SAAqC;AACvC,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IAEtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,UAAoD;AACtD,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IACtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,OAAwC;AAC1C,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IACtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,eAAwD;AAC1D,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,QAAQ;IACtB,OAAO;AACL,aAAO;IACT;EACF;EAEA,IAAI,0BAA+B;AACjC,QAAI,KAAK,SAAS;AAChB,aAAQ,KAAK,QAAgB;IAE/B,OAAO;AACL,aAAO;IACT;EACF;EAEA,8BAA8B,MAAiB;AAC7C,QAAI,KAAK,WAAW,gCAAgC,KAAK,SAAS;AAC/D,WAAK,QAAgB,2BAA2B,IAAI;IACvD,OAAO;AACL,aAAO;IACT;EACF;AA8bF;AAh0BE,UAAA,oBAAA,QAAA;AACA,UAAA,oBAAA,QAAA;AACA,YAAA,oBAAA,QAAA;AACA,eAAA,oBAAA,QAAA;AACA,kBAAA,oBAAA,QAAA;AAUO,YAAA,oBAAA,QAAA;AA2JP,kBAAA,oBAAA,QAAA;AAAA,oBAAe,WAAiD;AAC9D,SAAO,IAAI,QAA6C,CAAA,YAAW;AACjE,QAAI,aAAA,MAAK,OAAA,GAAS;AAChB,cAAQ,KAAK,OAAQ;IACvB;AACA,SAAK,YAAY,MAAM,QAAQ,KAAK,OAAQ,CAAC;EAC/C,CAAC;AACH;AAlKA,aApCmB,kBAoCZ,WAAP,MAAA;AApCF,IAAqB,kBAArB;;;AC5IO,IAAM,cAAc,CAACG,cAAsB,OAAkB,iBAA2C;AAC7G,MAAI,CAACA,gBAAe,cAAc;AAChC,WAAO,0BAA0B,YAAY;EAC/C;AACA,SAAO,0BAA0B,KAAK;AACxC;AAEA,IAAM,4BAA4B,CAAC,iBAA+B;AAChE,QAAM,SAAS,aAAa;AAC5B,QAAM,OAAO,aAAa;AAC1B,QAAM,YAAY,aAAa;AAC/B,QAAM,UAAU,aAAa;AAC7B,QAAM,eAAe,aAAa;AAClC,QAAM,QAAQ,aAAa;AAC3B,QAAM,UAAU,aAAa;AAC7B,QAAM,iBAAiB,aAAa;AACpC,QAAM,UAAU,aAAa;AAC7B,QAAM,QAAQ,aAAa;AAE3B,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,4BAA4B;IAC5B,wBAAwB;EAC1B;AACF;AAEA,IAAM,4BAA4B,CAAC,UAAqB;AA7CxD,MAAA;AA8CE,QAAM,SAAoC,MAAM,OAAO,MAAM,KAAK,KAAK,MAAM;AAC7E,QAAM,OAAO,MAAM;AACnB,QAAM,YAAuC,MAAM,UAAU,MAAM,QAAQ,KAAK,MAAM;AACtF,QAAM,UAAU,MAAM;AACtB,QAAM,QAAQ,WAAA,OAAA,SAAA,QAAS;AACvB,QAAM,eAAe,MAAM;AAC3B,QAAM,QAAmC,MAAM,eAAe,MAAM,aAAa,KAAK,MAAM;AAC5F,QAAM,UAAU,gBAAA,OAAA,SAAA,aAAc;AAC9B,QAAM,aAAa,gBACf,KAAA,QAAA,OAAA,SAAA,KAAM,4BAAN,OAAA,SAAA,GAA+B,KAAK,CAAA,OAAM,GAAG,aAAa,OAAO,KAAA,IACjE;AACJ,QAAM,iBAAiB,aAAa,WAAW,cAAc;AAC7D,QAAM,UAAU,aAAa,WAAW,OAAO;AAE/C,QAAM,6BAA6B,MAAM;AACzC,QAAM,yBAAyB,MAAM;AAErC,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AACF;;;AC1EO,IAAM,CAAC,aAAa,cAAc,IAAI,qBAQ1C,aAAa;;;AHWT,SAAS,qBAAqB,OAAiD;AACpF,QAAM,EAAE,wBAAwB,cAAc,SAAS,IAAI;AAC3D,QAAM,EAAE,iBAAiB,OAAO,QAAQC,aAAY,IAAI,yBAAyB,sBAAsB;AAEvG,MAAI,uBAAuB,aAAa;AACtC,eAAW,eAAe,+BAA+B;EAC3D;AAEA,QAAM,CAAC,OAAO,QAAQ,IAAI,eAAAC,QAAM,SAAoC;IAClE,QAAQ,MAAM;IACd,SAAS,MAAM;IACf,MAAM,MAAM;IACZ,cAAc,MAAM;IACpB,4BAA4B;IAC5B,wBAAwB;EAC1B,CAAC;AAED,iBAAAA,QAAM,UAAU,MAAM;AACpB,WAAO,MAAM,YAAY,CAAA,MAAK,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;EAClD,GAAG,CAAC,CAAC;AAEL,QAAM,eAAe,YAAYD,cAAa,OAAO,YAAY;AACjE,QAAM,WAAW,eAAAC,QAAM,QAAQ,OAAO,EAAE,OAAO,MAAM,IAAI,CAACD,YAAW,CAAC;AACtE,QAAM,YAAY,eAAAC,QAAM,QAAQ,OAAO,EAAE,OAAO,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,CAAC;AAE/E,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AAEJ,QAAM,UAAU,eAAAA,QAAM,QAAQ,MAAM;AAClC,UAAM,QAAQ,EAAE,WAAW,QAAQ,OAAO,OAAO,SAAS,SAAS,eAAe;AAClF,WAAO,EAAE,MAAM;EACjB,GAAG,CAAC,WAAW,QAAQ,OAAO,OAAO,SAAS,OAAO,CAAC;AACtD,QAAM,UAAU,eAAAA,QAAM,QAAQ,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC;AACrE,QAAM,aAAa,eAAAA,QAAM,QAAQ,OAAO,EAAE,OAAO,QAAQ,IAAI,CAAC,WAAW,OAAO,CAAC;AACjF,QAAM,kBAAkB,eAAAA,QAAM,QAAQ,MAAM;AAC1C,UAAM,QAAQ;MACZ;MACA;MACA;IACF;AACA,WAAO,EAAE,MAAM;EACjB,GAAG,CAAC,OAAO,cAAc,4BAA4B,sBAAsB,CAAC;AAE5E;;IAEE,eAAAA,QAAA,cAAC,uBAAuB,UAAvB,EAAgC,OAAO,SAAA,GACtC,eAAAA,QAAA,cAAC,cAAc,UAAd,EAAuB,OAAO,UAAA,GAC7B,eAAAA,QAAA,cAAC,eAAe,UAAf,EAAwB,OAAO,WAAA,GAC9B,eAAAA,QAAA,cAAC,sBAAA,EAAsB,GAAG,gBAAgB,MAAA,GACxC,eAAAA,QAAA,cAAC,YAAY,UAAZ,EAAqB,OAAO,QAAA,GAC3B,eAAAA,QAAA,cAAC,YAAY,UAAZ,EAAqB,OAAO,QAAA,GAAU,QAAS,CAClD,CACF,CACF,CACF,CACF;;AAEJ;AAEA,IAAM,2BAA2B,CAAC,YAAoC;AACpE,QAAM,CAAC,QAAQ,SAAS,IAAI,eAAAA,QAAM,SAAS,KAAK;AAChD,QAAM,kBAAkB,eAAAA,QAAM,QAAQ,MAAM,gBAAgB,oBAAoB,OAAO,GAAG,CAAC,CAAC;AAE5F,iBAAAA,QAAM,UAAU,MAAM;AACpB,oBAAgB,wBAAwB,EAAE,YAAY,QAAQ,WAAW,CAAC;EAC5E,GAAG,CAAC,QAAQ,UAAU,CAAC;AAEvB,iBAAAA,QAAM,UAAU,MAAM;AACpB,oBAAgB,wBAAwB,EAAE,QAAQ,CAAC;EACrD,GAAG,CAAC,QAAQ,YAAY,CAAC;AAEzB,iBAAAA,QAAM,UAAU,MAAM;AACpB,oBAAgB,YAAY,MAAM,UAAU,IAAI,CAAC;EACnD,GAAG,CAAC,CAAC;AAEL,iBAAAA,QAAM,UAAU,MAAM;AACpB,WAAO,MAAM;AACX,sBAAgB,cAAc;IAChC;EACF,GAAG,CAAC,CAAC;AAEL,SAAO,EAAE,iBAAiB,OAAO;AACnC;;;AzC1GA,mCAAmC;EACjC,aAAa;AACf,CAAC;AAOD,SAAS,kBAAkB,OAAwC;AACjE,QAAM,EAAE,cAAc,UAAU,GAAG,2BAA2B,IAAI;AAClE,QAAM,EAAE,cAAc,IAAI,iBAAiB,IAAI,OAAO,qBAAqB,IAAI;AAE/E,MAAI,CAAC,sBAAsB;AACzB,QAAI,CAAC,kBAAkB,CAAC,aAAa;AACnC,mBAAa,gCAAgC;IAC/C,WAAW,kBAAkB,CAAC,iBAAiB,cAAc,GAAG;AAC9D,mBAAa,gCAAgC,EAAE,KAAK,eAAe,CAAC;IACtE,WAAW,CAAC,kBAAkB,eAAe,CAAC,uBAAuB,WAAW,GAAG;AACjF,mBAAa,6BAA6B,EAAE,KAAK,YAAY,CAAC;IAChE;EACF;AAEA,SACE,eAAAC,QAAA,cAAC,iBAAiB,UAAjB,EAA0B,OAAO,uBAAuB,aAAA,GACvD,eAAAA,QAAA;IAAC;IAAA;MACC;MACA,wBAAwB;IAAA;IAEvB;EACH,CACF;AAEJ;AAEA,IAAM,gBAAgB,6BAA6B,mBAAmB,iBAAiB,2BAA2B;AAElH,cAAc,cAAc;;;A6CzC5B,IAAAC,iBAAkB;;;AC4CX,SAAS,UAAyB;AACvC,QAAM,OAAO,eAAe;AAE5B,MAAI,SAAS,QAAW;AACtB,WAAO,EAAE,UAAU,OAAO,YAAY,QAAW,MAAM,OAAU;EACnE;AAEA,MAAI,SAAS,MAAM;AACjB,WAAO,EAAE,UAAU,MAAM,YAAY,OAAO,MAAM,KAAK;EACzD;AAEA,SAAO,EAAE,UAAU,MAAM,YAAY,MAAM,KAAK;AAClD;;;ACvDA,IAAAC,iBAA4B;;;ACF5B,IAAM,cAAc,CAAC,oBAAqC;AACxD,SAAO,IAAI,QAAc,CAAA,YAAW;AAClC,QAAI,gBAAgB,QAAQ;AAC1B,cAAQ;IACV;AACA,oBAAgB,YAAY,OAAO;EACrC,CAAC;AACH;AAKO,IAAM,iBAAiB,CAAC,oBAAqC;AAClE,SAAO,OAAO,YAAiB;AAC7B,UAAM,YAAY,eAAe;AACjC,QAAI,CAAC,gBAAgB,SAAS;AAC5B,aAAO;IACT;AACA,WAAO,gBAAgB,QAAQ,SAAS,OAAO;EACjD;AACF;AAKO,IAAM,gBAAgB,CAAC,oBAAqC;AACjE,SAAO,UAAU,SAAc;AAC7B,UAAM,YAAY,eAAe;AACjC,WAAO,gBAAgB,QAAQ,GAAG,IAAI;EACxC;AACF;;;AD8EO,IAAM,UAAmB,MAAM;AACpC,QAAM,EAAE,WAAW,QAAQ,OAAO,OAAO,SAAS,SAAS,eAAe,IAAI,eAAe;AAC7F,QAAM,kBAAkB,0BAA0B;AAElD,QAAM,eAAqB,4BAAY,eAAe,eAAe,GAAG,CAAC,eAAe,CAAC;AACzF,QAAM,cAAmB,4BAAY,cAAc,eAAe,GAAG,CAAC,eAAe,CAAC;AAEtF,QAAM,UAAM;IACV,CAAC,WAAmE;AAClE,UAAI,EAAC,UAAA,OAAA,SAAA,OAAQ,eAAc,EAAC,UAAA,OAAA,SAAA,OAAQ,OAAM;AACxC,cAAM,IAAI,MAAM,kCAAkC;MACpD;AAEA,UAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,gBAAgB;AACpD,eAAO;MACT;AAEA,UAAI,OAAO,YAAY;AACrB,eAAO,eAAe,SAAS,OAAO,UAAU;MAClD;AAEA,UAAI,OAAO,MAAM;AACf,eAAO,YAAY,OAAO;MAC5B;AAEA,aAAO;IACT;IACA,CAAC,OAAO,SAAS,QAAQ,cAAc;EACzC;AAEA,MAAI,cAAc,UAAa,WAAW,QAAW;AACnD,WAAO;MACL,UAAU;MACV,YAAY;MACZ;MACA;MACA,OAAO;MACP,OAAO;MACP,SAAS;MACT,SAAS;MACT,KAAK;MACL;MACA;IACF;EACF;AAEA,MAAI,cAAc,QAAQ,WAAW,MAAM;AACzC,WAAO;MACL,UAAU;MACV,YAAY;MACZ;MACA;MACA,OAAO;MACP,OAAO;MACP,SAAS;MACT,SAAS;MACT,KAAK,MAAM;MACX;MACA;IACF;EACF;AAEA,MAAI,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS;AACnD,WAAO;MACL,UAAU;MACV,YAAY;MACZ;MACA;MACA,OAAO,SAAS;MAChB;MACA;MACA,SAAS,WAAW;MACpB;MACA;MACA;IACF;EACF;AAEA,MAAI,CAAC,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,OAAO;AACrC,WAAO;MACL,UAAU;MACV,YAAY;MACZ;MACA;MACA,OAAO,SAAS;MAChB,OAAO;MACP,SAAS;MACT,SAAS;MACT,KAAK,MAAM;MACX;MACA;IACF;EACF;AAEA,QAAM,IAAI,MAAM,iBAAiB;AACnC;;;AE9JO,IAAM,aAAyB,MAAM;AAC1C,QAAM,UAAU,kBAAkB;AAElC,MAAI,YAAY,QAAW;AACzB,WAAO,EAAE,UAAU,OAAO,YAAY,QAAW,SAAS,OAAU;EACtE;AAEA,MAAI,YAAY,MAAM;AACpB,WAAO,EAAE,UAAU,MAAM,YAAY,OAAO,SAAS,KAAK;EAC5D;AAEA,SAAO,EAAE,UAAU,MAAM,YAAY,MAAM,QAAQ;AACrD;;;AC1DO,IAAM,WAAW,MAAmB;AACzC,QAAM,kBAAkB,0BAA0B;AAGlD,SAAO;AACT;;;ACwBO,IAAM,YAAuB,MAAM;AACxC,QAAM,kBAAkB,0BAA0B;AAClD,QAAM,SAAS,iBAAiB;AAEhC,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,UAAU,OAAO,QAAQ,QAAW,YAAY,QAAW,WAAW,OAAU;EAC3F;AAEA,SAAO;IACL,UAAU;IACV,QAAQ,OAAO;IACf,YAAY,gBAAgB;IAC5B,WAAW,gBAAgB;EAC7B;AACF;;;ACdO,IAAM,YAAuB,MAAM;AACxC,QAAM,kBAAkB,0BAA0B;AAClD,QAAM,SAAS,iBAAiB;AAEhC,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,UAAU,OAAO,QAAQ,QAAW,YAAY,QAAW,WAAW,OAAU;EAC3F;AAEA,SAAO;IACL,UAAU;IACV,QAAQ,OAAO;IACf,YAAY,gBAAgB;IAC5B,WAAW,gBAAgB;EAC7B;AACF;;;ACbO,IAAM,iBAAiC,MAAM;AAClD,QAAM,kBAAkB,0BAA0B;AAClD,QAAM,SAAS,iBAAiB;AAEhC,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,UAAU,OAAO,UAAU,QAAW,YAAY,QAAW,WAAW,OAAU;EAC7F;AAEA,SAAO;IACL,UAAU;IACV,UAAU,OAAO;IACjB,YAAY,gBAAgB;IAC5B,WAAW,gBAAgB;EAC7B;AACF;;;ACvCA,IAAAC,iBAAkB;AAmBlB,SAAS,aACP,UACsF;AACtF,aAAW,gBAAgB,6BAA6B;AAExD,QAAM,EAAE,oBAAoB,oBAAoB,IAAI,eAAAC,QAAM,QAAQ,MAAM,SAAS,oBAAoB,GAAG,CAAC,QAAQ,CAAC;AAElH,iBAAAA,QAAM,UAAU,MAAM;AACpB,WAAO;EACT,GAAG,CAAC,CAAC;AAEL,SAAO;IACL;IACA;EACF;AACF;;;ACnCA,IAAAC,iBAAkB;AAUlB,SAAS,aACP,UACsF;AACtF,QAAM,EAAE,oBAAoB,oBAAoB,IAAI,eAAAC,QAAM,QAAQ,MAAM,SAAS,oBAAoB,GAAG,CAAC,QAAQ,CAAC;AAElH,iBAAAA,QAAM,UAAU,MAAM;AACpB,WAAO;EACT,GAAG,CAAC,CAAC;AAEL,SAAO;IACL;IACA;EACF;AACF;;;AVfO,IAAM,WAAW,CAAC,EAAE,SAAS,MAA4D;AAC9F,QAAM,EAAE,OAAO,IAAI,eAAe;AAClC,MAAI,QAAQ;AACV,WAAO,eAAAC,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;EACrB;AACA,SAAO;AACT;AAEO,IAAM,YAAY,CAAC,EAAE,SAAS,MAA4D;AAC/F,QAAM,EAAE,OAAO,IAAI,eAAe;AAClC,MAAI,WAAW,MAAM;AACnB,WAAO,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;EACrB;AACA,SAAO;AACT;AAEO,IAAM,cAAc,CAAC,EAAE,SAAS,MAA4D;AACjG,QAAM,kBAAkB,0BAA0B;AAClD,MAAI,CAAC,gBAAgB,QAAQ;AAC3B,WAAO;EACT;AACA,SAAO,eAAAA,QAAA,cAAC,iBAAA,MAAiB,QAAS;AACpC;AAEO,IAAM,eAAe,CAAC,EAAE,SAAS,MAA4D;AAClG,QAAM,kBAAkB,0BAA0B;AAClD,MAAI,gBAAgB,QAAQ;AAC1B,WAAO;EACT;AACA,SAAO,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAyCO,IAAM,UAAU,CAAC,EAAE,UAAU,UAAU,GAAG,qBAAqB,MAAoB;AACxF,QAAM,EAAE,UAAU,KAAK,OAAO,IAAI,QAAQ;AAK1C,MAAI,CAAC,UAAU;AACb,WAAO;EACT;AAKA,QAAM,eAAe,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,YAAA,OAAA,WAAY,IAAK;AAEzC,QAAM,aAAa,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,QAAS;AAE/B,MAAI,CAAC,QAAQ;AACX,WAAO;EACT;AAKA,MAAI,OAAO,qBAAqB,cAAc,YAAY;AACxD,QAAI,qBAAqB,UAAU,GAAG,GAAG;AACvC,aAAO;IACT;AACA,WAAO;EACT;AAEA,MAAI,qBAAqB,QAAQ,qBAAqB,YAAY;AAChE,QAAI,IAAI,oBAAoB,GAAG;AAC7B,aAAO;IACT;AACA,WAAO;EACT;AAMA,SAAO;AACT;AAEO,IAAM,mBAAmB,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA4C;AACvG,QAAM,EAAE,QAAQ,QAAQ,IAAI;AAE5B,QAAM,EAAE,wBAAwB,IAAI;AAEpC,QAAM,oBAAoB,OAAO,kBAAkB,OAAO,eAAe,SAAS;AAElF,iBAAAA,QAAM,UAAU,MAAM;AACpB,QAAI,YAAY,QAAQ,qBAAqB,yBAAyB;AACpE,YAAM,EAAE,mBAAmB,IAAI,wBAAwB;AACvD,WAAK,MAAM,SAAS,kBAAkB;IACxC,OAAO;AACL,WAAK,MAAM,iBAAiB,KAAK;IACnC;EACF,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,kBAAkB;AAEd,IAAM,mBAAmB,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM,MAA4C;AACvG,iBAAAA,QAAM,UAAU,MAAM;AACpB,SAAK,MAAM,iBAAiB,KAAK;EACnC,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,kBAAkB;AAEd,IAAM,wBAAwB,UAAU,CAAC,EAAE,MAAM,MAAM;AAC5D,iBAAAA,QAAM,UAAU,MAAM;AACpB,UAAM,sBAAsB;EAC9B,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,uBAAuB;AAEnB,IAAM,gCAAgC,UAAU,CAAC,EAAE,MAAM,MAAM;AACpE,iBAAAA,QAAM,UAAU,MAAM;AACpB,UAAM,8BAA8B;EACtC,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,+BAA+B;AAE3B,IAAM,+BAA+B,UAAU,CAAC,EAAE,MAAM,MAAM;AACnE,iBAAAA,QAAM,UAAU,MAAM;AACpB,UAAM,6BAA6B;EACrC,GAAG,CAAC,CAAC;AAEL,SAAO;AACT,GAAG,8BAA8B;AAE1B,IAAM,mCAAmC;EAC9C,CAAC,EAAE,OAAO,GAAG,6BAA6B,MAAgD;AACxF,mBAAAA,QAAM,UAAU,MAAM;AACpB,WAAK,MAAM,uBAAuB,4BAA4B;IAChE,GAAG,CAAC,CAAC;AAEL,WAAO;EACT;EACA;AACF;AAEO,IAAM,yBAAyB,CAAC,EAAE,SAAS,MAAqD;AACrG,QAAM,UAAU,kBAAkB;AAClC,SAAO,eAAAA,QAAA,cAAC,eAAAA,QAAM,UAAN,EAAe,KAAK,UAAU,QAAQ,KAAK,WAAA,GAAa,QAAS;AAC3E;;;AWpMA,IAAAC,iBAAkB;AAKX,IAAM,WAAW,CAAmC,WAAmC,gBAAyB;AACrH,gBAAc,eAAe,UAAU,eAAe,UAAU,QAAQ;AACxE,YAAU,cAAc;AACxB,QAAM,MAAiC,CAAC,UAA2B;AACjE,UAAM,OAAO,eAAe;AAE5B,QAAI,CAAC,MAAM;AACT,aAAO;IACT;AAEA,WACE,eAAAC,QAAA;MAAC;MAAA;QACE,GAAI;QACL;MAAA;IACF;EAEJ;AAEA,MAAI,cAAc,YAAY,WAAW;AACzC,SAAO;AACT;AAEO,IAAM,WAER,CAAC,EAAE,SAAS,MAAM;AACrB,QAAM,OAAO,eAAe;AAE5B,MAAI,OAAO,aAAa,YAAY;AAClC,UAAM,IAAI,MAAM,4BAA4B;EAC9C;AAEA,MAAI,CAAC,MAAM;AACT,WAAO;EACT;AAEA,SAAO,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,SAAS,IAAI,CAAE;AAC3B;;;ACzCA,IAAAC,iBAAkB;AAKX,IAAM,cAAc,CACzB,WACA,gBACG;AACH,gBAAc,eAAe,UAAU,eAAe,UAAU,QAAQ;AACxE,YAAU,cAAc;AACxB,QAAM,MAAoC,CAAC,UAA8B;AACvE,UAAM,UAAU,kBAAkB;AAElC,QAAI,CAAC,SAAS;AACZ,aAAO;IACT;AAEA,WACE,eAAAC,QAAA;MAAC;MAAA;QACE,GAAI;QACL;MAAA;IACF;EAEJ;AAEA,MAAI,cAAc,eAAe,WAAW;AAC5C,SAAO;AACT;AAEO,IAAM,cAER,CAAC,EAAE,SAAS,MAAM;AACrB,QAAM,UAAU,kBAAkB;AAElC,MAAI,OAAO,aAAa,YAAY;AAClC,UAAM,IAAI,MAAM,4BAA4B;EAC9C;AAEA,MAAI,CAAC,SAAS;AACZ,WAAO;EACT;AAEA,SAAO,eAAAA,QAAA,cAAA,eAAAA,QAAA,UAAA,MAAG,SAAS,OAAO,CAAE;AAC9B;;;AC7CA,IAAAC,iBAAkB;AAMX,IAAM,eAAe,UAAU,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAwC;AACzG,QAAM,EAAE,gBAAgB,gBAAgB,aAAa,MAAM,GAAG,KAAK,IAAI;AAEvE,aAAW,0BAA0B,UAAU,SAAS;AACxD,QAAM,QAAQ,kBAAkB,QAAQ,EAAE,cAAc;AAExD,QAAM,eAAe,MAAM;AACzB,UAAM,OAAO,EAAE,gBAAgB,gBAAgB,YAAY;AAC3D,QAAI,SAAS,SAAS;AACpB,aAAO,MAAM,WAAW,IAAI;IAC9B;AACA,WAAO,MAAM,iBAAiB,IAAI;EACpC;AAEA,QAAM,2BAAoD,OAAM,MAAK;AACnE,UAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,WAAO,aAAa;EACtB;AAEA,QAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,SAAO,eAAAC,QAAM,aAAa,OAAsC,UAAU;AAC5E,GAAG,cAAc;;;AC3BjB,IAAAC,iBAAkB;AAMX,IAAM,eAAe,UAAU,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAwC;AACzG,QAAM,EAAE,gBAAgB,gBAAgB,aAAa,MAAM,gBAAgB,GAAG,KAAK,IAAI;AAEvF,aAAW,0BAA0B,UAAU,SAAS;AACxD,QAAM,QAAQ,kBAAkB,QAAQ,EAAE,cAAc;AAExD,QAAM,eAAe,MAAM;AACzB,UAAM,OAAO,EAAE,gBAAgB,gBAAgB,aAAa,eAAe;AAE3E,QAAI,SAAS,SAAS;AACpB,aAAO,MAAM,WAAW,IAAI;IAC9B;AAEA,WAAO,MAAM,iBAAiB,IAAI;EACpC;AAEA,QAAM,2BAAoD,OAAM,MAAK;AACnE,UAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,WAAO,aAAa;EACtB;AAEA,QAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,SAAO,eAAAC,QAAM,aAAa,OAAsC,UAAU;AAC5E,GAAG,cAAc;;;AC5BjB,IAAAC,iBAAkB;AAYX,IAAM,gBAAgB;EAC3B,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAkE;AAC7F,UAAM,EAAE,iBAAiB,gBAAgB,GAAG,KAAK,IAAI;AAErD,eAAW,0BAA0B,UAAU,UAAU;AACzD,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,eAAe;AAEzD,UAAM,eAAe,MAAM;AACzB,aAAO,MAAM,QAAQ,iBAAiB,cAAc;IACtD;AAEA,UAAM,2BAAoD,OAAM,MAAK;AACnE,YAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,aAAO,aAAa;IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAO,eAAAC,QAAM,aAAa,OAAsC,UAAU;EAC5E;EACA;AACF;;;ACjCA,IAAAC,iBAAkB;AAMX,IAAM,2BAA2B;EACtC,CAAC,EAAE,OAAO,UAAU,GAAG,MAAM,MAAoD;AAC/E,UAAM,EAAE,aAAa,GAAG,KAAK,IAAI;AAEjC,eAAW,0BAA0B,UAAU,uBAAuB;AACtE,UAAM,QAAQ,kBAAkB,QAAQ,EAAE,0BAA0B;AAIpE,UAAM,eAAe,YAAY;AAC/B,qBAAe,eAAe;AAC5B,cAAM,MAAM,yBAAyB,EAAE,YAAY,CAAC;MACtD;AACA,WAAK,aAAa;IACpB;AAEA,UAAM,2BAAoD,OAAM,MAAK;AACnE,YAAM,YAAa,MAAc,MAAM,OAAO,EAAE,CAAC;AACjD,aAAO,aAAa;IACtB;AAEA,UAAM,aAAa,EAAE,GAAG,MAAM,SAAS,yBAAyB;AAChE,WAAO,eAAAC,QAAM,aAAa,OAAsC,UAAU;EAC5E;EACA;AACF;", "names": ["React", "useState", "useLayoutEffect", "useEffect", "useDebugValue", "__export", "import_react", "packageName", "customMessages", "React", "import_react", "React", "import_react", "React", "import_react", "import_react", "import_react", "import_react", "SWRConfig", "import_react", "import_react", "noop", "cache", "React", "mutate", "use", "middleware", "useSWR", "middleware", "use", "ReactExports", "cache", "compare", "cachedData", "SWRConfig", "import_react", "import_shim", "cache", "mutate", "import_react", "React", "__export", "SWRConfig", "default", "default", "_a", "_b", "undefinedPaginatedResource", "React", "import_react", "React", "React", "React", "React", "children", "import_react", "_IsomorphicClerk", "_a", "options", "clerkLoaded", "clerkLoaded", "React", "React", "import_react", "import_react", "import_react", "React", "import_react", "React", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React"]}