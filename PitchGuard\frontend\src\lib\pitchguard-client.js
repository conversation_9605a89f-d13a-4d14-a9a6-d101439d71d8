/**
 * PitchGuard Client Library
 * Handles client-side encryption and API communication
 */

export class PitchGuardClient {
  constructor(apiUrl = 'http://localhost:8000') {
    this.apiUrl = apiUrl
  }

  /**
   * Generate a random AES-GCM key
   */
  async generateAESKey() {
    return await window.crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256
      },
      true, // extractable
      ['encrypt', 'decrypt']
    )
  }

  /**
   * Generate a random initialization vector
   */
  generateIV() {
    return window.crypto.getRandomValues(new Uint8Array(12))
  }

  /**
   * Encrypt text using AES-GCM
   */
  async encryptText(text, key, iv) {
    const encoder = new TextEncoder()
    const data = encoder.encode(text)

    const encrypted = await window.crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv
      },
      key,
      data
    )

    return {
      ciphertext: this.arrayBufferToBase64(encrypted),
      iv: this.arrayBufferToBase64(iv)
    }
  }

  /**
   * Export AES key to base64
   */
  async exportKeyToBase64(key) {
    const exported = await window.crypto.subtle.exportKey('raw', key)
    return this.arrayBufferToBase64(exported)
  }

  /**
   * Convert ArrayBuffer to base64 string
   */
  arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  /**
   * Send encrypted payload to backend
   */
  async sendToBackend(payload) {
    const response = await fetch(`${this.apiUrl}/score`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(payload)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  }

  /**
   * Main method to analyze a pitch
   */
  async analyzePitch(pitchText) {
    if (!pitchText || pitchText.trim().length < 10) {
      throw new Error('Pitch text is too short')
    }

    try {
      // Step 1: Generate encryption key and IV
      const key = await this.generateAESKey()
      const iv = this.generateIV()

      // Step 2: Encrypt the pitch text
      const encryptedData = await this.encryptText(pitchText, key, iv)

      // Step 3: Export key to base64
      const keyBase64 = await this.exportKeyToBase64(key)

      // Step 4: Prepare payload
      const payload = {
        ciphertext: encryptedData.ciphertext,
        iv: encryptedData.iv,
        aes_key: keyBase64
      }

      // Step 5: Send to backend and return result
      const result = await this.sendToBackend(payload)

      // Step 6: Clear sensitive data from memory
      // Note: This is a best-effort cleanup, not cryptographically secure
      pitchText = null
      payload.aes_key = null

      return result

    } catch (error) {
      console.error('PitchGuard analysis error:', error)
      throw error
    }
  }

  /**
   * Test encryption/decryption locally (for debugging)
   */
  async testEncryption(text) {
    const key = await this.generateAESKey()
    const iv = this.generateIV()
    
    const encrypted = await this.encryptText(text, key, iv)
    console.log('Encrypted:', encrypted)
    
    return {
      key: await this.exportKeyToBase64(key),
      ...encrypted
    }
  }

  /**
   * Generate sample payload for testing
   */
  async generateSamplePayload() {
    const samplePitch = `
    Our startup, EcoClean, revolutionizes urban waste management through AI-powered sorting robots. 
    We've identified that 60% of recyclable materials end up in landfills due to improper sorting. 
    Our solution uses computer vision and machine learning to achieve 95% sorting accuracy, 
    reducing waste processing costs by 40%. With a team of MIT engineers and partnerships with 
    3 major cities, we're seeking $2M to scale our pilot program nationwide.
    `.trim()
    
    return await this.testEncryption(samplePitch)
  }

  /**
   * Check if the backend is available
   */
  async healthCheck() {
    try {
      const response = await fetch(`${this.apiUrl}/health`)
      return response.ok
    } catch {
      return false
    }
  }
}