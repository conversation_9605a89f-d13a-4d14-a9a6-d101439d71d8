import type { OutMode, OutModeAlt } from "../../../../Enums/Modes/OutMode";
import type { IDistance } from "../../../../Core/Interfaces/IDistance";
import type { IMove } from "../../../Interfaces/Particles/Move/IMove";
import type { IOptionLoader } from "../../../Interfaces/IOptionLoader";
import { MoveAngle } from "./MoveAngle";
import { MoveAttract } from "./MoveAttract";
import { MoveCenter } from "./MoveCenter";
import { MoveDirection } from "../../../../Enums/Directions/MoveDirection";
import type { MoveDirectionAlt } from "../../../../Enums/Directions/MoveDirection";
import { MoveGravity } from "./MoveGravity";
import { MovePath } from "./Path/MovePath";
import { MoveTrail } from "./MoveTrail";
import { OutModes } from "./OutModes";
import type { RangeValue } from "../../../../Types/RangeValue";
import type { RecursivePartial } from "../../../../Types/RecursivePartial";
import { Spin } from "./Spin";
export declare class Move implements IMove, IOptionLoader<IMove> {
    angle: MoveAngle;
    attract: MoveAttract;
    center: MoveCenter;
    decay: RangeValue;
    direction: MoveDirection | keyof typeof MoveDirection | MoveDirectionAlt | number;
    distance: Partial<IDistance>;
    drift: RangeValue;
    enable: boolean;
    gravity: MoveGravity;
    outModes: OutModes;
    path: MovePath;
    random: boolean;
    size: boolean;
    speed: RangeValue;
    spin: Spin;
    straight: boolean;
    trail: MoveTrail;
    vibrate: boolean;
    warp: boolean;
    constructor();
    get bounce(): boolean;
    set bounce(value: boolean);
    get collisions(): boolean;
    set collisions(_: boolean);
    get noise(): MovePath;
    set noise(value: MovePath);
    get outMode(): OutMode | keyof typeof OutMode | OutModeAlt;
    set outMode(value: OutMode | keyof typeof OutMode | OutModeAlt);
    get out_mode(): OutMode | keyof typeof OutMode | OutModeAlt;
    set out_mode(value: OutMode | keyof typeof OutMode | OutModeAlt);
    load(data?: RecursivePartial<IMove>): void;
}
