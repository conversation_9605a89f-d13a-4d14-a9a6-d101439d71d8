{"version": 3, "sources": ["../src/loadScript.ts"], "sourcesContent": ["const NO_DOCUMENT_ERROR = 'loadScript cannot be called when document does not exist';\nconst NO_SRC_ERROR = 'loadScript cannot be called without a src';\n\ntype LoadScriptOptions = {\n  async?: boolean;\n  defer?: boolean;\n  crossOrigin?: 'anonymous' | 'use-credentials';\n  beforeLoad?: (script: HTMLScriptElement) => void;\n};\n\nexport async function loadScript(src = '', opts: LoadScriptOptions): Promise<HTMLScriptElement> {\n  const { async, defer, beforeLoad, crossOrigin } = opts || {};\n  return new Promise((resolve, reject) => {\n    if (!src) {\n      reject(NO_SRC_ERROR);\n    }\n\n    if (!document || !document.body) {\n      reject(NO_DOCUMENT_ERROR);\n    }\n\n    const script = document.createElement('script');\n\n    crossOrigin && script.setAttribute('crossorigin', crossOrigin);\n    script.async = async || false;\n    script.defer = defer || false;\n\n    script.addEventListener('load', () => {\n      script.remove();\n      resolve(script);\n    });\n\n    script.addEventListener('error', () => {\n      script.remove();\n      reject();\n    });\n\n    script.src = src;\n    beforeLoad?.(script);\n    document.body.appendChild(script);\n  });\n}\n"], "mappings": ";AAAA,IAAM,oBAAoB;AAC1B,IAAM,eAAe;AASrB,eAAsB,WAAW,MAAM,IAAI,MAAqD;AAC9F,QAAM,EAAE,OAAO,OAAO,YAAY,YAAY,IAAI,QAAQ,CAAC;AAC3D,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,CAAC,KAAK;AACR,aAAO,YAAY;AAAA,IACrB;AAEA,QAAI,CAAC,YAAY,CAAC,SAAS,MAAM;AAC/B,aAAO,iBAAiB;AAAA,IAC1B;AAEA,UAAM,SAAS,SAAS,cAAc,QAAQ;AAE9C,mBAAe,OAAO,aAAa,eAAe,WAAW;AAC7D,WAAO,QAAQ,SAAS;AACxB,WAAO,QAAQ,SAAS;AAExB,WAAO,iBAAiB,QAAQ,MAAM;AACpC,aAAO,OAAO;AACd,cAAQ,MAAM;AAAA,IAChB,CAAC;AAED,WAAO,iBAAiB,SAAS,MAAM;AACrC,aAAO,OAAO;AACd,aAAO;AAAA,IACT,CAAC;AAED,WAAO,MAAM;AACb,6CAAa;AACb,aAAS,KAAK,YAAY,MAAM;AAAA,EAClC,CAAC;AACH;", "names": []}