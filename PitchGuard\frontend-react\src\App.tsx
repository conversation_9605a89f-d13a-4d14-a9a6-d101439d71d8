import React from 'react'
import { motion } from 'framer-motion'
import { Toaster } from 'react-hot-toast'
import ParticleBackground from './components/ParticleBackground'
import Header from './components/Header'
import PitchAnalyzer from './components/PitchAnalyzer'
import FloatingOrbs from './components/FloatingOrbs'
import CyberGrid from './components/CyberGrid'

function App() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Particle Background */}
      <ParticleBackground />
      
      {/* Floating Orbs */}
      <FloatingOrbs />
      
      {/* Cyber Grid */}
      <CyberGrid />
      
      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="relative z-10"
      >
        {/* Header */}
        <Header />
        
        {/* Main Application */}
        <main className="container mx-auto px-4 py-8 max-w-7xl">
          <PitchAnalyzer />
        </main>
        
        {/* Footer */}
        <motion.footer
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.8 }}
          className="text-center py-8 text-white/70"
        >
          <div className="glass-card inline-block px-6 py-3">
            <p className="text-sm">
              🔒 PitchGuard Lite - Secure pitch analysis with client-side encryption
            </p>
            <p className="text-xs mt-1 opacity-70">
              Powered by advanced AI • Built with React & Framer Motion
            </p>
          </div>
        </motion.footer>
      </motion.div>
      
      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            color: 'white',
          },
        }}
      />
    </div>
  )
}

export default App
