{"hash": "ab61075c", "browserHash": "f6075ea7", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "84203520", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "fadcd48b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "cf6e117b", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "7806ca84", "needsInterop": true}, "@clerk/clerk-react": {"src": "../../@clerk/clerk-react/dist/esm/index.js", "file": "@clerk_clerk-react.js", "fileHash": "a6e16060", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "74628786", "needsInterop": false}, "html2canvas": {"src": "../../html2canvas/dist/html2canvas.esm.js", "file": "html2canvas.js", "fileHash": "2b7de1e2", "needsInterop": false}, "jspdf": {"src": "../../jspdf/dist/jspdf.es.min.js", "file": "jspdf.js", "fileHash": "e0c1cc45", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "54674c8b", "needsInterop": false}, "react-confetti": {"src": "../../react-confetti/dist/react-confetti.mjs", "file": "react-confetti.js", "fileHash": "3686e28e", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "5c15382b", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "6411d5e8", "needsInterop": false}}, "chunks": {"purify.es-4DHREWT2": {"file": "purify__es-4DHREWT2.js"}, "index.es-WCC3BKB5": {"file": "index__es-WCC3BKB5.js"}, "chunk-D7ZASVPN": {"file": "chunk-D7ZASVPN.js"}, "chunk-G52XTN3B": {"file": "chunk-G52XTN3B.js"}, "chunk-VZBRM2AZ": {"file": "chunk-VZBRM2AZ.js"}, "chunk-LXGCQ6UQ": {"file": "chunk-LXGCQ6UQ.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}