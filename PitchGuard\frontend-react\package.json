{"name": "pitchguard-lite-react", "version": "1.0.0", "description": "Advanced React frontend for PitchGuard Lite with stunning animations", "private": true, "dependencies": {"@clerk/clerk-react": "^4.27.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@rollup/rollup-win32-x64-msvc": "^4.41.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.4", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-confetti": "^6.1.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-particles": "^2.12.2", "react-router-dom": "^6.18.0", "tailwind-merge": "^2.0.0", "tsparticles-slim": "^2.12.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}