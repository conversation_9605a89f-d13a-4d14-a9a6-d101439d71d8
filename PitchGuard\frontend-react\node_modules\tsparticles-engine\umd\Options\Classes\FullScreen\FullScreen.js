(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.FullScreen = void 0;
    class FullScreen {
        constructor() {
            this.enable = true;
            this.zIndex = 0;
        }
        load(data) {
            if (!data) {
                return;
            }
            if (data.enable !== undefined) {
                this.enable = data.enable;
            }
            if (data.zIndex !== undefined) {
                this.zIndex = data.zIndex;
            }
        }
    }
    exports.FullScreen = FullScreen;
});
