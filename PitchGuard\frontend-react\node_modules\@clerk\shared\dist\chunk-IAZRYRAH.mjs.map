{"version": 3, "sources": ["../src/keys.ts"], "sourcesContent": ["import type { PublishableKey } from '@clerk/types';\n\nimport { isomorphicAtob } from './isomorphicAtob';\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const keyPrefix = PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi)\n    ? PUBLISHABLE_KEY_TEST_PREFIX\n    : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${btoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(key: string | undefined): PublishableKey | null {\n  key = key || '';\n\n  if (!isPublishableKey(key)) {\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  if (!frontendApi.endsWith('$')) {\n    return null;\n  }\n\n  frontendApi = frontendApi.slice(0, -1);\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\nexport function isPublishableKey(key: string) {\n  key = key || '';\n\n  const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n  const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n  return hasValidPrefix && hasValidFrontendApiPostfix;\n}\n\nexport function isLegacyFrontendApiKey(key: string) {\n  key = key || '';\n\n  return key.startsWith('clerk.');\n}\n\nexport function createDevOrStagingUrlCache() {\n  // TODO: Check if we can merge it with `./instance.ts#isStaging()`\n  const DEV_OR_STAGING_SUFFIXES = [\n    '.lcl.dev',\n    '.stg.dev',\n    '.lclstage.dev',\n    '.stgstage.dev',\n    '.dev.lclclerk.com',\n    '.stg.lclclerk.com',\n    '.accounts.lclclerk.com',\n    'accountsstage.dev',\n    'accounts.dev',\n  ];\n\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromApiKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromApiKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n"], "mappings": ";;;;;AAIA,IAAM,8BAA8B;AACpC,IAAM,8BAA8B;AAGpC,IAAM,qCAAqC;AAEpC,SAAS,oBAAoB,aAA6B;AAC/D,QAAM,YAAY,mCAAmC,KAAK,WAAW,IACjE,8BACA;AACJ,SAAO,GAAG,SAAS,GAAG,KAAK,GAAG,WAAW,GAAG,CAAC;AAC/C;AAEO,SAAS,oBAAoB,KAAgD;AAClF,QAAM,OAAO;AAEb,MAAI,CAAC,iBAAiB,GAAG,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,eAAe,IAAI,WAAW,2BAA2B,IAAI,eAAe;AAElF,MAAI,cAAc,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AAElD,MAAI,CAAC,YAAY,SAAS,GAAG,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,gBAAc,YAAY,MAAM,GAAG,EAAE;AAErC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,iBAAiB,KAAa;AAC5C,QAAM,OAAO;AAEb,QAAM,iBAAiB,IAAI,WAAW,2BAA2B,KAAK,IAAI,WAAW,2BAA2B;AAEhH,QAAM,6BAA6B,eAAe,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,GAAG;AAEvF,SAAO,kBAAkB;AAC3B;AAEO,SAAS,uBAAuB,KAAa;AAClD,QAAM,OAAO;AAEb,SAAO,IAAI,WAAW,QAAQ;AAChC;AAEO,SAAS,6BAA6B;AAE3C,QAAM,0BAA0B;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,uBAAuB,oBAAI,IAAqB;AAEtD,SAAO;AAAA,IACL,mBAAmB,CAAC,QAA+B;AACjD,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACT;AAEA,YAAM,WAAW,OAAO,QAAQ,WAAW,MAAM,IAAI;AACrD,UAAI,MAAM,qBAAqB,IAAI,QAAQ;AAC3C,UAAI,QAAQ,QAAW;AACrB,cAAM,wBAAwB,KAAK,OAAK,SAAS,SAAS,CAAC,CAAC;AAC5D,6BAAqB,IAAI,UAAU,GAAG;AAAA,MACxC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEO,SAAS,wBAAwB,QAAyB;AAC/D,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;AAEO,SAAS,uBAAuB,QAAyB;AAC9D,SAAO,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AACnE;", "names": []}