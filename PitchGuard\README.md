# PitchGuard Lite

**Secure, AI-powered pitch scoring with client-side encryption**

PitchGuard Lite is a privacy-first web application that evaluates startup pitches using AI while ensuring complete confidentiality. Your pitch text is encrypted in your browser before transmission and never stored in plaintext on our servers.

## 🔒 Security Features

- **Client-side AES-GCM encryption** (256-bit) using Web Crypto API
- **Zero plaintext persistence** - pitches are decrypted only in memory
- **Cryptographic receipts** - SHA-256 hashes for score verification
- **No logging of sensitive data** - privacy by design

## 📊 Scoring Criteria

PitchGuard evaluates your pitch on four key dimensions:

1. **Narrative Clarity** - How well you communicate your story
2. **Originality** - Uniqueness and innovation of your solution  
3. **Team Strength** - Credibility and capability of your team
4. **Market Fit** - Alignment with market needs and opportunity

Each criterion is scored from 0.0 to 10.0 using advanced AI analysis.

## 🚀 Quick Start

### Prerequisites

- **OpenRouter API Key** - Get one free at [openrouter.ai](https://openrouter.ai)
- **Docker** (recommended) or Python 3.10+
- **Modern web browser** with Web Crypto API support

### Option 1: Docker (Recommended)

1. **<PERSON><PERSON> and navigate to the project:**
   ```bash
   git clone <repository-url>
   cd PitchGuard
   ```

2. **Set your OpenRouter API key:**
   ```bash
   export OPENROUTER_API_KEY="your_api_key_here"
   ```

3. **Build and run the backend:**
   ```bash
   cd backend
   docker build -t pitchguard-backend .
   docker run -p 8000:8000 -e OPENROUTER_API_KEY=$OPENROUTER_API_KEY pitchguard-backend
   ```

4. **Open the frontend:**
   ```bash
   cd ../frontend
   # Option A: Simple HTTP server
   python -m http.server 3000
   # Option B: Just open index.html in your browser
   open index.html
   ```

5. **Access the application:**
   - Frontend: http://localhost:3000 (or file:// if opened directly)
   - Backend API: http://localhost:8000

### Option 2: Local Python Development

1. **Set up the backend:**
   ```bash
   cd backend
   pip install -r requirements.txt
   export OPENROUTER_API_KEY="your_api_key_here"
   python app.py
   ```

2. **Open the frontend:**
   ```bash
   cd ../frontend
   open index.html  # or serve via HTTP server
   ```

## 🎯 Usage

1. **Enter your pitch** in the textarea (minimum 50 characters recommended)
2. **Click "Analyze Pitch"** - your text is encrypted locally before sending
3. **View your scores** displayed as animated progress bars
4. **Save your receipt hash** for verification purposes

### Example Pitch

```
Our startup, EcoClean, revolutionizes urban waste management through 
AI-powered sorting robots. We've identified that 60% of recyclable 
materials end up in landfills due to improper sorting. Our solution 
uses computer vision and machine learning to achieve 95% sorting 
accuracy, reducing waste processing costs by 40%. With a team of MIT 
engineers and partnerships with 3 major cities, we're seeking $2M 
to scale our pilot program nationwide.
```

## 🏗️ Architecture

### Frontend (Vanilla JavaScript)
- **Encryption**: AES-GCM with Web Crypto API
- **UI**: Tailwind CSS for responsive design
- **Security**: No plaintext storage, memory clearing

### Backend (FastAPI + Python)
- **Decryption**: Cryptography library for AES-GCM
- **AI Integration**: OpenRouter API with Mistral-7B model
- **Security**: Memory overwriting, no logging of sensitive data

### Data Flow
```
Browser → [Encrypt] → API → [Decrypt] → AI → [Score] → Receipt → Browser
```

## 🔐 Security Details

### Encryption Process
1. Generate random 256-bit AES key
2. Generate random 12-byte IV (initialization vector)
3. Encrypt pitch text using AES-GCM
4. Export key and encode all data as base64
5. Send encrypted payload to backend

### Receipt Generation
```
receipt_input = ciphertext + "|" + model_name + "|" + timestamp + "|" + scores
receipt_hash = SHA256(receipt_input)
```

This allows verification that scores haven't been tampered with.

## 🧪 Testing

### Manual Testing
1. Use the sample pitch provided above
2. Verify all four scores are returned (0.0-10.0 range)
3. Confirm receipt hash is generated
4. Test with various pitch lengths and content

### API Testing
```bash
# Health check
curl http://localhost:8000/health

# Test encryption (browser console)
PitchGuardUtils.generateSamplePayload()
```

## 📁 Project Structure

```
pitchguard-lite/
├── frontend/
│   ├���─ index.html          # Main UI
│   ├── main.js            # Encryption & API logic
│   └── styles.css         # Custom styling
├── backend/
│   ├── app.py             # FastAPI application
│   ├── requirements.txt   # Python dependencies
│   └── Dockerfile         # Container configuration
└── README.md              # This file
```

## 🔧 Configuration

### Environment Variables
- `OPENROUTER_API_KEY` - Required for AI scoring functionality

### Model Configuration
- **Default Model**: `mistralai/mistral-7b-instruct:free`
- **Temperature**: 0.0 (deterministic)
- **Max Tokens**: 200
- **Top P**: 1.0

## 🚨 Troubleshooting

### Common Issues

**"OpenRouter API key not configured"**
- Ensure `OPENROUTER_API_KEY` environment variable is set
- Verify the key is valid at openrouter.ai

**"Decryption failed"**
- Check browser compatibility (Web Crypto API required)
- Ensure frontend and backend are both running
- Verify CORS settings if accessing from different origins

**"AI scoring service unavailable"**
- Check internet connection
- Verify OpenRouter API key has sufficient credits
- Try again after a few minutes (rate limiting)

---

**Built with ❤️ for the startup community**